// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = "file:./dev.db"
}

model Task {
  id          String   @id @default(cuid())
  name        String   // 任务名称
  type        TaskType // 任务类型
  status      TaskStatus @default(PENDING)
  priority    Int      @default(0)
  
  // 任务配置
  config      String   // JSON字符串存储活动配置
  
  // 进度信息
  totalItems  Int      @default(0)
  processedItems Int   @default(0)
  failedItems Int      @default(0)
  
  // 文件信息
  inputFile   String?  // 输入文件路径
  outputFile  String?  // 输出文件路径
  
  // 时间戳
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  startedAt   DateTime?
  completedAt DateTime?
  
  // 关联数据
  products    Product[]
  logs        TaskLog[]
  
  @@map("tasks")
}

model Product {
  id        String   @id @default(cuid())
  productId String   // 商品ID
  taskId    String   // 关联任务
  status    ProductStatus @default(PENDING)
  errorMessage String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  task      Task     @relation(fields: [taskId], references: [id], onDelete: Cascade)

  @@map("products")
}

model TaskLog {
  id        String   @id @default(cuid())
  taskId    String
  level     LogLevel
  message   String
  details   String?  // JSON字符串存储详细信息
  
  createdAt DateTime @default(now())
  
  task      Task     @relation(fields: [taskId], references: [id], onDelete: Cascade)
  
  @@map("task_logs")
}

enum TaskType {
  SINGLE    // 单个商品
  BATCH     // 批量处理
}

enum TaskStatus {
  PENDING   // 等待中
  RUNNING   // 执行中
  COMPLETED // 已完成
  FAILED    // 失败
  CANCELLED // 已取消
}

enum ProductStatus {
  PENDING   // 等待处理
  PROCESSING // 处理中
  COMPLETED // 已完成
  FAILED    // 失败
}

enum LogLevel {
  INFO
  WARN
  ERROR
  DEBUG
}

// 商品信息管理相关模型
model ProductInfo {
  id          String   @id @default(cuid())
  productId   String   @unique // 商品ID，唯一标识
  title       String   // 商品标题
  basePrice   Decimal  // 商品定价
  limitPrice  Decimal? // 商品限价价格
  purchaseDiscount Decimal? // 商品进货折扣
  activityType ActivityType @default(DISCOUNT) // 活动形式
  activityStartTime DateTime? // 活动创建时间
  activityEndTime   DateTime? // 活动结束时间
  supplierId  String?  // 供应商ID

  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // 关联关系
  skus        ProductSku[]
  supplier    Supplier? @relation(fields: [supplierId], references: [id])
  templates   ProductTemplate[]

  @@map("product_info")
}

model ProductSku {
  id          String   @id @default(cuid())
  skuId       String   @unique // SKU ID，唯一标识
  productId   String   // 关联的商品ID
  skuName     String?  // SKU名称
  price       Decimal? // SKU价格
  stock       Int?     // 库存数量
  isActive    Boolean  @default(true) // 是否启用

  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // 关联关系
  product     ProductInfo @relation(fields: [productId], references: [productId], onDelete: Cascade)

  @@map("product_skus")
}

model Supplier {
  id          String   @id @default(cuid())
  name        String   // 供应商名称
  code        String   @unique // 供应商编码
  contact     String?  // 联系人
  phone       String?  // 联系电话
  email       String?  // 邮箱
  address     String?  // 地址
  isActive    Boolean  @default(true) // 是否启用

  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // 关联关系
  products    ProductInfo[]

  @@map("suppliers")
}

model ProductTemplate {
  id          String   @id @default(cuid())
  name        String   // 模板名称
  description String?  // 模板描述
  templateData String  // 模板数据（JSON格式）
  isDefault   Boolean  @default(false) // 是否为默认模板

  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // 关联关系
  products    ProductInfo[]

  @@map("product_templates")
}

enum ActivityType {
  DISCOUNT    // 打折
  DIRECT_REDUCTION // 直降
  INSTANT_REDUCTION // 立减
}