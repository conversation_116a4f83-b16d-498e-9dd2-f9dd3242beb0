import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function main() {
  console.log('开始创建种子数据...')

  // 创建测试任务
  const testTask = await prisma.task.create({
    data: {
      name: '测试批量任务',
      type: 'BATCH',
      status: 'COMPLETED',
      priority: 1,
      config: JSON.stringify({
        region: { type: 'national' },
        minQuantity: 1,
        discount: 8.0,
        timeMode: 'continuous',
        startDate: new Date('2025-08-01'),
        startTime: new Date('2025-08-01T09:00:00'),
        endTime: new Date('2025-08-01T18:00:00'),
        days: 7,
        weekendFullDay: true
      }),
      totalItems: 3,
      processedItems: 3,
      failedItems: 0,
      inputFile: 'uploads/input/test_products.xlsx',
      outputFile: 'uploads/output/test_batch_output.xlsx',
      startedAt: new Date('2025-08-04T10:00:00'),
      completedAt: new Date('2025-08-04T10:05:00'),
      products: {
        create: [
          {
            productId: 'TEST001',
            status: 'COMPLETED'
          },
          {
            productId: 'TEST002', 
            status: 'COMPLETED'
          },
          {
            productId: 'TEST003',
            status: 'COMPLETED'
          }
        ]
      },
      logs: {
        create: [
          {
            level: 'INFO',
            message: '任务开始执行',
            details: JSON.stringify({ timestamp: new Date() })
          },
          {
            level: 'INFO',
            message: '处理商品 TEST001',
            details: JSON.stringify({ productId: 'TEST001', progress: '33%' })
          },
          {
            level: 'INFO',
            message: '处理商品 TEST002',
            details: JSON.stringify({ productId: 'TEST002', progress: '66%' })
          },
          {
            level: 'INFO',
            message: '处理商品 TEST003',
            details: JSON.stringify({ productId: 'TEST003', progress: '100%' })
          },
          {
            level: 'INFO',
            message: '任务执行完成',
            details: JSON.stringify({ 
              totalItems: 3,
              processedItems: 3,
              failedItems: 0,
              duration: '5分钟'
            })
          }
        ]
      }
    }
  })

  // 创建进行中的任务
  const runningTask = await prisma.task.create({
    data: {
      name: '正在处理的任务',
      type: 'BATCH',
      status: 'RUNNING',
      priority: 0,
      config: JSON.stringify({
        region: { type: 'regional', regions: ['华东', '华南'] },
        minQuantity: 2,
        discount: 7.5,
        timeMode: 'random',
        randomStartTime: new Date('2025-08-05T14:00:00'),
        timeSlotCount: 5,
        slotDuration: 2
      }),
      totalItems: 5,
      processedItems: 2,
      failedItems: 0,
      inputFile: 'uploads/input/running_products.xlsx',
      startedAt: new Date(),
      products: {
        create: [
          {
            productId: 'RUN001',
            status: 'COMPLETED'
          },
          {
            productId: 'RUN002',
            status: 'COMPLETED'
          },
          {
            productId: 'RUN003',
            status: 'PROCESSING'
          },
          {
            productId: 'RUN004',
            status: 'PENDING'
          },
          {
            productId: 'RUN005',
            status: 'PENDING'
          }
        ]
      },
      logs: {
        create: [
          {
            level: 'INFO',
            message: '任务开始执行',
            details: JSON.stringify({ timestamp: new Date() })
          },
          {
            level: 'INFO',
            message: '处理商品 RUN001',
            details: JSON.stringify({ productId: 'RUN001', progress: '20%' })
          },
          {
            level: 'INFO',
            message: '处理商品 RUN002',
            details: JSON.stringify({ productId: 'RUN002', progress: '40%' })
          },
          {
            level: 'INFO',
            message: '正在处理商品 RUN003',
            details: JSON.stringify({ productId: 'RUN003', progress: '60%' })
          }
        ]
      }
    }
  })

  // 创建失败的任务
  const failedTask = await prisma.task.create({
    data: {
      name: '失败的任务',
      type: 'SINGLE',
      status: 'FAILED',
      priority: 0,
      config: JSON.stringify({
        region: { type: 'national' },
        minQuantity: 1,
        discount: 6.0,
        timeMode: 'continuous',
        startDate: new Date('2025-08-03'),
        startTime: new Date('2025-08-03T08:00:00'),
        endTime: new Date('2025-08-03T20:00:00'),
        days: 1,
        weekendFullDay: false
      }),
      totalItems: 1,
      processedItems: 0,
      failedItems: 1,
      inputFile: 'uploads/input/failed_product.xlsx',
      startedAt: new Date('2025-08-04T09:00:00'),
      products: {
        create: [
          {
            productId: 'FAIL001',
            status: 'FAILED',
            errorMessage: '商品ID格式不正确'
          }
        ]
      },
      logs: {
        create: [
          {
            level: 'INFO',
            message: '任务开始执行'
          },
          {
            level: 'ERROR',
            message: '商品ID验证失败',
            details: JSON.stringify({ 
              productId: 'FAIL001',
              error: '商品ID格式不正确',
              expectedFormat: '数字和字母组合'
            })
          },
          {
            level: 'ERROR',
            message: '任务执行失败',
            details: JSON.stringify({ 
              reason: '商品ID验证失败',
              failedItems: 1
            })
          }
        ]
      }
    }
  })

  console.log('种子数据创建完成!')
  console.log(`创建了 ${3} 个测试任务:`)
  console.log(`- 已完成任务: ${testTask.id}`)
  console.log(`- 进行中任务: ${runningTask.id}`)
  console.log(`- 失败任务: ${failedTask.id}`)
}

main()
  .then(async () => {
    await prisma.$disconnect()
  })
  .catch(async (e) => {
    console.error(e)
    await prisma.$disconnect()
    process.exit(1)
  })