# 测试目录结构说明

本项目采用分层测试架构，将不同类型的测试分别组织在不同的目录中。

> 📖 **完整测试文档**: 请参阅项目根目录的 [TESTING.md](../TESTING.md) 获取详细的测试规范和最佳实践。

## 目录结构

```
tests/
├── unit/           # 单元测试
│   ├── lib/        # 库函数测试
│   ├── components/ # 组件测试
│   ├── api/        # API路由测试
│   └── utils/      # 工具函数测试
├── integration/    # 集成测试
│   ├── api/        # API集成测试
│   ├── database/   # 数据库集成测试
│   └── services/   # 服务集成测试
├── e2e/           # 端到端测试
│   ├── pages/      # 页面功能测试
│   └── workflows/  # 完整工作流测试
├── fixtures/      # 测试数据和固定装置
│   ├── data/       # 测试数据文件
│   ├── templates/  # 测试模板文件
│   └── mocks/      # 模拟数据
├── utils/         # 测试工具函数
│   ├── helpers.ts  # 测试辅助函数
│   ├── setup.ts    # 测试环境设置
│   └── teardown.ts # 测试清理
└── mocks/         # 全局模拟对象
    ├── api.ts      # API模拟
    ├── database.ts # 数据库模拟
    └── external.ts # 外部服务模拟
```

## 测试类型说明

### 单元测试 (Unit Tests)
- **目的**: 测试单个函数、组件或模块的功能
- **特点**: 快速执行，隔离性强，不依赖外部服务
- **命名**: `*.test.ts` 或 `*.spec.ts`
- **运行**: `npm run test:unit`

### 集成测试 (Integration Tests)
- **目的**: 测试多个模块之间的交互
- **特点**: 可能涉及数据库、API调用等
- **命名**: `*.integration.test.ts`
- **运行**: `npm run test:integration`

### 端到端测试 (E2E Tests)
- **目的**: 测试完整的用户工作流
- **特点**: 模拟真实用户操作，测试整个应用
- **命名**: `*.e2e.test.ts`
- **运行**: `npm run test:e2e`

## 测试文件命名规范

1. **单元测试**: `[模块名].test.ts`
2. **集成测试**: `[功能名].integration.test.ts`
3. **端到端测试**: `[工作流名].e2e.test.ts`
4. **测试工具**: `[工具名].helper.ts`

## 测试数据管理

- **fixtures/data/**: 存放测试用的JSON、CSV等数据文件
- **fixtures/templates/**: 存放测试用的Excel模板文件
- **fixtures/mocks/**: 存放模拟响应数据

## 运行测试

```bash
# 运行所有测试
npm run test

# 运行单元测试
npm run test:unit

# 运行集成测试
npm run test:integration

# 运行端到端测试
npm run test:e2e

# 运行测试并生成覆盖率报告
npm run test:coverage

# 监视模式运行测试
npm run test:watch
```

## 最佳实践

1. **测试隔离**: 每个测试应该独立运行，不依赖其他测试的结果
2. **清晰命名**: 测试名称应该清楚描述测试的功能和预期结果
3. **数据清理**: 测试后应该清理创建的测试数据
4. **模拟外部依赖**: 使用mocks来模拟外部API、数据库等依赖
5. **覆盖率目标**: 保持代码覆盖率在70%以上
