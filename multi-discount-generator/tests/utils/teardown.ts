/**
 * 测试环境清理
 * 在测试运行后进行必要的清理工作
 */

import fs from 'fs';
import path from 'path';
import { cleanupTestDatabase, cleanupTempFiles } from './helpers';

/**
 * 清理测试数据库
 */
export async function teardownTestDatabase() {
  if (global.__TEST_PRISMA__) {
    await cleanupTestDatabase(global.__TEST_PRISMA__);
    await global.__TEST_PRISMA__.$disconnect();
    console.log('✅ 测试数据库清理完成');
  }
}

/**
 * 清理测试文件
 */
export async function teardownTestFiles() {
  // 清理临时文件
  cleanupTempFiles();
  
  // 清理测试上传文件
  const testUploadDir = path.join(process.cwd(), 'uploads', 'test');
  if (fs.existsSync(testUploadDir)) {
    fs.rmSync(testUploadDir, { recursive: true, force: true });
  }
  
  // 清理测试导出文件
  const testExportPattern = /^测试导出结果_\d+\.xlsx$/;
  const currentDir = process.cwd();
  const files = fs.readdirSync(currentDir);
  
  files.forEach(file => {
    if (testExportPattern.test(file)) {
      const filePath = path.join(currentDir, file);
      fs.unlinkSync(filePath);
    }
  });
  
  console.log('✅ 测试文件清理完成');
}

/**
 * 清理测试环境变量
 */
export function teardownTestEnvironment() {
  // 恢复原始环境变量
  delete process.env.TEST_DATABASE_URL;
  delete process.env.VERBOSE_TESTS;
  
  console.log('✅ 测试环境变量清理完成');
}

/**
 * 全局测试清理
 */
export async function globalTestTeardown() {
  await teardownTestDatabase();
  await teardownTestFiles();
  teardownTestEnvironment();
  
  console.log('🧹 全局测试清理完成');
}
