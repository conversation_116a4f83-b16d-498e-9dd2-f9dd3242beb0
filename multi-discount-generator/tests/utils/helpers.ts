/**
 * 测试辅助函数
 * 提供通用的测试工具和辅助方法
 */

import fs from 'fs';
import path from 'path';
import { PrismaClient } from '@prisma/client';

/**
 * 创建测试用的Prisma客户端
 */
export function createTestPrismaClient(): PrismaClient {
  return new PrismaClient({
    datasources: {
      db: {
        url: process.env.TEST_DATABASE_URL || 'file:./test.db'
      }
    }
  });
}

/**
 * 清理测试数据库
 */
export async function cleanupTestDatabase(prisma: PrismaClient) {
  // 按依赖关系顺序删除数据
  await prisma.batchTask.deleteMany();
  await prisma.product.deleteMany();
  await prisma.template.deleteMany();
}

/**
 * 创建测试用的临时文件
 */
export function createTempFile(content: string, extension: string = '.txt'): string {
  const tempDir = path.join(process.cwd(), 'temp');
  if (!fs.existsSync(tempDir)) {
    fs.mkdirSync(tempDir, { recursive: true });
  }
  
  const fileName = `test-${Date.now()}-${Math.random().toString(36).substr(2, 9)}${extension}`;
  const filePath = path.join(tempDir, fileName);
  
  fs.writeFileSync(filePath, content);
  return filePath;
}

/**
 * 清理临时文件
 */
export function cleanupTempFiles() {
  const tempDir = path.join(process.cwd(), 'temp');
  if (fs.existsSync(tempDir)) {
    fs.rmSync(tempDir, { recursive: true, force: true });
  }
}

/**
 * 等待指定时间
 */
export function sleep(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * 生成测试用的随机字符串
 */
export function generateRandomString(length: number = 10): string {
  return Math.random().toString(36).substring(2, 2 + length);
}

/**
 * 生成测试用的随机数字
 */
export function generateRandomNumber(min: number = 1, max: number = 1000): number {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

/**
 * 创建测试用的日期范围
 */
export function createTestDateRange(daysFromNow: number = 7): { start: Date; end: Date } {
  const start = new Date();
  const end = new Date();
  end.setDate(start.getDate() + daysFromNow);
  
  return { start, end };
}

/**
 * 验证对象是否包含指定的属性
 */
export function expectObjectToHaveProperties(obj: any, properties: string[]) {
  properties.forEach(prop => {
    expect(obj).toHaveProperty(prop);
  });
}

/**
 * 验证数组是否按指定字段排序
 */
export function expectArrayToBeSortedBy<T>(array: T[], field: keyof T, order: 'asc' | 'desc' = 'asc') {
  for (let i = 1; i < array.length; i++) {
    const current = array[i][field];
    const previous = array[i - 1][field];
    
    if (order === 'asc') {
      expect(current >= previous).toBe(true);
    } else {
      expect(current <= previous).toBe(true);
    }
  }
}

/**
 * 模拟API响应
 */
export function mockApiResponse<T>(data: T, status: number = 200) {
  return {
    ok: status >= 200 && status < 300,
    status,
    json: async () => data,
    text: async () => JSON.stringify(data)
  };
}

/**
 * 创建测试用的FormData
 */
export function createTestFormData(fields: Record<string, string | File>): FormData {
  const formData = new FormData();
  
  Object.entries(fields).forEach(([key, value]) => {
    formData.append(key, value);
  });
  
  return formData;
}

/**
 * 验证文件是否存在
 */
export function expectFileToExist(filePath: string) {
  expect(fs.existsSync(filePath)).toBe(true);
}

/**
 * 验证文件内容
 */
export function expectFileToContain(filePath: string, content: string) {
  expectFileToExist(filePath);
  const fileContent = fs.readFileSync(filePath, 'utf-8');
  expect(fileContent).toContain(content);
}
