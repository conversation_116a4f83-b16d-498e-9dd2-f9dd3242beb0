/**
 * 测试环境设置
 * 在测试运行前进行必要的初始化
 */

import { PrismaClient } from '@prisma/client';
import { createTestPrismaClient, cleanupTestDatabase } from './helpers';

// 全局测试变量
declare global {
  var __TEST_PRISMA__: PrismaClient;
}

/**
 * 设置测试环境
 */
export async function setupTestEnvironment() {
  // 设置环境变量
  process.env.NODE_ENV = 'test';
  process.env.NEXT_PUBLIC_API_URL = 'http://localhost:3001';
  
  // 创建测试数据库连接
  if (!global.__TEST_PRISMA__) {
    global.__TEST_PRISMA__ = createTestPrismaClient();
  }
  
  // 清理测试数据库
  await cleanupTestDatabase(global.__TEST_PRISMA__);
  
  console.log('✅ 测试环境设置完成');
}

/**
 * 设置测试数据库
 */
export async function setupTestDatabase() {
  const prisma = global.__TEST_PRISMA__;
  
  // 创建测试用的基础数据
  await prisma.product.createMany({
    data: [
      {
        id: 'test-product-1',
        name: '测试商品1',
        price: 100,
        category: '测试分类',
        status: 'active'
      },
      {
        id: 'test-product-2',
        name: '测试商品2',
        price: 200,
        category: '测试分类',
        status: 'active'
      }
    ]
  });
  
  console.log('✅ 测试数据库设置完成');
}

/**
 * 设置测试文件系统
 */
export async function setupTestFileSystem() {
  const fs = require('fs');
  const path = require('path');
  
  // 创建测试上传目录
  const uploadDir = path.join(process.cwd(), 'uploads', 'test');
  if (!fs.existsSync(uploadDir)) {
    fs.mkdirSync(uploadDir, { recursive: true });
  }
  
  // 创建测试模板目录
  const templateDir = path.join(process.cwd(), 'tests', 'fixtures', 'templates');
  if (!fs.existsSync(templateDir)) {
    fs.mkdirSync(templateDir, { recursive: true });
  }
  
  console.log('✅ 测试文件系统设置完成');
}

/**
 * 设置测试模拟
 */
export function setupTestMocks() {
  // 模拟console.log以减少测试输出噪音
  const originalConsoleLog = console.log;
  console.log = (...args: any[]) => {
    if (process.env.VERBOSE_TESTS === 'true') {
      originalConsoleLog(...args);
    }
  };
  
  // 模拟Date.now()以确保测试的确定性
  const originalDateNow = Date.now;
  Date.now = jest.fn(() => new Date('2024-01-01T00:00:00Z').getTime());
  
  // 在测试结束后恢复原始函数
  afterAll(() => {
    console.log = originalConsoleLog;
    Date.now = originalDateNow;
  });
  
  console.log('✅ 测试模拟设置完成');
}

/**
 * 全局测试设置
 */
export async function globalTestSetup() {
  await setupTestEnvironment();
  await setupTestDatabase();
  await setupTestFileSystem();
  setupTestMocks();
  
  console.log('🚀 全局测试设置完成');
}
