/**
 * 时间解析功能单元测试
 * 测试Excel时间小数转换和时间格式化功能
 */

describe('时间解析功能', () => {
  /**
   * Excel时间转换函数
   * Excel中时间存储为一天的小数部分，例如0.5表示12:00:00
   */
  function excelTimeToTimeString(value: number): string {
    const totalMinutes = Math.round(value * 24 * 60);
    const hours = Math.floor(totalMinutes / 60);
    const minutes = totalMinutes % 60;
    
    const hoursStr = String(hours).padStart(2, '0');
    const minutesStr = String(minutes).padStart(2, '0');
    return `${hoursStr}:${minutesStr}:00`;
  }

  describe('Excel时间小数转换', () => {
    const testCases = [
      { value: 0.791666666666667, expected: '19:00:00', description: '19:00' },
      { value: 0.333333333333333, expected: '08:00:00', description: '08:00' },
      { value: 0.5, expected: '12:00:00', description: '12:00 (正午)' },
      { value: 0.25, expected: '06:00:00', description: '06:00 (早晨)' },
      { value: 0.75, expected: '18:00:00', description: '18:00 (傍晚)' },
      { value: 0, expected: '00:00:00', description: '00:00 (午夜)' },
      { value: 0.999999, expected: '23:59:00', description: '23:59 (接近午夜)' }
    ];

    testCases.forEach(testCase => {
      it(`应该正确转换 ${testCase.description}`, () => {
        const result = excelTimeToTimeString(testCase.value);
        expect(result).toBe(testCase.expected);
      });
    });
  });

  describe('实际Excel值转换', () => {
    it('应该正确转换每日开始时间', () => {
      const value = 0.791666666666667; // Excel中的19:00
      const result = excelTimeToTimeString(value);
      expect(result).toBe('19:00:00');
    });

    it('应该正确转换每日结束时间', () => {
      const value = 0.333333333333333; // Excel中的08:00
      const result = excelTimeToTimeString(value);
      expect(result).toBe('08:00:00');
    });
  });

  describe('边界值测试', () => {
    it('应该处理最小值 (0)', () => {
      const result = excelTimeToTimeString(0);
      expect(result).toBe('00:00:00');
    });

    it('应该处理最大值 (接近1)', () => {
      const result = excelTimeToTimeString(0.9999);
      expect(result).toBe('23:59:00');
    });

    it('应该处理精确的半天值', () => {
      const result = excelTimeToTimeString(0.5);
      expect(result).toBe('12:00:00');
    });

    it('应该处理四分之一天值', () => {
      const result = excelTimeToTimeString(0.25);
      expect(result).toBe('06:00:00');
    });

    it('应该处理四分之三天值', () => {
      const result = excelTimeToTimeString(0.75);
      expect(result).toBe('18:00:00');
    });
  });

  describe('精度测试', () => {
    it('应该正确处理分钟级精度', () => {
      // 测试30分钟 = 0.5小时 = 0.5/24天
      const thirtyMinutes = 0.5 / 24;
      const result = excelTimeToTimeString(thirtyMinutes);
      expect(result).toBe('00:30:00');
    });

    it('应该正确处理15分钟精度', () => {
      // 测试15分钟 = 0.25小时 = 0.25/24天
      const fifteenMinutes = 0.25 / 24;
      const result = excelTimeToTimeString(fifteenMinutes);
      expect(result).toBe('00:15:00');
    });

    it('应该正确处理45分钟精度', () => {
      // 测试45分钟 = 0.75小时 = 0.75/24天
      const fortyFiveMinutes = 0.75 / 24;
      const result = excelTimeToTimeString(fortyFiveMinutes);
      expect(result).toBe('00:45:00');
    });
  });

  describe('错误处理', () => {
    it('应该处理负值', () => {
      const result = excelTimeToTimeString(-0.1);
      // 负值应该被处理为0或抛出错误
      expect(result).toBe('00:00:00');
    });

    it('应该处理大于1的值', () => {
      const result = excelTimeToTimeString(1.5);
      // 大于1的值应该取模或处理为最大时间
      expect(result).toBe('12:00:00'); // 1.5 - 1 = 0.5
    });

    it('应该处理NaN值', () => {
      const result = excelTimeToTimeString(NaN);
      expect(result).toBe('00:00:00');
    });

    it('应该处理Infinity值', () => {
      const result = excelTimeToTimeString(Infinity);
      expect(result).toBe('00:00:00');
    });
  });

  describe('常见时间点验证', () => {
    const commonTimes = [
      { time: '09:00:00', decimal: 9/24 },
      { time: '12:30:00', decimal: 12.5/24 },
      { time: '17:45:00', decimal: 17.75/24 },
      { time: '21:15:00', decimal: 21.25/24 },
      { time: '23:30:00', decimal: 23.5/24 }
    ];

    commonTimes.forEach(({ time, decimal }) => {
      it(`应该正确转换 ${time}`, () => {
        const result = excelTimeToTimeString(decimal);
        expect(result).toBe(time);
      });
    });
  });
});
