/**
 * 节假日时间处理逻辑单元测试
 * 测试TimeGenerator中的节假日处理功能
 */

import { TimeGenerator } from '../../../src/lib/time-generator';

describe('节假日时间处理逻辑', () => {
  describe('国庆节时间处理', () => {
    it('应该正确处理2025年国庆节期间的时间生成', () => {
      const testConfig = {
        startDate: new Date('2025-09-29'), // 周一
        startTime: new Date('2024-01-01 19:00:00'), // 19:00
        endTime: new Date('2024-01-01 08:00:00'),   // 08:00 (跨天)
        days: 5, // 测试5天：9/29, 9/30, 10/1, 10/2, 10/3
        weekendFullDay: true
      };

      const result = TimeGenerator.generateContinuousTimeSlots(testConfig);

      // 验证基本结果结构
      expect(result).toHaveProperty('slots');
      expect(result).toHaveProperty('warnings');
      expect(result).toHaveProperty('errors');
      expect(Array.isArray(result.slots)).toBe(true);
      expect(Array.isArray(result.warnings)).toBe(true);
      expect(Array.isArray(result.errors)).toBe(true);

      // 验证时间段数量
      expect(result.slots.length).toBeGreaterThan(0);
      
      // 验证每个时间段的结构
      result.slots.forEach(slot => {
        expect(slot).toHaveProperty('startTime');
        expect(slot).toHaveProperty('endTime');
        expect(new Date(slot.startTime)).toBeInstanceOf(Date);
        expect(new Date(slot.endTime)).toBeInstanceOf(Date);
      });
    });

    it('应该正确识别国庆节假日', () => {
      const testConfig = {
        startDate: new Date('2025-10-01'), // 国庆节当天
        startTime: new Date('2024-01-01 09:00:00'),
        endTime: new Date('2024-01-01 21:00:00'),
        days: 3, // 10/1, 10/2, 10/3
        weekendFullDay: true,
        holidayFullDay: true
      };

      const result = TimeGenerator.generateContinuousTimeSlots(testConfig);

      // 国庆节期间应该有特殊处理
      expect(result.slots.length).toBeGreaterThan(0);
      
      // 检查是否有节假日相关的警告或处理
      const hasHolidayHandling = result.warnings.some(warning => 
        warning.includes('节假日') || warning.includes('国庆')
      );
      
      // 如果有节假日处理逻辑，应该有相关提示
      if (result.warnings.length > 0) {
        expect(hasHolidayHandling).toBe(true);
      }
    });

    it('应该正确处理跨天时间段', () => {
      const testConfig = {
        startDate: new Date('2025-09-29'),
        startTime: new Date('2024-01-01 19:00:00'), // 19:00
        endTime: new Date('2024-01-01 08:00:00'),   // 08:00 (跨天)
        days: 2,
        weekendFullDay: true
      };

      const result = TimeGenerator.generateContinuousTimeSlots(testConfig);

      expect(result.slots.length).toBeGreaterThan(0);

      // 验证跨天时间段的正确性
      result.slots.forEach(slot => {
        const startTime = new Date(slot.startTime);
        const endTime = new Date(slot.endTime);
        
        // 跨天的情况下，结束时间应该在第二天
        if (startTime.getHours() > endTime.getHours()) {
          expect(endTime.getDate()).toBe(startTime.getDate() + 1);
        }
      });
    });
  });

  describe('周末处理', () => {
    it('应该正确处理周末全天模式', () => {
      const testConfig = {
        startDate: new Date('2025-09-27'), // 周六
        startTime: new Date('2024-01-01 09:00:00'),
        endTime: new Date('2024-01-01 21:00:00'),
        days: 2, // 周六和周日
        weekendFullDay: true
      };

      const result = TimeGenerator.generateContinuousTimeSlots(testConfig);

      expect(result.slots.length).toBeGreaterThan(0);
      
      // 验证周末时间段
      result.slots.forEach(slot => {
        const startTime = new Date(slot.startTime);
        const dayOfWeek = startTime.getDay();
        
        // 如果是周末（0=周日，6=周六）
        if (dayOfWeek === 0 || dayOfWeek === 6) {
          // 周末全天模式下应该有特殊处理
          expect(slot).toBeDefined();
        }
      });
    });

    it('应该正确处理周末非全天模式', () => {
      const testConfig = {
        startDate: new Date('2025-09-27'), // 周六
        startTime: new Date('2024-01-01 09:00:00'),
        endTime: new Date('2024-01-01 21:00:00'),
        days: 2, // 周六和周日
        weekendFullDay: false
      };

      const result = TimeGenerator.generateContinuousTimeSlots(testConfig);

      expect(result.slots.length).toBeGreaterThan(0);
    });
  });

  describe('错误处理', () => {
    it('应该处理无效的日期配置', () => {
      const testConfig = {
        startDate: new Date('invalid-date'),
        startTime: new Date('2024-01-01 09:00:00'),
        endTime: new Date('2024-01-01 21:00:00'),
        days: 1,
        weekendFullDay: true
      };

      const result = TimeGenerator.generateContinuousTimeSlots(testConfig);

      // 应该有错误信息
      expect(result.errors.length).toBeGreaterThan(0);
    });

    it('应该处理负数天数', () => {
      const testConfig = {
        startDate: new Date('2025-09-29'),
        startTime: new Date('2024-01-01 09:00:00'),
        endTime: new Date('2024-01-01 21:00:00'),
        days: -1,
        weekendFullDay: true
      };

      const result = TimeGenerator.generateContinuousTimeSlots(testConfig);

      // 应该有错误信息或者返回空结果
      expect(result.errors.length > 0 || result.slots.length === 0).toBe(true);
    });

    it('应该处理无效的时间配置', () => {
      const testConfig = {
        startDate: new Date('2025-09-29'),
        startTime: new Date('invalid-time'),
        endTime: new Date('2024-01-01 21:00:00'),
        days: 1,
        weekendFullDay: true
      };

      const result = TimeGenerator.generateContinuousTimeSlots(testConfig);

      // 应该有错误信息
      expect(result.errors.length).toBeGreaterThan(0);
    });
  });

  describe('边界情况', () => {
    it('应该处理单天配置', () => {
      const testConfig = {
        startDate: new Date('2025-09-29'),
        startTime: new Date('2024-01-01 09:00:00'),
        endTime: new Date('2024-01-01 21:00:00'),
        days: 1,
        weekendFullDay: true
      };

      const result = TimeGenerator.generateContinuousTimeSlots(testConfig);

      expect(result.slots.length).toBeGreaterThanOrEqual(1);
    });

    it('应该处理零天配置', () => {
      const testConfig = {
        startDate: new Date('2025-09-29'),
        startTime: new Date('2024-01-01 09:00:00'),
        endTime: new Date('2024-01-01 21:00:00'),
        days: 0,
        weekendFullDay: true
      };

      const result = TimeGenerator.generateContinuousTimeSlots(testConfig);

      expect(result.slots.length).toBe(0);
    });
  });
});
