/**
 * 任务队列API集成测试
 * 测试批量任务生成、状态查询和结果获取的完整工作流
 */

import { sleep } from '../../utils/helpers';

const BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';

// 测试模板数据
const testTemplateData = [
  {
    '商品ID': 'TASK001',
    '商品标题': '测试商品1',
    '活动区域': '全国',
    '满几件': '1',
    '折扣': '8'
  },
  {
    '商品ID': 'TASK002', 
    '商品标题': '测试商品2',
    '活动区域': '华东',
    '满几件': '1',
    '折扣': '7'
  },
  {
    '商品ID': 'TASK003',
    '商品标题': '测试商品3', 
    '活动区域': '华南',
    '满几件': '1',
    '折扣': '9'
  }
];

describe('任务队列API集成测试', () => {
  let taskId: string;

  beforeAll(async () => {
    // 确保测试服务器运行
    // 这里可以添加服务器启动逻辑
  });

  describe('任务生成', () => {
    it('应该成功创建批量生成任务', async () => {
      const generateResponse = await fetch(`${BASE_URL}/api/batch/generate-tasks`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          templateData: testTemplateData,
          configs: {
            timeMode: 'continuous',
            startDate: '2024-01-15',
            endDate: '2024-01-21',
            dailyStartTime: '09:00:00',
            dailyEndTime: '21:00:00',
            weekendFullDay: true,
            holidayFullDay: true
          }
        })
      });

      expect(generateResponse.ok).toBe(true);

      const generateResult = await generateResponse.json();
      
      expect(generateResult.success).toBe(true);
      expect(generateResult.data).toBeDefined();
      expect(generateResult.data.taskId).toBeDefined();
      expect(generateResult.data.status).toBe('pending');
      expect(generateResult.data.total).toBe(testTemplateData.length);

      // 保存任务ID供后续测试使用
      taskId = generateResult.data.taskId;
    });

    it('应该拒绝无效的任务配置', async () => {
      const invalidResponse = await fetch(`${BASE_URL}/api/batch/generate-tasks`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          templateData: [], // 空数据
          configs: {
            timeMode: 'invalid-mode' // 无效模式
          }
        })
      });

      expect(invalidResponse.ok).toBe(false);
      const result = await invalidResponse.json();
      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
    });
  });

  describe('任务状态查询', () => {
    beforeEach(() => {
      if (!taskId) {
        throw new Error('需要先运行任务生成测试');
      }
    });

    it('应该能够查询任务状态', async () => {
      const statusResponse = await fetch(`${BASE_URL}/api/tasks/${taskId}/status`);
      
      expect(statusResponse.ok).toBe(true);

      const statusResult = await statusResponse.json();
      
      expect(statusResult.success).toBe(true);
      expect(statusResult.data).toBeDefined();
      expect(statusResult.data.taskId).toBe(taskId);
      expect(statusResult.data.status).toBeDefined();
      expect(['pending', 'processing', 'completed', 'failed']).toContain(statusResult.data.status);
      expect(typeof statusResult.data.progress).toBe('number');
      expect(statusResult.data.progress).toBeGreaterThanOrEqual(0);
      expect(statusResult.data.progress).toBeLessThanOrEqual(100);
    });

    it('应该返回404对于不存在的任务', async () => {
      const nonExistentTaskId = 'non-existent-task-id';
      const statusResponse = await fetch(`${BASE_URL}/api/tasks/${nonExistentTaskId}/status`);
      
      expect(statusResponse.status).toBe(404);
    });
  });

  describe('任务进度监控', () => {
    beforeEach(() => {
      if (!taskId) {
        throw new Error('需要先运行任务生成测试');
      }
    });

    it('应该能够监控任务进度直到完成', async () => {
      let attempts = 0;
      const maxAttempts = 30; // 最多等待30次，每次1秒
      let finalStatus = 'pending';

      while (attempts < maxAttempts) {
        const statusResponse = await fetch(`${BASE_URL}/api/tasks/${taskId}/status`);
        const statusResult = await statusResponse.json();

        expect(statusResponse.ok).toBe(true);
        expect(statusResult.success).toBe(true);

        finalStatus = statusResult.data.status;
        const progress = statusResult.data.progress;

        // 验证进度值的合理性
        expect(progress).toBeGreaterThanOrEqual(0);
        expect(progress).toBeLessThanOrEqual(100);

        // 如果任务完成或失败，退出循环
        if (finalStatus === 'completed' || finalStatus === 'failed') {
          break;
        }

        // 等待1秒后再次检查
        await sleep(1000);
        attempts++;
      }

      // 验证任务最终状态
      expect(['completed', 'failed']).toContain(finalStatus);
      
      // 如果任务失败，记录失败原因
      if (finalStatus === 'failed') {
        const statusResponse = await fetch(`${BASE_URL}/api/tasks/${taskId}/status`);
        const statusResult = await statusResponse.json();
        console.warn('任务失败:', statusResult.data.error);
      }
    }, 35000); // 增加超时时间到35秒
  });

  describe('任务结果获取', () => {
    beforeEach(() => {
      if (!taskId) {
        throw new Error('需要先运行任务生成测试');
      }
    });

    it('应该能够获取已完成任务的结果', async () => {
      // 首先确保任务已完成
      let taskCompleted = false;
      let attempts = 0;
      const maxAttempts = 30;

      while (!taskCompleted && attempts < maxAttempts) {
        const statusResponse = await fetch(`${BASE_URL}/api/tasks/${taskId}/status`);
        const statusResult = await statusResponse.json();

        if (statusResult.data.status === 'completed') {
          taskCompleted = true;
          break;
        } else if (statusResult.data.status === 'failed') {
          throw new Error('任务执行失败，无法获取结果');
        }

        await sleep(1000);
        attempts++;
      }

      if (!taskCompleted) {
        throw new Error('任务未在预期时间内完成');
      }

      // 获取任务结果
      const resultResponse = await fetch(`${BASE_URL}/api/tasks/${taskId}/result`);
      
      expect(resultResponse.ok).toBe(true);

      const resultData = await resultResponse.json();
      
      expect(resultData.success).toBe(true);
      expect(resultData.data).toBeDefined();
      expect(Array.isArray(resultData.data.results)).toBe(true);
      expect(resultData.data.results.length).toBe(testTemplateData.length);

      // 验证每个结果的结构
      resultData.data.results.forEach((result: any) => {
        expect(result).toHaveProperty('productId');
        expect(result).toHaveProperty('timeSlots');
        expect(Array.isArray(result.timeSlots)).toBe(true);
      });
    }, 35000);

    it('应该返回404对于不存在任务的结果', async () => {
      const nonExistentTaskId = 'non-existent-task-id';
      const resultResponse = await fetch(`${BASE_URL}/api/tasks/${nonExistentTaskId}/result`);
      
      expect(resultResponse.status).toBe(404);
    });
  });

  describe('任务列表查询', () => {
    it('应该能够获取任务列表', async () => {
      const listResponse = await fetch(`${BASE_URL}/api/tasks`);
      
      expect(listResponse.ok).toBe(true);

      const listResult = await listResponse.json();
      
      expect(listResult.success).toBe(true);
      expect(listResult.data).toBeDefined();
      expect(Array.isArray(listResult.data.tasks)).toBe(true);
      expect(typeof listResult.data.total).toBe('number');
      expect(typeof listResult.data.page).toBe('number');
      expect(typeof listResult.data.pageSize).toBe('number');

      // 验证任务列表中包含我们创建的任务
      if (taskId) {
        const ourTask = listResult.data.tasks.find((task: any) => task.id === taskId);
        expect(ourTask).toBeDefined();
      }
    });

    it('应该支持分页查询', async () => {
      const pageResponse = await fetch(`${BASE_URL}/api/tasks?page=1&pageSize=5`);
      
      expect(pageResponse.ok).toBe(true);

      const pageResult = await pageResponse.json();
      
      expect(pageResult.success).toBe(true);
      expect(pageResult.data.tasks.length).toBeLessThanOrEqual(5);
      expect(pageResult.data.page).toBe(1);
      expect(pageResult.data.pageSize).toBe(5);
    });
  });
});
