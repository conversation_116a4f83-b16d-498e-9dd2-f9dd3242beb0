// 测试完整的解析修复（模拟API逻辑）
const XLSX = require('xlsx');
const path = require('path');

function testCompleteParsing() {
  console.log('🔍 测试完整的解析修复...');
  
  try {
    // 读取测试模板
    const templatePath = path.join(__dirname, '正确格式批量生成模板.xlsx');
    const workbook = XLSX.readFile(templatePath);
    const sheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];
    
    // 转换为JSON数据，使用默认设置让XLSX自动处理日期
    const jsonData = XLSX.utils.sheet_to_json(worksheet);
    
    console.log('📋 原始JSON数据:');
    jsonData.forEach((row, index) => {
      console.log(`第${index + 1}行:`, row);
    });
    
    console.log('');
    console.log('🔧 应用修复逻辑...');
    
    // 处理日期字段的转换（与修复后的API一致）
    const parsedData = jsonData.map((row, index) => {
      const processedRow = { ...row }
      
      console.log(`处理第${index + 1}行...`);
      
      // 处理日期字段
      const dateFields = ['开始日期', '结束日期']
      dateFields.forEach(field => {
        if (processedRow[field] !== undefined && processedRow[field] !== '') {
          const value = processedRow[field]
          
          // 如果是数字（Excel日期序列号），转换为日期
          if (typeof value === 'number') {
            console.log(`  ${field}: ${value} (数字) -> 转换中...`);
            // Excel日期从1900年1月1日开始计算（但有1900年闰年bug，实际从1899年12月30日开始）
            const excelEpoch = new Date(1899, 11, 30) // 1899年12月30日
            const jsDate = new Date(excelEpoch.getTime() + value * 24 * 60 * 60 * 1000)
            
            if (!isNaN(jsDate.getTime())) {
              const year = jsDate.getFullYear()
              const month = String(jsDate.getMonth() + 1).padStart(2, '0')
              const day = String(jsDate.getDate()).padStart(2, '0')
              processedRow[field] = `${year}-${month}-${day}`
              console.log(`  ${field}: 转换结果 -> ${processedRow[field]}`);
            }
          }
          // 如果是字符串日期，尝试标准化格式
          else if (typeof value === 'string') {
            console.log(`  ${field}: ${value} (字符串) -> 解析中...`);
            try {
              const date = new Date(value)
              if (!isNaN(date.getTime())) {
                const year = date.getFullYear()
                const month = String(date.getMonth() + 1).padStart(2, '0')
                const day = String(date.getDate()).padStart(2, '0')
                processedRow[field] = `${year}-${month}-${day}`
                console.log(`  ${field}: 解析结果 -> ${processedRow[field]}`);
              }
            } catch (error) {
              console.warn(`  ${field}: 解析失败 ->`, error.message);
            }
          }
        }
      })
      
      // 处理时间字段
      const timeFields = ['每日开始时间', '每日结束时间']
      timeFields.forEach(field => {
        if (processedRow[field] !== undefined && processedRow[field] !== '') {
          const value = processedRow[field]
          
          // 如果是数字（Excel时间小数），转换为时间
          if (typeof value === 'number') {
            console.log(`  ${field}: ${value} (数字) -> 转换中...`);
            // Excel时间是一天的小数部分，0.5表示12:00:00
            const totalMinutes = Math.round(value * 24 * 60)
            let hours = Math.floor(totalMinutes / 60)
            let minutes = totalMinutes % 60
            
            // 处理边界情况
            if (hours >= 24) {
              hours = 23
              minutes = 59
            }
            
            const hoursStr = String(hours).padStart(2, '0')
            const minutesStr = String(minutes).padStart(2, '0')
            processedRow[field] = `${hoursStr}:${minutesStr}:00`
            console.log(`  ${field}: 转换结果 -> ${processedRow[field]}`);
          }
          // 如果是字符串时间，尝试标准化格式
          else if (typeof value === 'string') {
            console.log(`  ${field}: ${value} (字符串) -> 标准化中...`);
            // 如果已经是HH:MM:SS格式，保持不变
            if (/^\d{2}:\d{2}:\d{2}$/.test(value)) {
              processedRow[field] = value
            }
            // 如果是HH:MM格式，添加秒
            else if (/^\d{1,2}:\d{2}$/.test(value)) {
              const [hours, minutes] = value.split(':')
              const hoursStr = String(parseInt(hours)).padStart(2, '0')
              const minutesStr = String(parseInt(minutes)).padStart(2, '0')
              processedRow[field] = `${hoursStr}:${minutesStr}:00`
            }
            console.log(`  ${field}: 标准化结果 -> ${processedRow[field]}`);
          }
        }
      })
      
      return processedRow
    }).filter(row => {
      // 过滤空行
      return Object.values(row).some(value => value && value.toString().trim() !== '')
    })
    
    console.log('');
    console.log('🎯 最终处理结果:');
    parsedData.forEach((row, index) => {
      console.log(`第${index + 1}行处理结果:`);
      console.log('  商品ID:', row['商品ID']);
      console.log('  活动区域:', row['活动区域']);
      console.log('  满几件:', row['满几件']);
      console.log('  折扣:', row['折扣']);
      console.log('  时间模式:', row['时间模式']);
      console.log('  开始日期:', row['开始日期']);
      console.log('  结束日期:', row['结束日期']);
      console.log('  每日开始时间:', row['每日开始时间']);
      console.log('  每日结束时间:', row['每日结束时间']);
      console.log('  周末全天:', row['周末全天']);
      console.log('  节假日全天:', row['节假日全天']);
      console.log('---');
    });
    
    console.log('✅ 解析修复测试完成！');
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
  }
}

// 运行测试
testCompleteParsing();
