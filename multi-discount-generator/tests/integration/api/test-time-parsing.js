// 测试时间解析修复
function testTimeConversion() {
  console.log('🔍 测试时间转换...');
  
  // 测试Excel时间小数转换
  const testCases = [
    { value: 0.791666666666667, expected: '19:00:00' },
    { value: 0.333333333333333, expected: '08:00:00' },
    { value: 0.5, expected: '12:00:00' },
    { value: 0.25, expected: '06:00:00' },
    { value: 0.75, expected: '18:00:00' },
    { value: 0, expected: '00:00:00' },
    { value: 0.999999, expected: '23:59:00' }
  ];
  
  testCases.forEach(testCase => {
    const value = testCase.value;
    
    // Excel时间是一天的小数部分，0.5表示12:00:00
    const totalMinutes = Math.round(value * 24 * 60);
    const hours = Math.floor(totalMinutes / 60);
    const minutes = totalMinutes % 60;
    
    const hoursStr = String(hours).padStart(2, '0');
    const minutesStr = String(minutes).padStart(2, '0');
    const result = `${hoursStr}:${minutesStr}:00`;
    
    console.log(`${value} -> ${result} (期望: ${testCase.expected}) ${result === testCase.expected ? '✅' : '❌'}`);
  });
  
  console.log('');
  console.log('🔍 测试具体的Excel值:');
  
  // 测试实际的Excel值
  const actualValues = [
    { field: '每日开始时间', value: 0.791666666666667 },
    { field: '每日结束时间', value: 0.333333333333333 }
  ];
  
  actualValues.forEach(item => {
    const value = item.value;
    const totalMinutes = Math.round(value * 24 * 60);
    const hours = Math.floor(totalMinutes / 60);
    const minutes = totalMinutes % 60;
    
    const hoursStr = String(hours).padStart(2, '0');
    const minutesStr = String(minutes).padStart(2, '0');
    const result = `${hoursStr}:${minutesStr}:00`;
    
    console.log(`${item.field}: ${value} -> ${result}`);
  });
}

// 运行测试
testTimeConversion();
