// 测试节假日时间处理逻辑
const { TimeGenerator } = require('./src/lib/time-generator.ts');

async function testHolidayLogic() {
  console.log('🔍 测试节假日时间处理逻辑...');
  
  try {
    // 测试2025年国庆节前后的时间处理
    const testConfig = {
      startDate: new Date('2025-09-29'), // 周一
      startTime: new Date('2024-01-01 19:00:00'), // 19:00
      endTime: new Date('2024-01-01 08:00:00'),   // 08:00 (跨天)
      days: 5, // 测试5天：9/29, 9/30, 10/1, 10/2, 10/3
      weekendFullDay: true
    };
    
    console.log('📋 测试配置:');
    console.log('  开始日期: 2025-09-29 (周一)');
    console.log('  结束日期: 2025-10-03 (周五)');
    console.log('  每日时间: 19:00:00 - 08:00:00 (跨天)');
    console.log('  节假日: 10/1-10/3 是国庆节');
    console.log('');
    
    const result = TimeGenerator.generateContinuousTimeSlots(testConfig);
    
    console.log('🎯 生成结果:');
    console.log(`  时间段数量: ${result.slots.length}`);
    console.log(`  警告数量: ${result.warnings.length}`);
    console.log(`  错误数量: ${result.errors.length}`);
    console.log('');
    
    if (result.warnings.length > 0) {
      console.log('⚠️ 警告信息:');
      result.warnings.forEach((warning, index) => {
        console.log(`  ${index + 1}. ${warning}`);
      });
      console.log('');
    }
    
    if (result.errors.length > 0) {
      console.log('❌ 错误信息:');
      result.errors.forEach((error, index) => {
        console.log(`  ${index + 1}. ${error}`);
      });
      console.log('');
    }
    
    console.log('📊 详细时间段:');
    result.slots.forEach((slot, index) => {
      const startTime = new Date(slot.startTime).toLocaleString('zh-CN', {
        timeZone: 'Asia/Shanghai',
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });
      
      const endTime = new Date(slot.endTime).toLocaleString('zh-CN', {
        timeZone: 'Asia/Shanghai',
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });
      
      const type = slot.type || 'unknown';
      const isHoliday = slot.isHoliday ? '🎉' : '';
      const holidayName = slot.holidayName || '';
      
      console.log(`  ${index + 1}. ${startTime} - ${endTime} [${type}] ${isHoliday} ${holidayName}`);
    });
    
    console.log('');
    console.log('🔍 关键检查点:');
    
    // 检查9/29 19:00 - 9/30 08:00
    const slot1 = result.slots[0];
    if (slot1) {
      const start1 = new Date(slot1.startTime);
      const end1 = new Date(slot1.endTime);
      console.log(`  1. 9/29时间段: ${start1.getDate()}日${start1.getHours()}:${start1.getMinutes().toString().padStart(2,'0')} - ${end1.getDate()}日${end1.getHours()}:${end1.getMinutes().toString().padStart(2,'0')}`);
    }
    
    // 检查9/30的处理
    const slot2 = result.slots[1];
    if (slot2) {
      const start2 = new Date(slot2.startTime);
      const end2 = new Date(slot2.endTime);
      console.log(`  2. 9/30时间段: ${start2.getDate()}日${start2.getHours()}:${start2.getMinutes().toString().padStart(2,'0')} - ${end2.getDate()}日${end2.getHours()}:${end2.getMinutes().toString().padStart(2,'0')}`);
      
      // 检查是否正确截断到23:59:59
      if (end2.getHours() === 23 && end2.getMinutes() === 59) {
        console.log('  ✅ 9/30正确截断到23:59:59');
      } else {
        console.log('  ❌ 9/30没有正确截断');
      }
    }
    
    // 检查10/1的处理
    const slot3 = result.slots[2];
    if (slot3) {
      const start3 = new Date(slot3.startTime);
      const end3 = new Date(slot3.endTime);
      console.log(`  3. 10/1时间段: ${start3.getDate()}日${start3.getHours()}:${start3.getMinutes().toString().padStart(2,'0')} - ${end3.getDate()}日${end3.getHours()}:${end3.getMinutes().toString().padStart(2,'0')}`);
      
      // 检查是否从00:00:00开始
      if (start3.getHours() === 0 && start3.getMinutes() === 0) {
        console.log('  ✅ 10/1正确从00:00:00开始');
      } else {
        console.log('  ❌ 10/1没有从00:00:00开始');
      }
      
      // 检查是否到23:59:59结束
      if (end3.getHours() === 23 && end3.getMinutes() === 59) {
        console.log('  ✅ 10/1正确到23:59:59结束');
      } else {
        console.log('  ❌ 10/1没有正确到23:59:59结束');
      }
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
  }
}

// 运行测试
testHolidayLogic();
