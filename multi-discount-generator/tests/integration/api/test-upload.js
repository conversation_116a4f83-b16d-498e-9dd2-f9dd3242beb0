// 测试模板上传功能
const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));
const FormData = require('form-data');
const fs = require('fs');
const path = require('path');

async function testUpload() {
  console.log('🧪 测试模板上传功能...');
  
  try {
    // 检查测试文件是否存在
    const testFile = path.join(__dirname, '正确格式批量生成模板.xlsx');
    if (!fs.existsSync(testFile)) {
      console.error('❌ 测试文件不存在:', testFile);
      return;
    }
    
    console.log('📁 测试文件:', testFile);
    console.log('📊 文件大小:', (fs.statSync(testFile).size / 1024).toFixed(2), 'KB');
    
    // 创建FormData
    const formData = new FormData();
    formData.append('file', fs.createReadStream(testFile));
    
    console.log('📤 开始上传...');
    
    // 发送上传请求
    const response = await fetch('http://localhost:3001/api/templates/upload', {
      method: 'POST',
      body: formData
    });
    
    console.log('📥 响应状态:', response.status);
    console.log('📥 响应头:', Object.fromEntries(response.headers.entries()));
    
    const result = await response.json();
    
    console.log('📋 响应结果:');
    console.log(JSON.stringify(result, null, 2));
    
    if (result.success) {
      console.log('✅ 上传成功');
      console.log(`   - 文件名: ${result.data.originalName}`);
      console.log(`   - 解析行数: ${result.data.summary.totalRows}`);
      console.log(`   - 有效商品: ${result.data.summary.validProducts}`);
      console.log(`   - 列数: ${result.data.columns.length}`);
      console.log(`   - 列名: ${result.data.columns.join(', ')}`);
      
      if (result.data.templateData && result.data.templateData.length > 0) {
        console.log('📊 第一行数据示例:');
        console.log(JSON.stringify(result.data.templateData[0], null, 2));
      }
      
      if (result.data.errors && result.data.errors.length > 0) {
        console.log('⚠️ 解析警告:');
        result.data.errors.forEach(error => console.log(`   - ${error}`));
      }
    } else {
      console.error('❌ 上传失败:', result.error);
      if (result.details) {
        console.error('详细信息:', result.details);
      }
    }
    
  } catch (error) {
    console.error('💥 测试失败:', error.message);
    if (error.code) {
      console.error('错误代码:', error.code);
    }
  }
}

// 运行测试
testUpload();
