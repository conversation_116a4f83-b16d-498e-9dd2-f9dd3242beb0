/**
 * 批量导出API集成测试
 * 测试模板上传和批量导出的完整工作流
 */

import fs from 'fs';
import path from 'path';
import FormData from 'form-data';
import { createTestFormData, expectFileToExist, cleanupTempFiles } from '../../utils/helpers';

const BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';

describe('批量导出API集成测试', () => {
  let templateData: any;
  let exportedFileName: string;

  beforeAll(async () => {
    // 确保测试服务器运行
    // 这里可以添加服务器启动逻辑
  });

  afterAll(async () => {
    // 清理测试文件
    cleanupTempFiles();
    if (exportedFileName && fs.existsSync(exportedFileName)) {
      fs.unlinkSync(exportedFileName);
    }
  });

  describe('模板上传功能', () => {
    it('应该成功上传Excel模板文件', async () => {
      // 检查测试文件是否存在
      const templateFile = path.join(process.cwd(), '完整测试批量时间生成模板.xlsx');
      expectFileToExist(templateFile);

      // 创建FormData
      const formData = new FormData();
      formData.append('file', fs.createReadStream(templateFile));

      // 上传模板
      const response = await fetch(`${BASE_URL}/api/templates/upload`, {
        method: 'POST',
        body: formData
      });

      expect(response.ok).toBe(true);

      const result = await response.json();
      
      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data.products).toBeDefined();
      expect(result.data.originalName).toBeDefined();
      expect(result.data.configs).toBeDefined();
      expect(Array.isArray(result.data.products)).toBe(true);
      expect(result.data.products.length).toBeGreaterThan(0);

      // 保存模板数据供后续测试使用
      templateData = result.data;
    });

    it('应该拒绝无效的文件格式', async () => {
      const invalidFile = Buffer.from('invalid content');
      const formData = new FormData();
      formData.append('file', invalidFile, 'invalid.txt');

      const response = await fetch(`${BASE_URL}/api/templates/upload`, {
        method: 'POST',
        body: formData
      });

      const result = await response.json();
      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
    });
  });

  describe('批量导出功能', () => {
    beforeEach(() => {
      // 确保有有效的模板数据
      if (!templateData) {
        throw new Error('需要先运行模板上传测试');
      }
    });

    it('应该成功导出批量活动配置', async () => {
      // 准备导出配置
      const exportConfig = {
        productIds: templateData.products.slice(0, 5), // 只测试前5个商品
        configs: {
          activityType: '多件多折',
          timeMode: '连续',
          startDate: '2024-01-15',
          endDate: '2024-01-21',
          dailyStartTime: '09:00:00',
          dailyEndTime: '21:00:00',
          region: '全国',
          minQuantity: 2,
          discount: 8,
          weekendFullDay: true,
          holidayFullDay: true
        }
      };

      // 调用批量导出API
      const response = await fetch(`${BASE_URL}/api/batch/export`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(exportConfig)
      });

      expect(response.ok).toBe(true);

      // 验证响应是Excel文件
      const contentType = response.headers.get('content-type');
      expect(contentType).toContain('application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');

      // 保存并验证导出的文件
      const buffer = await response.arrayBuffer();
      exportedFileName = `测试导出结果_${Date.now()}.xlsx`;
      fs.writeFileSync(exportedFileName, Buffer.from(buffer));

      expectFileToExist(exportedFileName);
      expect(buffer.byteLength).toBeGreaterThan(0);
    });

    it('应该拒绝无效的导出配置', async () => {
      const invalidConfig = {
        productIds: [], // 空的商品ID列表
        configs: {
          activityType: '多件多折'
          // 缺少必要的配置项
        }
      };

      const response = await fetch(`${BASE_URL}/api/batch/export`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(invalidConfig)
      });

      expect(response.ok).toBe(false);
      const result = await response.json();
      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
    });

    it('应该正确处理大量商品的导出', async () => {
      const largeExportConfig = {
        productIds: templateData.products, // 使用所有商品
        configs: {
          activityType: '多件多折',
          timeMode: '连续',
          startDate: '2024-01-15',
          endDate: '2024-01-21',
          dailyStartTime: '09:00:00',
          dailyEndTime: '21:00:00',
          region: '全国',
          minQuantity: 2,
          discount: 8,
          weekendFullDay: true,
          holidayFullDay: true
        }
      };

      const response = await fetch(`${BASE_URL}/api/batch/export`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(largeExportConfig)
      });

      expect(response.ok).toBe(true);

      const buffer = await response.arrayBuffer();
      expect(buffer.byteLength).toBeGreaterThan(0);

      // 验证文件大小合理（应该比小批量导出的文件大）
      const largeFileName = `测试大批量导出结果_${Date.now()}.xlsx`;
      fs.writeFileSync(largeFileName, Buffer.from(buffer));
      
      const stats = fs.statSync(largeFileName);
      expect(stats.size).toBeGreaterThan(1024); // 至少1KB

      // 清理文件
      fs.unlinkSync(largeFileName);
    }, 30000); // 增加超时时间
  });
});
