# 测试数据管理规范

本目录包含所有测试用的数据文件和固定装置，用于支持各种测试场景。

## 目录结构

```
fixtures/
├── data/           # JSON、CSV等结构化测试数据
├── templates/      # Excel模板文件
├── mocks/          # 模拟响应数据
└── README.md       # 本文档
```

## 文件命名规范

### 模板文件 (templates/)
- **标准测试模板**: `standard-test-template.xlsx`
- **节假日测试模板**: `holiday-test-template.xlsx`
- **长期活动模板**: `90day-test-template.xlsx`
- **大批量测试模板**: `large-batch-test-template.xlsx`
- **特定场景模板**: `[场景名]-test-template.xlsx`

### 数据文件 (data/)
- **产品数据**: `products-[场景].json`
- **配置数据**: `configs-[场景].json`
- **时间数据**: `time-slots-[场景].json`
- **用户数据**: `users-[场景].json`

### 模拟数据 (mocks/)
- **API响应**: `api-[endpoint]-[场景].json`
- **数据库数据**: `db-[表名]-[场景].json`
- **外部服务**: `external-[服务名]-[场景].json`

## 文件管理原则

### 1. 版本控制
- ✅ **包含在版本控制中**: 标准测试模板、配置文件、示例数据
- ❌ **排除版本控制**: 临时生成的文件、大型测试数据、敏感信息

### 2. 文件大小限制
- **小文件** (< 1MB): 直接存储在仓库中
- **中等文件** (1-10MB): 考虑使用Git LFS
- **大文件** (> 10MB): 动态生成或外部存储

### 3. 数据安全
- 所有测试数据必须是虚拟的，不包含真实的商品信息
- 敏感配置使用环境变量或配置文件
- 定期清理过期的测试数据

## 当前文件说明

### 模板文件
1. **90天连续活动模板.xlsx** - 长期活动测试模板
2. **国庆节时间处理测试.xlsx** - 节假日处理测试模板
3. **完整测试批量时间生成模板.xlsx** - 完整功能测试模板
4. **正确格式批量生成模板.xlsx** - 标准格式参考模板
5. **测试批量时间生成模板.xlsx** - 基础功能测试模板

### 导出结果文件
- **测试导出结果_*.xlsx** - 测试运行产生的导出文件
- 这些文件应该定期清理，不应该提交到版本控制

## 使用指南

### 在测试中使用模板文件
```typescript
import path from 'path';

const templatePath = path.join(
  process.cwd(), 
  'tests/fixtures/templates', 
  'standard-test-template.xlsx'
);
```

### 生成新的测试模板
```typescript
import { TemplateGenerator } from '../tools/template-generator';

// 生成标准测试模板
const templates = TemplateGenerator.generateAllTestTemplates();
```

### 调试模板文件
```typescript
import { TemplateDebugger } from '../tools/template-debugger';

// 调试单个模板
const result = TemplateDebugger.debugTemplate(templatePath);

// 批量调试所有模板
const results = TemplateDebugger.debugAllTemplates('tests/fixtures/templates');
```

## 清理策略

### 自动清理
测试运行后会自动清理：
- 临时生成的文件
- 测试导出结果
- 临时上传文件

### 手动清理
定期执行以下清理操作：
```bash
# 清理测试导出文件
find tests/fixtures/templates -name "测试导出结果_*.xlsx" -delete

# 清理临时文件
find tests/fixtures -name "temp_*" -delete

# 清理过期的测试数据（超过30天）
find tests/fixtures -name "*.xlsx" -mtime +30 -delete
```

### 清理脚本
```bash
# 运行清理脚本
npm run test:cleanup
```

## 最佳实践

1. **保持文件最新**: 定期更新测试模板以反映最新的业务需求
2. **文档化**: 为每个测试文件添加说明注释
3. **最小化**: 只保留必要的测试数据，避免冗余
4. **标准化**: 使用统一的命名规范和文件格式
5. **安全性**: 确保测试数据不包含敏感信息

## 故障排除

### 常见问题
1. **文件不存在**: 检查文件路径和名称是否正确
2. **格式错误**: 使用TemplateDebugger工具验证文件格式
3. **权限问题**: 确保测试进程有读写权限
4. **编码问题**: 确保Excel文件使用正确的编码格式

### 调试步骤
1. 使用TemplateDebugger验证文件
2. 检查文件权限和路径
3. 查看测试日志获取详细错误信息
4. 重新生成测试模板文件
