/**
 * 测试模板生成工具
 * 用于创建各种测试场景的Excel模板文件
 */

import * as XLSX from 'xlsx';
import path from 'path';
import fs from 'fs';

export interface TestProductData {
  productId: string;
  activityType: string;
  timeMode: string;
  startDate: string;
  endDate: string;
  dailyStartTime: string;
  dailyEndTime: string;
  region: string;
  minQuantity: number;
  discount: number;
  weekendFullDay: boolean;
  holidayFullDay: boolean;
}

export class TemplateGenerator {
  /**
   * 创建标准测试模板
   */
  static createStandardTemplate(): TestProductData[] {
    return [
      {
        productId: 'TMALL001',
        activityType: '多件多折',
        timeMode: '连续',
        startDate: '2024-01-15',
        endDate: '2024-01-21',
        dailyStartTime: '09:00:00',
        dailyEndTime: '21:00:00',
        region: '全国',
        minQuantity: 2,
        discount: 8,
        weekendFullDay: true,
        holidayFullDay: true
      },
      {
        productId: 'TMALL002',
        activityType: '多件多折',
        timeMode: '连续',
        startDate: '2024-01-15',
        endDate: '2024-01-21',
        dailyStartTime: '10:00:00',
        dailyEndTime: '22:00:00',
        region: '全国',
        minQuantity: 3,
        discount: 7,
        weekendFullDay: false,
        holidayFullDay: true
      },
      {
        productId: 'TMALL003',
        activityType: '多件多折',
        timeMode: '连续',
        startDate: '2024-01-15',
        endDate: '2024-01-21',
        dailyStartTime: '08:00:00',
        dailyEndTime: '20:00:00',
        region: '全国',
        minQuantity: 2,
        discount: 9,
        weekendFullDay: true,
        holidayFullDay: false
      }
    ];
  }

  /**
   * 创建节假日测试模板
   */
  static createHolidayTemplate(): TestProductData[] {
    return [
      {
        productId: 'HOLIDAY001',
        activityType: '多件多折',
        timeMode: '连续',
        startDate: '2025-09-29', // 国庆节前
        endDate: '2025-10-03',   // 国庆节期间
        dailyStartTime: '19:00:00',
        dailyEndTime: '08:00:00', // 跨天
        region: '全国',
        minQuantity: 2,
        discount: 8,
        weekendFullDay: true,
        holidayFullDay: true
      },
      {
        productId: 'HOLIDAY002',
        activityType: '多件多折',
        timeMode: '连续',
        startDate: '2025-09-29',
        endDate: '2025-10-03',
        dailyStartTime: '09:00:00',
        dailyEndTime: '21:00:00',
        region: '华东',
        minQuantity: 3,
        discount: 7,
        weekendFullDay: false,
        holidayFullDay: true
      }
    ];
  }

  /**
   * 创建90天连续活动模板
   */
  static create90DayTemplate(): TestProductData[] {
    const startDate = new Date('2024-01-01');
    const endDate = new Date(startDate);
    endDate.setDate(startDate.getDate() + 89); // 90天

    return [
      {
        productId: 'LONG001',
        activityType: '多件多折',
        timeMode: '连续',
        startDate: startDate.toISOString().split('T')[0],
        endDate: endDate.toISOString().split('T')[0],
        dailyStartTime: '09:00:00',
        dailyEndTime: '21:00:00',
        region: '全国',
        minQuantity: 2,
        discount: 8,
        weekendFullDay: true,
        holidayFullDay: true
      }
    ];
  }

  /**
   * 创建大批量测试模板
   */
  static createLargeBatchTemplate(count: number = 100): TestProductData[] {
    const products: TestProductData[] = [];
    
    for (let i = 1; i <= count; i++) {
      products.push({
        productId: `BATCH${i.toString().padStart(3, '0')}`,
        activityType: '多件多折',
        timeMode: '连续',
        startDate: '2024-01-15',
        endDate: '2024-01-21',
        dailyStartTime: '09:00:00',
        dailyEndTime: '21:00:00',
        region: i % 2 === 0 ? '全国' : '华东',
        minQuantity: (i % 4) + 1,
        discount: 5 + (i % 5),
        weekendFullDay: i % 2 === 0,
        holidayFullDay: i % 3 === 0
      });
    }
    
    return products;
  }

  /**
   * 将测试数据转换为Excel格式的数组
   */
  static toExcelData(products: TestProductData[]): any[][] {
    const headers = [
      '商品ID', '活动类型', '时间模式', '开始日期', '结束日期', 
      '每日开始时间', '每日结束时间', '活动区域', '满几件', '折扣', 
      '周末全天', '节假日全天'
    ];

    const rows = products.map(product => [
      product.productId,
      product.activityType,
      product.timeMode,
      product.startDate,
      product.endDate,
      product.dailyStartTime,
      product.dailyEndTime,
      product.region,
      product.minQuantity,
      product.discount,
      product.weekendFullDay ? '是' : '否',
      product.holidayFullDay ? '是' : '否'
    ]);

    return [headers, ...rows];
  }

  /**
   * 生成Excel文件
   */
  static generateExcelFile(
    products: TestProductData[], 
    fileName: string,
    outputDir: string = 'tests/fixtures/templates'
  ): string {
    const data = this.toExcelData(products);
    const workbook = XLSX.utils.book_new();
    const worksheet = XLSX.utils.aoa_to_sheet(data);

    // 设置列宽
    const colWidths = [
      { wch: 15 }, // 商品ID
      { wch: 12 }, // 活动类型
      { wch: 10 }, // 时间模式
      { wch: 12 }, // 开始日期
      { wch: 12 }, // 结束日期
      { wch: 15 }, // 每日开始时间
      { wch: 15 }, // 每日结束时间
      { wch: 10 }, // 活动区域
      { wch: 8 },  // 满几件
      { wch: 8 },  // 折扣
      { wch: 10 }, // 周末全天
      { wch: 10 }  // 节假日全天
    ];
    worksheet['!cols'] = colWidths;

    XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1');

    // 确保输出目录存在
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }

    const filePath = path.join(outputDir, fileName);
    XLSX.writeFile(workbook, filePath);

    return filePath;
  }

  /**
   * 生成所有预定义的测试模板
   */
  static generateAllTestTemplates(): Record<string, string> {
    const templates = {
      standard: this.generateExcelFile(
        this.createStandardTemplate(),
        'standard-test-template.xlsx'
      ),
      holiday: this.generateExcelFile(
        this.createHolidayTemplate(),
        'holiday-test-template.xlsx'
      ),
      longTerm: this.generateExcelFile(
        this.create90DayTemplate(),
        '90day-test-template.xlsx'
      ),
      largeBatch: this.generateExcelFile(
        this.createLargeBatchTemplate(50),
        'large-batch-test-template.xlsx'
      )
    };

    console.log('✅ 已生成所有测试模板:');
    Object.entries(templates).forEach(([name, path]) => {
      console.log(`  - ${name}: ${path}`);
    });

    return templates;
  }
}
