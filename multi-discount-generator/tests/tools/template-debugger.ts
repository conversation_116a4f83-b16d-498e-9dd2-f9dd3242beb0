/**
 * 模板调试工具
 * 用于调试和验证Excel模板的解析结果
 */

import * as XLSX from 'xlsx';
import path from 'path';
import fs from 'fs';

export interface DebugResult {
  success: boolean;
  fileName: string;
  sheetNames: string[];
  rowCount: number;
  columnCount: number;
  headers: string[];
  sampleData: any[];
  errors: string[];
  warnings: string[];
}

export class TemplateDebugger {
  /**
   * 调试Excel模板文件
   */
  static debugTemplate(filePath: string): DebugResult {
    const result: DebugResult = {
      success: false,
      fileName: path.basename(filePath),
      sheetNames: [],
      rowCount: 0,
      columnCount: 0,
      headers: [],
      sampleData: [],
      errors: [],
      warnings: []
    };

    try {
      // 检查文件是否存在
      if (!fs.existsSync(filePath)) {
        result.errors.push(`文件不存在: ${filePath}`);
        return result;
      }

      // 读取Excel文件
      const workbook = XLSX.readFile(filePath);
      result.sheetNames = workbook.SheetNames;

      if (result.sheetNames.length === 0) {
        result.errors.push('Excel文件中没有工作表');
        return result;
      }

      // 使用第一个工作表
      const sheetName = result.sheetNames[0];
      const worksheet = workbook.Sheets[sheetName];

      if (!worksheet) {
        result.errors.push(`无法读取工作表: ${sheetName}`);
        return result;
      }

      // 转换为JSON数据
      const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
      
      if (jsonData.length === 0) {
        result.errors.push('工作表中没有数据');
        return result;
      }

      result.rowCount = jsonData.length;
      result.headers = jsonData[0] as string[];
      result.columnCount = result.headers.length;

      // 验证表头
      const expectedHeaders = [
        '商品ID', '活动类型', '时间模式', '开始日期', '结束日期',
        '每日开始时间', '每日结束时间', '活动区域', '满几件', '折扣',
        '周末全天', '节假日全天'
      ];

      expectedHeaders.forEach(header => {
        if (!result.headers.includes(header)) {
          result.warnings.push(`缺少必需的列: ${header}`);
        }
      });

      // 检查额外的列
      result.headers.forEach(header => {
        if (!expectedHeaders.includes(header)) {
          result.warnings.push(`发现未知的列: ${header}`);
        }
      });

      // 获取样本数据（前5行，排除表头）
      const dataRows = jsonData.slice(1, 6);
      result.sampleData = dataRows.map((row: any[]) => {
        const obj: any = {};
        result.headers.forEach((header, index) => {
          obj[header] = row[index];
        });
        return obj;
      });

      // 验证数据完整性
      this.validateDataIntegrity(result);

      result.success = result.errors.length === 0;

    } catch (error) {
      result.errors.push(`解析文件时出错: ${error.message}`);
    }

    return result;
  }

  /**
   * 验证数据完整性
   */
  private static validateDataIntegrity(result: DebugResult): void {
    result.sampleData.forEach((row, index) => {
      const rowNum = index + 2; // 加2是因为从第2行开始（排除表头）

      // 检查必需字段
      if (!row['商品ID']) {
        result.errors.push(`第${rowNum}行: 商品ID不能为空`);
      }

      if (!row['活动类型']) {
        result.errors.push(`第${rowNum}行: 活动类型不能为空`);
      }

      // 检查日期格式
      if (row['开始日期'] && !this.isValidDate(row['开始日期'])) {
        result.warnings.push(`第${rowNum}行: 开始日期格式可能不正确`);
      }

      if (row['结束日期'] && !this.isValidDate(row['结束日期'])) {
        result.warnings.push(`第${rowNum}行: 结束日期格式可能不正确`);
      }

      // 检查时间格式
      if (row['每日开始时间'] && !this.isValidTime(row['每日开始时间'])) {
        result.warnings.push(`第${rowNum}行: 每日开始时间格式可能不正确`);
      }

      if (row['每日结束时间'] && !this.isValidTime(row['每日结束时间'])) {
        result.warnings.push(`第${rowNum}行: 每日结束时间格式可能不正确`);
      }

      // 检查数值字段
      if (row['满几件'] && (isNaN(row['满几件']) || row['满几件'] <= 0)) {
        result.warnings.push(`第${rowNum}行: 满几件应该是正整数`);
      }

      if (row['折扣'] && (isNaN(row['折扣']) || row['折扣'] <= 0 || row['折扣'] > 10)) {
        result.warnings.push(`第${rowNum}行: 折扣应该是1-10之间的数字`);
      }

      // 检查布尔字段
      const booleanFields = ['周末全天', '节假日全天'];
      booleanFields.forEach(field => {
        if (row[field] && !['是', '否', true, false].includes(row[field])) {
          result.warnings.push(`第${rowNum}行: ${field}应该是"是"或"否"`);
        }
      });
    });
  }

  /**
   * 验证日期格式
   */
  private static isValidDate(dateStr: any): boolean {
    if (typeof dateStr === 'number') {
      // Excel日期序列号
      return dateStr > 0;
    }
    
    if (typeof dateStr === 'string') {
      // 字符串日期格式
      const date = new Date(dateStr);
      return !isNaN(date.getTime());
    }
    
    return false;
  }

  /**
   * 验证时间格式
   */
  private static isValidTime(timeStr: any): boolean {
    if (typeof timeStr === 'number') {
      // Excel时间小数
      return timeStr >= 0 && timeStr < 1;
    }
    
    if (typeof timeStr === 'string') {
      // 时间字符串格式 HH:MM:SS
      const timeRegex = /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9]$/;
      return timeRegex.test(timeStr);
    }
    
    return false;
  }

  /**
   * 打印调试结果
   */
  static printDebugResult(result: DebugResult): void {
    console.log('🔍 模板调试结果');
    console.log('================');
    console.log(`文件名: ${result.fileName}`);
    console.log(`状态: ${result.success ? '✅ 成功' : '❌ 失败'}`);
    console.log(`工作表: ${result.sheetNames.join(', ')}`);
    console.log(`行数: ${result.rowCount}`);
    console.log(`列数: ${result.columnCount}`);
    console.log('');

    if (result.headers.length > 0) {
      console.log('📋 表头信息:');
      result.headers.forEach((header, index) => {
        console.log(`  ${index + 1}. ${header}`);
      });
      console.log('');
    }

    if (result.sampleData.length > 0) {
      console.log('📊 样本数据:');
      result.sampleData.forEach((row, index) => {
        console.log(`  第${index + 1}行:`);
        Object.entries(row).forEach(([key, value]) => {
          console.log(`    ${key}: ${value}`);
        });
        console.log('');
      });
    }

    if (result.errors.length > 0) {
      console.log('❌ 错误信息:');
      result.errors.forEach((error, index) => {
        console.log(`  ${index + 1}. ${error}`);
      });
      console.log('');
    }

    if (result.warnings.length > 0) {
      console.log('⚠️ 警告信息:');
      result.warnings.forEach((warning, index) => {
        console.log(`  ${index + 1}. ${warning}`);
      });
      console.log('');
    }
  }

  /**
   * 批量调试目录中的所有模板文件
   */
  static debugAllTemplates(templateDir: string): Record<string, DebugResult> {
    const results: Record<string, DebugResult> = {};

    if (!fs.existsSync(templateDir)) {
      console.error(`模板目录不存在: ${templateDir}`);
      return results;
    }

    const files = fs.readdirSync(templateDir);
    const excelFiles = files.filter(file => 
      file.endsWith('.xlsx') || file.endsWith('.xls')
    );

    console.log(`🔍 开始调试 ${excelFiles.length} 个模板文件...`);
    console.log('');

    excelFiles.forEach(file => {
      const filePath = path.join(templateDir, file);
      const result = this.debugTemplate(filePath);
      results[file] = result;
      
      console.log(`📄 ${file}:`);
      console.log(`  状态: ${result.success ? '✅' : '❌'}`);
      console.log(`  行数: ${result.rowCount}`);
      console.log(`  错误: ${result.errors.length}`);
      console.log(`  警告: ${result.warnings.length}`);
      console.log('');
    });

    return results;
  }
}
