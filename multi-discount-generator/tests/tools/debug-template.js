// 调试模板解析问题
const XLSX = require('xlsx');
const path = require('path');

function debugTemplate() {
  console.log('🔍 调试模板解析...');
  
  try {
    // 读取测试模板
    const templatePath = path.join(__dirname, '正确格式批量生成模板.xlsx');
    console.log('📁 模板路径:', templatePath);
    
    const workbook = XLSX.readFile(templatePath);
    const sheetName = workbook.SheetNames[0];
    console.log('📊 工作表名:', sheetName);
    
    const worksheet = workbook.Sheets[sheetName];
    const jsonData = XLSX.utils.sheet_to_json(worksheet);
    
    console.log('📋 解析的数据:');
    console.log('总行数:', jsonData.length);
    console.log('');
    
    // 检查每一行数据
    jsonData.forEach((row, index) => {
      console.log(`第${index + 1}行数据:`);
      console.log('  商品ID:', row['商品ID']);
      console.log('  活动区域:', row['活动区域']);
      console.log('  满几件:', row['满几件']);
      console.log('  折扣:', row['折扣']);
      console.log('  时间模式:', row['时间模式']);
      console.log('  开始日期:', row['开始日期']);
      console.log('  结束日期:', row['结束日期']);
      console.log('  每日开始时间:', row['每日开始时间']);
      console.log('  每日结束时间:', row['每日结束时间']);
      console.log('  周末全天:', row['周末全天']);
      console.log('  节假日全天:', row['节假日全天']);
      console.log('  完整行数据:', JSON.stringify(row, null, 2));
      console.log('---');
    });
    
    // 检查列名
    console.log('📝 所有列名:');
    if (jsonData.length > 0) {
      const columns = Object.keys(jsonData[0]);
      columns.forEach((col, index) => {
        console.log(`  ${index + 1}. "${col}"`);
      });
    }
    
    // 模拟FormData构建
    console.log('🔧 模拟FormData构建:');
    if (jsonData.length > 0) {
      const firstRow = jsonData[0];
      const formData = {
        productId: firstRow['商品ID'] || '',
        region: firstRow['活动区域'] || '全国',
        minQuantity: parseInt(firstRow['满几件']) || 1,
        discount: parseFloat(firstRow['折扣']) || 8,
        timeMode: firstRow['时间模式'] || 'continuous',
        startDate: firstRow['开始日期'] || '2024-01-15',
        endDate: firstRow['结束日期'] || '2024-01-21',
        dailyStartTime: firstRow['每日开始时间'] || '09:00:00',
        dailyEndTime: firstRow['每日结束时间'] || '21:00:00',
        weekendFullDay: (firstRow['周末全天'] === '是'),
        holidayFullDay: (firstRow['节假日全天'] === '是'),
        timeSlotCount: parseInt(firstRow['时间段数量']) || 5,
        slotDuration: parseInt(firstRow['时段时长']) || 1
      };
      
      console.log('构建的FormData:', JSON.stringify(formData, null, 2));
      
      // 检查关键字段
      console.log('🎯 关键字段检查:');
      console.log('  商品ID是否为空:', !formData.productId);
      console.log('  开始日期是否有效:', formData.startDate && !isNaN(new Date(formData.startDate).getTime()));
      console.log('  结束日期是否有效:', formData.endDate && !isNaN(new Date(formData.endDate).getTime()));
      console.log('  时间格式是否正确:', /^\d{2}:\d{2}:\d{2}$/.test(formData.dailyStartTime));
    }
    
  } catch (error) {
    console.error('❌ 调试失败:', error);
  }
}

// 运行调试
debugTemplate();
