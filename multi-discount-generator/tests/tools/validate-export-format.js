// 验证批量导出格式是否与前端手动导出一致
const XLSX = require('xlsx');
const fs = require('fs');

// 期望的表头（与前端constants/index.ts中的EXCEL_HEADERS一致）
const EXPECTED_HEADERS = [
  '活动类型',
  '商品ID',
  '开始时间\n示例：\n2020-03-27 00:00:00',
  '结束时间\n示例：\n2020-03-27 23:59:59', 
  '活动区域\n支持填写"全国"及5大区\n5大区：华中,西南/西北,华南,华东,华北',
  '满几件\n说明：表示满N件，件数只能在下拉框中选择',
  '折扣-打几折\n说明：表示打几折，折扣只能下拉选择',
  '立减金额-多件总额下的扣减金额\n表示扣减的金额，单位元，支持最多2位小数，与折扣只能择一生效'
];

function validateExportFormat() {
  console.log('🔍 验证批量导出格式是否与前端手动导出一致\n');

  // 查找最新的导出文件
  const files = fs.readdirSync('.').filter(f => f.startsWith('测试导出结果_') && f.endsWith('.xlsx'));
  if (files.length === 0) {
    console.error('❌ 未找到导出文件');
    return false;
  }

  const latestFile = files.sort().pop();
  console.log(`📁 验证文件: ${latestFile}`);

  try {
    // 读取Excel文件
    const workbook = XLSX.readFile(latestFile);
    
    // 验证工作表数量和名称
    console.log('\n📊 工作表验证:');
    console.log(`   - 工作表数量: ${workbook.SheetNames.length} (期望: 1)`);
    console.log(`   - 工作表名称: ${workbook.SheetNames[0]} (期望: Sheet1)`);
    
    if (workbook.SheetNames.length !== 1 || workbook.SheetNames[0] !== 'Sheet1') {
      console.error('❌ 工作表结构不符合前端格式');
      return false;
    }

    const worksheet = workbook.Sheets['Sheet1'];
    const data = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
    
    if (data.length === 0) {
      console.error('❌ 工作表为空');
      return false;
    }

    // 验证表头
    console.log('\n📋 表头验证:');
    const actualHeaders = data[0];
    
    let headerValid = true;
    for (let i = 0; i < EXPECTED_HEADERS.length; i++) {
      const expected = EXPECTED_HEADERS[i];
      const actual = actualHeaders[i];
      
      if (expected !== actual) {
        console.error(`❌ 表头不匹配 [列${i+1}]:`);
        console.error(`   期望: "${expected}"`);
        console.error(`   实际: "${actual}"`);
        headerValid = false;
      } else {
        console.log(`✅ 列${i+1}: ${expected.split('\n')[0]}`);
      }
    }

    if (!headerValid) {
      return false;
    }

    // 验证数据格式
    console.log('\n📊 数据格式验证:');
    const dataRows = data.slice(1);
    console.log(`   - 数据行数: ${dataRows.length}`);

    if (dataRows.length === 0) {
      console.error('❌ 没有数据行');
      return false;
    }

    // 验证第一行数据格式
    const firstRow = dataRows[0];
    console.log('\n🔍 第一行数据验证:');
    
    // 活动类型
    if (firstRow[0] !== '多件多折') {
      console.error(`❌ 活动类型错误: "${firstRow[0]}" (期望: "多件多折")`);
      return false;
    }
    console.log(`✅ 活动类型: ${firstRow[0]}`);

    // 商品ID
    if (!firstRow[1] || typeof firstRow[1] !== 'string') {
      console.error(`❌ 商品ID格式错误: "${firstRow[1]}"`);
      return false;
    }
    console.log(`✅ 商品ID: ${firstRow[1]}`);

    // 时间格式验证
    const startTime = firstRow[2];
    const endTime = firstRow[3];
    const timeRegex = /^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/;
    
    if (!timeRegex.test(startTime)) {
      console.error(`❌ 开始时间格式错误: "${startTime}" (期望格式: YYYY-MM-DD HH:mm:ss)`);
      return false;
    }
    console.log(`✅ 开始时间: ${startTime}`);

    if (!timeRegex.test(endTime)) {
      console.error(`❌ 结束时间格式错误: "${endTime}" (期望格式: YYYY-MM-DD HH:mm:ss)`);
      return false;
    }
    console.log(`✅ 结束时间: ${endTime}`);

    // 活动区域
    const region = firstRow[4];
    if (!region || typeof region !== 'string') {
      console.error(`❌ 活动区域格式错误: "${region}"`);
      return false;
    }
    console.log(`✅ 活动区域: ${region}`);

    // 满几件（应该固定为1）
    const minQuantity = firstRow[5];
    if (minQuantity !== 1) {
      console.error(`❌ 满几件错误: "${minQuantity}" (期望: 1)`);
      return false;
    }
    console.log(`✅ 满几件: ${minQuantity}`);

    // 折扣
    const discount = firstRow[6];
    if (typeof discount !== 'number' || discount < 1 || discount > 10) {
      console.error(`❌ 折扣格式错误: "${discount}" (期望: 1-10的数字)`);
      return false;
    }
    console.log(`✅ 折扣: ${discount}`);

    // 立减金额（应该为空）
    const reduction = firstRow[7];
    if (reduction !== '' && reduction !== null && reduction !== undefined) {
      console.error(`❌ 立减金额应为空: "${reduction}"`);
      return false;
    }
    console.log(`✅ 立减金额: 空值`);

    // 验证所有数据行的一致性
    console.log('\n🔍 数据一致性验证:');
    let consistencyValid = true;
    
    for (let i = 0; i < dataRows.length; i++) {
      const row = dataRows[i];
      
      // 检查活动类型是否都是"多件多折"
      if (row[0] !== '多件多折') {
        console.error(`❌ 第${i+2}行活动类型错误: "${row[0]}"`);
        consistencyValid = false;
      }
      
      // 检查满几件是否都是1
      if (row[5] !== 1) {
        console.error(`❌ 第${i+2}行满几件错误: "${row[5]}"`);
        consistencyValid = false;
      }
      
      // 检查立减金额是否都为空
      if (row[7] !== '' && row[7] !== null && row[7] !== undefined) {
        console.error(`❌ 第${i+2}行立减金额应为空: "${row[7]}"`);
        consistencyValid = false;
      }
    }

    if (consistencyValid) {
      console.log(`✅ 所有${dataRows.length}行数据格式一致`);
    }

    // 统计信息
    console.log('\n📈 统计信息:');
    const uniqueProducts = [...new Set(dataRows.map(row => row[1]))];
    console.log(`   - 唯一商品数: ${uniqueProducts.length}`);
    console.log(`   - 总记录数: ${dataRows.length}`);
    console.log(`   - 平均每商品记录数: ${(dataRows.length / uniqueProducts.length).toFixed(1)}`);

    if (headerValid && consistencyValid) {
      console.log('\n🎉 格式验证通过！');
      console.log('✅ 批量导出格式与前端手动导出完全一致');
      return true;
    } else {
      console.log('\n❌ 格式验证失败');
      return false;
    }

  } catch (error) {
    console.error('❌ 验证过程出错:', error.message);
    return false;
  }
}

// 运行验证
const isValid = validateExportFormat();
process.exit(isValid ? 0 : 1);
