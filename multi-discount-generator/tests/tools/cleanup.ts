/**
 * 测试数据清理工具
 * 用于清理测试过程中产生的临时文件和过期数据
 */

import fs from 'fs';
import path from 'path';

export interface CleanupOptions {
  dryRun?: boolean;
  verbose?: boolean;
  maxAge?: number; // 文件最大保留天数
}

export interface CleanupResult {
  deletedFiles: string[];
  deletedDirs: string[];
  errors: string[];
  totalSize: number;
}

export class TestDataCleaner {
  /**
   * 清理测试导出文件
   */
  static cleanupExportFiles(options: CleanupOptions = {}): CleanupResult {
    const result: CleanupResult = {
      deletedFiles: [],
      deletedDirs: [],
      errors: [],
      totalSize: 0
    };

    const patterns = [
      /^测试导出结果_\d+\.xlsx$/,
      /^test-export-\d+\.xlsx$/,
      /^temp_.*\.xlsx$/
    ];

    const searchDirs = [
      process.cwd(),
      path.join(process.cwd(), 'tests/fixtures/templates'),
      path.join(process.cwd(), 'uploads'),
      path.join(process.cwd(), 'temp')
    ];

    searchDirs.forEach(dir => {
      if (!fs.existsSync(dir)) return;

      try {
        const files = fs.readdirSync(dir);
        
        files.forEach(file => {
          const shouldDelete = patterns.some(pattern => pattern.test(file));
          
          if (shouldDelete) {
            const filePath = path.join(dir, file);
            
            try {
              const stats = fs.statSync(filePath);
              
              // 检查文件年龄
              if (options.maxAge) {
                const ageInDays = (Date.now() - stats.mtime.getTime()) / (1000 * 60 * 60 * 24);
                if (ageInDays < options.maxAge) {
                  return; // 文件太新，跳过
                }
              }

              if (options.verbose) {
                console.log(`发现待删除文件: ${filePath} (${(stats.size / 1024).toFixed(2)} KB)`);
              }

              if (!options.dryRun) {
                fs.unlinkSync(filePath);
              }

              result.deletedFiles.push(filePath);
              result.totalSize += stats.size;

            } catch (error) {
              result.errors.push(`删除文件失败 ${filePath}: ${error.message}`);
            }
          }
        });

      } catch (error) {
        result.errors.push(`读取目录失败 ${dir}: ${error.message}`);
      }
    });

    return result;
  }

  /**
   * 清理临时目录
   */
  static cleanupTempDirectories(options: CleanupOptions = {}): CleanupResult {
    const result: CleanupResult = {
      deletedFiles: [],
      deletedDirs: [],
      errors: [],
      totalSize: 0
    };

    const tempDirs = [
      path.join(process.cwd(), 'temp'),
      path.join(process.cwd(), 'tmp'),
      path.join(process.cwd(), 'uploads/temp'),
      path.join(process.cwd(), 'tests/temp')
    ];

    tempDirs.forEach(dir => {
      if (!fs.existsSync(dir)) return;

      try {
        const stats = fs.statSync(dir);
        
        // 检查目录年龄
        if (options.maxAge) {
          const ageInDays = (Date.now() - stats.mtime.getTime()) / (1000 * 60 * 60 * 24);
          if (ageInDays < options.maxAge) {
            return; // 目录太新，跳过
          }
        }

        if (options.verbose) {
          console.log(`发现待删除目录: ${dir}`);
        }

        if (!options.dryRun) {
          fs.rmSync(dir, { recursive: true, force: true });
        }

        result.deletedDirs.push(dir);

      } catch (error) {
        result.errors.push(`删除目录失败 ${dir}: ${error.message}`);
      }
    });

    return result;
  }

  /**
   * 清理测试数据库文件
   */
  static cleanupTestDatabases(options: CleanupOptions = {}): CleanupResult {
    const result: CleanupResult = {
      deletedFiles: [],
      deletedDirs: [],
      errors: [],
      totalSize: 0
    };

    const dbPatterns = [
      /^test\.db$/,
      /^test-\d+\.db$/,
      /^.*\.test\.db$/
    ];

    const searchDirs = [
      process.cwd(),
      path.join(process.cwd(), 'prisma'),
      path.join(process.cwd(), 'tests')
    ];

    searchDirs.forEach(dir => {
      if (!fs.existsSync(dir)) return;

      try {
        const files = fs.readdirSync(dir);
        
        files.forEach(file => {
          const shouldDelete = dbPatterns.some(pattern => pattern.test(file));
          
          if (shouldDelete) {
            const filePath = path.join(dir, file);
            
            try {
              const stats = fs.statSync(filePath);

              if (options.verbose) {
                console.log(`发现待删除数据库: ${filePath} (${(stats.size / 1024).toFixed(2)} KB)`);
              }

              if (!options.dryRun) {
                fs.unlinkSync(filePath);
              }

              result.deletedFiles.push(filePath);
              result.totalSize += stats.size;

            } catch (error) {
              result.errors.push(`删除数据库文件失败 ${filePath}: ${error.message}`);
            }
          }
        });

      } catch (error) {
        result.errors.push(`读取目录失败 ${dir}: ${error.message}`);
      }
    });

    return result;
  }

  /**
   * 清理日志文件
   */
  static cleanupLogFiles(options: CleanupOptions = {}): CleanupResult {
    const result: CleanupResult = {
      deletedFiles: [],
      deletedDirs: [],
      errors: [],
      totalSize: 0
    };

    const logPatterns = [
      /^test.*\.log$/,
      /^debug.*\.log$/,
      /^error.*\.log$/,
      /^.*\.test\.log$/
    ];

    const searchDirs = [
      process.cwd(),
      path.join(process.cwd(), 'logs'),
      path.join(process.cwd(), 'tests/logs')
    ];

    searchDirs.forEach(dir => {
      if (!fs.existsSync(dir)) return;

      try {
        const files = fs.readdirSync(dir);
        
        files.forEach(file => {
          const shouldDelete = logPatterns.some(pattern => pattern.test(file));
          
          if (shouldDelete) {
            const filePath = path.join(dir, file);
            
            try {
              const stats = fs.statSync(filePath);
              
              // 检查文件年龄
              if (options.maxAge) {
                const ageInDays = (Date.now() - stats.mtime.getTime()) / (1000 * 60 * 60 * 24);
                if (ageInDays < options.maxAge) {
                  return; // 文件太新，跳过
                }
              }

              if (options.verbose) {
                console.log(`发现待删除日志: ${filePath} (${(stats.size / 1024).toFixed(2)} KB)`);
              }

              if (!options.dryRun) {
                fs.unlinkSync(filePath);
              }

              result.deletedFiles.push(filePath);
              result.totalSize += stats.size;

            } catch (error) {
              result.errors.push(`删除日志文件失败 ${filePath}: ${error.message}`);
            }
          }
        });

      } catch (error) {
        result.errors.push(`读取目录失败 ${dir}: ${error.message}`);
      }
    });

    return result;
  }

  /**
   * 执行完整清理
   */
  static cleanupAll(options: CleanupOptions = {}): CleanupResult {
    const combinedResult: CleanupResult = {
      deletedFiles: [],
      deletedDirs: [],
      errors: [],
      totalSize: 0
    };

    const cleanupMethods = [
      this.cleanupExportFiles,
      this.cleanupTempDirectories,
      this.cleanupTestDatabases,
      this.cleanupLogFiles
    ];

    cleanupMethods.forEach(method => {
      const result = method.call(this, options);
      combinedResult.deletedFiles.push(...result.deletedFiles);
      combinedResult.deletedDirs.push(...result.deletedDirs);
      combinedResult.errors.push(...result.errors);
      combinedResult.totalSize += result.totalSize;
    });

    return combinedResult;
  }

  /**
   * 打印清理结果
   */
  static printCleanupResult(result: CleanupResult, options: CleanupOptions = {}): void {
    console.log('🧹 测试数据清理结果');
    console.log('==================');
    
    if (options.dryRun) {
      console.log('⚠️ 这是预览模式，没有实际删除文件');
      console.log('');
    }

    console.log(`删除文件数量: ${result.deletedFiles.length}`);
    console.log(`删除目录数量: ${result.deletedDirs.length}`);
    console.log(`释放空间: ${(result.totalSize / 1024 / 1024).toFixed(2)} MB`);
    console.log(`错误数量: ${result.errors.length}`);
    console.log('');

    if (result.deletedFiles.length > 0 && options.verbose) {
      console.log('📄 删除的文件:');
      result.deletedFiles.forEach(file => {
        console.log(`  - ${file}`);
      });
      console.log('');
    }

    if (result.deletedDirs.length > 0 && options.verbose) {
      console.log('📁 删除的目录:');
      result.deletedDirs.forEach(dir => {
        console.log(`  - ${dir}`);
      });
      console.log('');
    }

    if (result.errors.length > 0) {
      console.log('❌ 错误信息:');
      result.errors.forEach(error => {
        console.log(`  - ${error}`);
      });
      console.log('');
    }

    if (result.deletedFiles.length === 0 && result.deletedDirs.length === 0) {
      console.log('✨ 没有找到需要清理的文件');
    } else {
      console.log('✅ 清理完成');
    }
  }
}

// 命令行接口
if (require.main === module) {
  const args = process.argv.slice(2);
  const options: CleanupOptions = {
    dryRun: args.includes('--dry-run'),
    verbose: args.includes('--verbose'),
    maxAge: args.includes('--max-age') ? 
      parseInt(args[args.indexOf('--max-age') + 1]) : undefined
  };

  console.log('🧹 开始清理测试数据...');
  console.log('');

  const result = TestDataCleaner.cleanupAll(options);
  TestDataCleaner.printCleanupResult(result, options);
}
