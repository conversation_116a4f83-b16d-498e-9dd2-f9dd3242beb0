// 添加测试商品数据
const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));

const testProduct = {
  productId: '709107945753',
  title: '337晨读法小学生晨读美文每日一读一二三四五六年级晨诵晚读100篇',
  basePrice: 48,
  limitPrice: 30,
  purchaseDiscount: 8,
  supplierName: '教育图书供应商',
  skus: []
};

async function addTestProduct() {
  console.log('🧪 添加测试商品数据...');
  
  try {
    const response = await fetch('http://localhost:3001/api/products/create', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(testProduct)
    });

    const result = await response.json();
    
    if (result.success) {
      console.log('✅ 测试商品添加成功');
      console.log(`   - 商品ID: ${testProduct.productId}`);
      console.log(`   - 商品标题: ${testProduct.title}`);
      console.log(`   - 定价: ¥${testProduct.basePrice}`);
      console.log(`   - 限价: ¥${testProduct.limitPrice}`);
      console.log(`   - 进货折扣: ${testProduct.purchaseDiscount}折`);
      console.log(`   - 供应商: ${testProduct.supplierName}`);
    } else {
      console.error('❌ 添加失败:', result.error);
    }
  } catch (error) {
    console.error('❌ 请求失败:', error.message);
  }
}

// 运行测试
addTestProduct();
