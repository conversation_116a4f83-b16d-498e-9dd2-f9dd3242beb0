/**
 * API模拟对象
 * 提供测试中使用的API响应模拟
 */

import { NextApiRequest, NextApiResponse } from 'next';

/**
 * 模拟成功的API响应
 */
export function mockSuccessResponse<T>(data: T) {
  return {
    success: true,
    data,
    message: '操作成功'
  };
}

/**
 * 模拟错误的API响应
 */
export function mockErrorResponse(message: string, code?: string) {
  return {
    success: false,
    error: {
      message,
      code: code || 'UNKNOWN_ERROR'
    }
  };
}

/**
 * 模拟文件上传响应
 */
export function mockUploadResponse(fileId: string, fileName: string) {
  return mockSuccessResponse({
    fileId,
    fileName,
    uploadTime: new Date().toISOString(),
    size: 1024
  });
}

/**
 * 模拟批量任务响应
 */
export function mockBatchTaskResponse(taskId: string, status: string = 'pending') {
  return mockSuccessResponse({
    taskId,
    status,
    createdAt: new Date().toISOString(),
    progress: 0,
    total: 100
  });
}

/**
 * 模拟产品列表响应
 */
export function mockProductListResponse(count: number = 5) {
  const products = Array.from({ length: count }, (_, index) => ({
    id: `product-${index + 1}`,
    name: `测试商品${index + 1}`,
    price: (index + 1) * 100,
    category: '测试分类',
    status: 'active'
  }));
  
  return mockSuccessResponse({
    products,
    total: count,
    page: 1,
    pageSize: 10
  });
}

/**
 * 模拟模板生成响应
 */
export function mockTemplateGenerateResponse(templateId: string) {
  return mockSuccessResponse({
    templateId,
    fileName: `template-${templateId}.xlsx`,
    downloadUrl: `/api/templates/download/${templateId}`,
    generatedAt: new Date().toISOString()
  });
}

/**
 * 创建模拟的NextApiRequest
 */
export function createMockRequest(options: {
  method?: string;
  query?: Record<string, string | string[]>;
  body?: any;
  headers?: Record<string, string>;
}): Partial<NextApiRequest> {
  return {
    method: options.method || 'GET',
    query: options.query || {},
    body: options.body,
    headers: options.headers || {}
  };
}

/**
 * 创建模拟的NextApiResponse
 */
export function createMockResponse(): {
  res: Partial<NextApiResponse>;
  getResponseData: () => any;
} {
  let statusCode = 200;
  let responseData: any;
  
  const res: Partial<NextApiResponse> = {
    status: jest.fn((code: number) => {
      statusCode = code;
      return res as NextApiResponse;
    }),
    json: jest.fn((data: any) => {
      responseData = data;
      return res as NextApiResponse;
    }),
    end: jest.fn(),
    setHeader: jest.fn()
  };
  
  return {
    res,
    getResponseData: () => ({ statusCode, data: responseData })
  };
}

/**
 * 模拟fetch响应
 */
export function mockFetchResponse<T>(data: T, status: number = 200) {
  return Promise.resolve({
    ok: status >= 200 && status < 300,
    status,
    json: () => Promise.resolve(data),
    text: () => Promise.resolve(JSON.stringify(data))
  });
}

/**
 * 模拟WebSocket连接
 */
export function createMockWebSocket() {
  const listeners: Record<string, Function[]> = {};
  
  return {
    on: jest.fn((event: string, callback: Function) => {
      if (!listeners[event]) {
        listeners[event] = [];
      }
      listeners[event].push(callback);
    }),
    emit: jest.fn((event: string, data?: any) => {
      if (listeners[event]) {
        listeners[event].forEach(callback => callback(data));
      }
    }),
    disconnect: jest.fn(),
    connected: true
  };
}
