# 测试快速开始指南

这是一个快速上手指南，帮助开发者快速了解和使用项目的测试系统。

## 🚀 快速开始

### 1. 运行第一个测试

```bash
# 运行所有单元测试
npm run test:unit

# 运行特定测试文件
npm test -- tests/unit/lib/time-parsing.test.ts

# 监视模式运行测试
npm run test:watch
```

### 2. 查看测试覆盖率

```bash
# 生成覆盖率报告
npm run test:coverage

# 在浏览器中查看详细报告
open coverage/lcov-report/index.html
```

### 3. 运行集成测试

```bash
# 确保服务器运行在 localhost:3001
npm run dev

# 在另一个终端运行集成测试
npm run test:integration
```

## 📝 编写你的第一个测试

### 单元测试示例

```typescript
// tests/unit/utils/my-function.test.ts
import { myFunction } from '../../../src/utils/my-function';

describe('myFunction', () => {
  it('应该返回正确的结果', () => {
    // Arrange (准备)
    const input = 'test input';
    
    // Act (执行)
    const result = myFunction(input);
    
    // Assert (断言)
    expect(result).toBe('expected output');
  });
});
```

### 集成测试示例

```typescript
// tests/integration/api/my-endpoint.integration.test.ts
describe('My API Endpoint', () => {
  it('应该成功处理请求', async () => {
    const response = await fetch('/api/my-endpoint', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ data: 'test' })
    });
    
    expect(response.ok).toBe(true);
    const result = await response.json();
    expect(result.success).toBe(true);
  });
});
```

## 🛠️ 常用工具

### 生成测试模板

```bash
# 生成所有预定义的测试模板
npm run test:generate-templates

# 查看生成的模板
ls tests/fixtures/templates/
```

### 调试测试模板

```bash
# 调试所有模板文件
npm run test:debug-templates

# 调试特定模板
npm run test:debug-templates -- path/to/template.xlsx
```

### 清理测试数据

```bash
# 预览要清理的文件
npm run test:cleanup:dry

# 执行清理
npm run test:cleanup
```

## 🔧 测试配置

### 环境变量

在 `.env.test` 文件中设置测试环境变量：

```bash
NODE_ENV=test
DATABASE_URL=file:./test.db
VERBOSE_TESTS=false
```

### Jest配置

主要配置在 `jest.config.js` 中：

```javascript
module.exports = {
  // 测试项目配置
  projects: [
    {
      displayName: 'unit',
      testMatch: ['<rootDir>/tests/unit/**/*.test.{js,ts,tsx}']
    },
    // ...
  ]
}
```

## 📊 测试报告

### 覆盖率报告

运行 `npm run test:coverage` 后，可以在以下位置查看报告：

- **控制台输出**: 简要覆盖率统计
- **HTML报告**: `coverage/lcov-report/index.html`
- **LCOV文件**: `coverage/lcov.info`

### 测试结果

测试运行后会显示：

```
✅ 通过的测试数量
❌ 失败的测试数量
⏱️ 运行时间
📊 覆盖率统计
```

## 🐛 常见问题

### Q: 测试运行很慢怎么办？

A: 尝试以下方法：
- 只运行单元测试: `npm run test:unit`
- 运行特定文件: `npm test -- path/to/test.ts`
- 使用监视模式: `npm run test:watch`

### Q: 模拟不工作怎么办？

A: 检查以下几点：
- 确保在 `beforeEach` 中清理模拟
- 检查模拟的导入路径
- 查看 `jest.setup.js` 中的全局模拟

### Q: 测试数据污染怎么解决？

A: 确保：
- 每个测试后清理数据
- 使用独立的测试数据库
- 避免测试间共享状态

### Q: 如何调试失败的测试？

A: 使用以下方法：
- 添加 `console.log` 输出
- 设置 `VERBOSE_TESTS=true`
- 运行单个测试文件
- 使用 `--verbose` 标志

## 📚 进一步学习

- 📖 [完整测试文档](../TESTING.md)
- 📁 [测试工具文档](./tools/README.md)
- 📊 [测试数据管理](./fixtures/README.md)
- 🔧 [Jest官方文档](https://jestjs.io/docs/getting-started)

## 💡 最佳实践提醒

1. **先写测试，再写代码** (TDD)
2. **保持测试简单和专注**
3. **使用描述性的测试名称**
4. **定期运行测试**
5. **维护测试覆盖率**

---

🎉 **恭喜！** 你已经掌握了基本的测试使用方法。开始编写你的第一个测试吧！
