// 创建测试用的Excel模板
const XLSX = require('xlsx');

// 创建测试数据
const testData = [
  // 表头
  ['商品ID', '活动类型', '时间模式', '开始日期', '结束日期', '每日开始时间', '每日结束时间', '活动区域', '满几件', '折扣', '周末全天', '节假日全天'],
  // 测试数据
  ['PROD001', '多件多折', '连续', '2024-01-01', '2024-01-07', '09:00:00', '21:00:00', '全国', '2', '8', '是', '是'],
  ['PROD002', '多件多折', '连续', '2024-01-01', '2024-01-07', '10:00:00', '22:00:00', '全国', '3', '7', '否', '是'],
  ['PROD003', '多件多折', '连续', '2024-01-01', '2024-01-07', '08:00:00', '20:00:00', '全国', '2', '9', '是', '否'],
  ['PROD004', '多件多折', '连续', '2024-01-01', '2024-01-07', '09:30:00', '21:30:00', '全国', '4', '6', '否', '否'],
  ['PROD005', '多件多折', '连续', '2024-01-01', '2024-01-07', '11:00:00', '23:00:00', '全国', '2', '8', '是', '是']
];

// 创建工作簿
const workbook = XLSX.utils.book_new();
const worksheet = XLSX.utils.aoa_to_sheet(testData);

// 设置列宽
worksheet['!cols'] = [
  { wch: 12 }, // 商品ID
  { wch: 12 }, // 活动类型
  { wch: 10 }, // 时间模式
  { wch: 12 }, // 开始日期
  { wch: 12 }, // 结束日期
  { wch: 12 }, // 每日开始时间
  { wch: 12 }, // 每日结束时间
  { wch: 10 }, // 活动区域
  { wch: 8 },  // 满几件
  { wch: 8 },  // 折扣
  { wch: 10 }, // 周末全天
  { wch: 10 }  // 节假日全天
];

// 添加工作表
XLSX.utils.book_append_sheet(workbook, worksheet, '批量时间生成模板');

// 保存文件
XLSX.writeFile(workbook, '测试批量时间生成模板.xlsx');

console.log('测试模板文件已创建: 测试批量时间生成模板.xlsx');
