// 测试日期解析修复
const XLSX = require('xlsx');
const path = require('path');

function testDateParsing() {
  console.log('🔍 测试日期解析修复...');
  
  try {
    // 读取测试模板
    const templatePath = path.join(__dirname, '正确格式批量生成模板.xlsx');
    console.log('📁 模板路径:', templatePath);
    
    const workbook = XLSX.readFile(templatePath);
    const sheetName = workbook.SheetNames[0];
    console.log('📊 工作表名:', sheetName);
    
    const worksheet = workbook.Sheets[sheetName];
    
    // 使用默认设置读取（与修复后的API一致）
    const jsonData = XLSX.utils.sheet_to_json(worksheet);
    
    console.log('📋 原始解析数据:');
    console.log('总行数:', jsonData.length);
    console.log('');
    
    // 处理日期字段的转换（与修复后的API一致）
    const parsedData = jsonData.map((row, index) => {
      const processedRow = { ...row }
      
      console.log(`第${index + 1}行原始数据:`);
      console.log('  开始日期原始值:', row['开始日期'], '类型:', typeof row['开始日期']);
      console.log('  结束日期原始值:', row['结束日期'], '类型:', typeof row['结束日期']);
      
      // 处理日期字段
      const dateFields = ['开始日期', '结束日期']
      dateFields.forEach(field => {
        if (processedRow[field] !== undefined && processedRow[field] !== '') {
          const value = processedRow[field]
          
          // 如果是数字（Excel日期序列号），转换为日期
          if (typeof value === 'number') {
            console.log(`  ${field} 是数字，进行转换...`);
            // Excel日期从1900年1月1日开始计算（但有1900年闰年bug，实际从1899年12月30日开始）
            const excelEpoch = new Date(1899, 11, 30) // 1899年12月30日
            const jsDate = new Date(excelEpoch.getTime() + value * 24 * 60 * 60 * 1000)
            
            if (!isNaN(jsDate.getTime())) {
              const year = jsDate.getFullYear()
              const month = String(jsDate.getMonth() + 1).padStart(2, '0')
              const day = String(jsDate.getDate()).padStart(2, '0')
              processedRow[field] = `${year}-${month}-${day}`
              console.log(`  ${field} 转换结果:`, processedRow[field]);
            } else {
              console.log(`  ${field} 转换失败`);
            }
          }
          // 如果是字符串日期，尝试标准化格式
          else if (typeof value === 'string') {
            console.log(`  ${field} 是字符串，尝试解析...`);
            try {
              const date = new Date(value)
              if (!isNaN(date.getTime())) {
                const year = date.getFullYear()
                const month = String(date.getMonth() + 1).padStart(2, '0')
                const day = String(date.getDate()).padStart(2, '0')
                processedRow[field] = `${year}-${month}-${day}`
                console.log(`  ${field} 解析结果:`, processedRow[field]);
              } else {
                console.log(`  ${field} 解析失败，无效日期`);
              }
            } catch (error) {
              console.log(`  ${field} 解析异常:`, error.message);
            }
          }
        }
      })
      
      console.log(`  处理后的开始日期:`, processedRow['开始日期']);
      console.log(`  处理后的结束日期:`, processedRow['结束日期']);
      console.log('---');
      
      return processedRow
    }).filter(row => {
      // 过滤空行
      return Object.values(row).some(value => value && value.toString().trim() !== '')
    })
    
    console.log('🎯 最终处理结果:');
    parsedData.forEach((row, index) => {
      console.log(`第${index + 1}行:`);
      console.log('  商品ID:', row['商品ID']);
      console.log('  开始日期:', row['开始日期']);
      console.log('  结束日期:', row['结束日期']);
      console.log('  活动区域:', row['活动区域']);
      console.log('  时间模式:', row['时间模式']);
    });
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
  }
}

// 运行测试
testDateParsing();
