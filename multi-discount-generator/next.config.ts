import type { NextConfig } from 'next'
// @ts-ignore
import withPWA from 'next-pwa'

const nextConfig: NextConfig = {
  webpack: (config, { isServer }) => {
    // 处理Node.js模块在客户端的兼容性问题
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        net: false,
        tls: false,
        crypto: false,
        stream: false,
        url: false,
        zlib: false,
        http: false,
        https: false,
        assert: false,
        os: false,
        path: false,
      }
    }
    return config
  },
  // 移动到正确的位置
  serverExternalPackages: ['chinese-holidays'],
  // 改进错误处理
  onDemandEntries: {
    // 页面在开发中保持活跃的时间
    maxInactiveAge: 25 * 1000,
    // 同时保持活跃的页面数
    pagesBufferLength: 2,
  },
  // 改进开发体验
  reactStrictMode: true
}

export default withPWA({
  dest: 'public',
  register: true,
  skipWaiting: true,
  disable: process.env.NODE_ENV === 'development',
})(nextConfig)
