'use client'

import { AdminLayout } from '@/components/admin/AdminLayout'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'

export default function AdminSettingsPage() {
  return (
    <AdminLayout>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <h2 className="text-xl font-semibold text-gray-900">系统设置</h2>
          <div className="flex space-x-2">
            <Button variant="outline">重置</Button>
            <Button>保存设置</Button>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card className="p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">系统配置</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  系统名称
                </label>
                <Input 
                  defaultValue="多件多折模板生成器"
                  placeholder="请输入系统名称"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  系统版本
                </label>
                <Input 
                  defaultValue="v1.0.0"
                  placeholder="请输入系统版本"
                  disabled
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  管理员邮箱
                </label>
                <Input 
                  type="email"
                  placeholder="请输入管理员邮箱"
                />
              </div>
            </div>
          </Card>

          <Card className="p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">文件配置</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  最大文件大小 (MB)
                </label>
                <Input 
                  type="number"
                  defaultValue="10"
                  placeholder="请输入最大文件大小"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  最大商品数量
                </label>
                <Input 
                  type="number"
                  defaultValue="1000"
                  placeholder="请输入最大商品数量"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  文件保留天数
                </label>
                <Input 
                  type="number"
                  defaultValue="7"
                  placeholder="请输入文件保留天数"
                />
              </div>
            </div>
          </Card>

          <Card className="p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">任务配置</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  并发任务数
                </label>
                <Input 
                  type="number"
                  defaultValue="5"
                  placeholder="请输入并发任务数"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  任务超时时间 (分钟)
                </label>
                <Input 
                  type="number"
                  defaultValue="30"
                  placeholder="请输入任务超时时间"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  重试次数
                </label>
                <Input 
                  type="number"
                  defaultValue="3"
                  placeholder="请输入重试次数"
                />
              </div>
            </div>
          </Card>

          <Card className="p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">监控配置</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  日志保留天数
                </label>
                <Input 
                  type="number"
                  defaultValue="30"
                  placeholder="请输入日志保留天数"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  监控刷新间隔 (秒)
                </label>
                <Input 
                  type="number"
                  defaultValue="30"
                  placeholder="请输入监控刷新间隔"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  告警阈值 (%)
                </label>
                <Input 
                  type="number"
                  defaultValue="80"
                  placeholder="请输入告警阈值"
                />
              </div>
            </div>
          </Card>
        </div>

        <Card className="p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">系统维护</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Button variant="outline" className="w-full">
              清理临时文件
            </Button>
            <Button variant="outline" className="w-full">
              清理过期日志
            </Button>
            <Button variant="outline" className="w-full">
              重启系统服务
            </Button>
          </div>
        </Card>

        <Card className="p-6 bg-yellow-50 border-yellow-200">
          <div className="flex items-start space-x-3">
            <div className="w-5 h-5 text-yellow-600 mt-0.5">
              <svg fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            </div>
            <div>
              <h4 className="text-sm font-medium text-yellow-800">注意事项</h4>
              <p className="text-sm text-yellow-700 mt-1">
                修改系统配置可能会影响系统性能和稳定性，请谨慎操作。建议在非业务高峰期进行配置变更。
              </p>
            </div>
          </div>
        </Card>
      </div>
    </AdminLayout>
  )
}