'use client'

import { AdminLayout } from '@/components/admin/AdminLayout'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'

export default function AdminFilesPage() {
  return (
    <AdminLayout>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <h2 className="text-xl font-semibold text-gray-900">文件管理</h2>
          <div className="flex space-x-2">
            <Button variant="outline">刷新</Button>
            <Button variant="outline">清理临时文件</Button>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card className="p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">上传文件</h3>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>文件数量</span>
                <span className="font-medium">0</span>
              </div>
              <div className="flex justify-between text-sm">
                <span>总大小</span>
                <span className="font-medium">0 MB</span>
              </div>
            </div>
          </Card>

          <Card className="p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">输出文件</h3>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>文件数量</span>
                <span className="font-medium">0</span>
              </div>
              <div className="flex justify-between text-sm">
                <span>总大小</span>
                <span className="font-medium">0 MB</span>
              </div>
            </div>
          </Card>

          <Card className="p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">临时文件</h3>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>文件数量</span>
                <span className="font-medium">0</span>
              </div>
              <div className="flex justify-between text-sm">
                <span>总大小</span>
                <span className="font-medium">0 MB</span>
              </div>
            </div>
          </Card>
        </div>

        <Card className="p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">文件列表</h3>
          <div className="text-center py-8 text-gray-500">
            <div className="mb-4">
              <svg className="w-16 h-16 mx-auto text-gray-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">暂无文件</h3>
            <p className="text-gray-600">还没有上传任何文件</p>
          </div>
        </Card>
      </div>
    </AdminLayout>
  )
}