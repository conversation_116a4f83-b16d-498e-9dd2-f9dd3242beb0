import { TaskDetail } from '@/components/tasks'

interface TaskDetailPageProps {
  params: {
    id: string
  }
}

export default function TaskDetailPage({ params }: TaskDetailPageProps) {
  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-6xl mx-auto">
        <TaskDetail taskId={params.id} />
      </div>
    </div>
  )
}

export const metadata = {
  title: '任务详情 - 多件多折模板生成器',
  description: '查看任务详细信息和执行状态'
}