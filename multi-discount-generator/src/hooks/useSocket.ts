import { useEffect, useRef, useState, useCallback } from 'react'
import { io, Socket } from 'socket.io-client'
import type { 
  TaskProgress,
  SystemMetrics,
  TaskWithRelations 
} from '@/types/batch'

interface SocketState {
  connected: boolean
  connecting: boolean
  error: string | null
}

interface TaskStatusData {
  taskId: string
  progress?: TaskProgress
  task?: Partial<TaskWithRelations>
  timestamp: Date
}

interface UseSocketReturn {
  socket: Socket | null
  state: SocketState
  joinTask: (taskId: string) => void
  leaveTask: (taskId: string) => void
  getTaskStatus: (taskId: string) => void
  getSystemMetrics: () => void
}

/**
 * WebSocket连接钩子
 */
export function useSocket(): UseSocketReturn {
  const socketRef = useRef<Socket | null>(null)
  const [state, setState] = useState<SocketState>({
    connected: false,
    connecting: false,
    error: null
  })

  // 初始化连接
  useEffect(() => {
    if (socketRef.current) return
    if (typeof window === 'undefined') return // 只在客户端初始化

    // 暂时禁用 WebSocket 连接以避免错误
    // TODO: 启用 Socket.IO 服务器后移除这个检查
    if (process.env.NODE_ENV === 'development') {
      console.log('WebSocket 连接已禁用（开发模式）')
      setState({
        connected: false,
        connecting: false,
        error: null
      })
      return
    }

    setState(prev => ({ ...prev, connecting: true, error: null }))

    const socket = io({
      transports: ['polling', 'websocket'], // 优先使用 polling，更稳定
      timeout: 20000, // 增加超时时间
      reconnection: true,
      reconnectionAttempts: 10, // 增加重连次数
      reconnectionDelay: 2000, // 增加重连延迟
      reconnectionDelayMax: 10000, // 最大重连延迟
      maxReconnectionAttempts: 10,
      forceNew: true
    })

    socketRef.current = socket

    // 连接事件
    socket.on('connect', () => {
      console.log('WebSocket连接成功')
      setState({
        connected: true,
        connecting: false,
        error: null
      })
    })

    socket.on('disconnect', (reason) => {
      console.log('WebSocket连接断开:', reason)
      setState(prev => ({
        ...prev,
        connected: false,
        connecting: false
      }))
    })

    socket.on('connect_error', (error) => {
      console.error('WebSocket连接错误:', error)
      setState({
        connected: false,
        connecting: false,
        error: `连接失败: ${error.message || '网络错误'}`
      })
    })

    socket.on('reconnect', (attemptNumber) => {
      console.log(`WebSocket重连成功 (第${attemptNumber}次尝试)`)
      setState({
        connected: true,
        connecting: false,
        error: null
      })
    })

    socket.on('reconnect_error', (error) => {
      console.error('WebSocket重连失败:', error)
      setState(prev => ({
        ...prev,
        error: `重连失败: ${error.message || '网络错误'}`
      }))
    })

    socket.on('reconnect_failed', () => {
      console.error('WebSocket重连彻底失败')
      setState(prev => ({
        ...prev,
        connected: false,
        connecting: false,
        error: '连接失败，请刷新页面重试'
      }))
    })

    // 清理函数
    return () => {
      if (socketRef.current) {
        socketRef.current.disconnect()
        socketRef.current = null
      }
    }
  }, [])

  // 加入任务房间
  const joinTask = useCallback((taskId: string) => {
    if (socketRef.current && state.connected) {
      socketRef.current.emit('joinTask', taskId)
    }
  }, [state.connected])

  // 离开任务房间
  const leaveTask = useCallback((taskId: string) => {
    if (socketRef.current && state.connected) {
      socketRef.current.emit('leaveTask', taskId)
    }
  }, [state.connected])

  // 获取任务状态
  const getTaskStatus = useCallback((taskId: string) => {
    if (socketRef.current && state.connected) {
      socketRef.current.emit('getTaskStatus', taskId)
    }
  }, [state.connected])

  // 获取系统指标
  const getSystemMetrics = useCallback(() => {
    if (socketRef.current && state.connected) {
      socketRef.current.emit('getSystemMetrics')
    }
  }, [state.connected])

  return {
    socket: socketRef.current,
    state,
    joinTask,
    leaveTask,
    getTaskStatus,
    getSystemMetrics
  }
}

/**
 * 任务状态监听钩子
 */
export function useTaskStatus(taskId: string | null) {
  const { socket, joinTask, leaveTask } = useSocket()
  const [taskStatus, setTaskStatus] = useState<TaskStatusData | null>(null)
  const [progress, setProgress] = useState<TaskProgress | null>(null)

  useEffect(() => {
    if (!socket || !taskId) return

    // 加入任务房间
    joinTask(taskId)

    // 监听任务状态更新
    const handleTaskStatus = (data: TaskStatusData) => {
      if (data.taskId === taskId) {
        setTaskStatus(data)
        if (data.progress) {
          setProgress(data.progress)
        }
      }
    }

    const handleTaskProgress = (data: any) => {
      if (data.taskId === taskId) {
        setProgress({
          taskId: data.taskId,
          totalItems: data.totalItems,
          processedItems: data.processedItems,
          failedItems: data.failedItems || 0,
          progress: data.progress,
          currentProduct: data.currentProduct,
          estimatedTimeRemaining: data.estimatedTimeRemaining
        })
      }
    }

    const handleTaskCompleted = (data: any) => {
      if (data.taskId === taskId) {
        setProgress(prev => prev ? {
          ...prev,
          progress: 100,
          processedItems: data.totalItems || prev.totalItems,
          estimatedTimeRemaining: 0
        } : null)
      }
    }

    const handleTaskFailed = (data: any) => {
      if (data.taskId === taskId) {
        setProgress(prev => prev ? {
          ...prev,
          estimatedTimeRemaining: 0
        } : null)
      }
    }

    socket.on('taskStatus', handleTaskStatus)
    socket.on('taskProgress', handleTaskProgress)
    socket.on('taskCompleted', handleTaskCompleted)
    socket.on('taskFailed', handleTaskFailed)

    // 清理函数
    return () => {
      socket.off('taskStatus', handleTaskStatus)
      socket.off('taskProgress', handleTaskProgress)
      socket.off('taskCompleted', handleTaskCompleted)
      socket.off('taskFailed', handleTaskFailed)
      leaveTask(taskId)
    }
  }, [socket, taskId, joinTask, leaveTask])

  return {
    taskStatus,
    progress
  }
}

/**
 * 系统指标监听钩子
 */
export function useSystemMetrics() {
  const { socket, getSystemMetrics } = useSocket()
  const [metrics, setMetrics] = useState<SystemMetrics | null>(null)
  const [queueStatus, setQueueStatus] = useState<any>(null)

  useEffect(() => {
    if (!socket) return

    const handleSystemMetrics = (data: any) => {
      setMetrics(data.metrics)
      setQueueStatus(data.queueStatus)
    }

    const handleSystemMetricsUpdate = (data: any) => {
      setMetrics(data.metrics)
    }

    socket.on('systemMetrics', handleSystemMetrics)
    socket.on('systemMetricsUpdate', handleSystemMetricsUpdate)

    // 初始获取指标
    getSystemMetrics()

    // 定期更新指标
    const interval = setInterval(() => {
      getSystemMetrics()
    }, 30000) // 30秒更新一次

    return () => {
      socket.off('systemMetrics', handleSystemMetrics)
      socket.off('systemMetricsUpdate', handleSystemMetricsUpdate)
      clearInterval(interval)
    }
  }, [socket, getSystemMetrics])

  return {
    metrics,
    queueStatus,
    refresh: getSystemMetrics
  }
}

/**
 * 通知监听钩子
 */
export function useNotifications() {
  const { socket, state } = useSocket()
  const [notifications, setNotifications] = useState<any[]>([])

  useEffect(() => {
    if (!socket || !state.connected) return

    const handleNotification = (notification: any) => {
      setNotifications(prev => [notification, ...prev].slice(0, 50)) // 保留最近50条
    }

    const handleTaskNotification = (notification: any) => {
      setNotifications(prev => [notification, ...prev].slice(0, 50))
    }

    socket.on('notification', handleNotification)
    socket.on('taskNotification', handleTaskNotification)

    return () => {
      socket.off('notification', handleNotification)
      socket.off('taskNotification', handleTaskNotification)
    }
  }, [socket])

  const clearNotifications = useCallback(() => {
    setNotifications([])
  }, [])

  const removeNotification = useCallback((index: number) => {
    setNotifications(prev => prev.filter((_, i) => i !== index))
  }, [])

  return {
    notifications,
    clearNotifications,
    removeNotification
  }
}