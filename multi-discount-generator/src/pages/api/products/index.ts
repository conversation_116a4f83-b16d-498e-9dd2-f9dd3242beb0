import type { NextApiRequest, NextApiResponse } from 'next'
import { prisma } from '@/lib/prisma'
import type { ProductListResponse, ProductSearchParams } from '@/types/product'
import type { ApiResponse } from '@/types/api'

/**
 * 商品列表查询API
 * GET /api/products - 获取商品列表
 */
export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<ApiResponse<ProductListResponse>>
) {
  if (req.method !== 'GET') {
    return res.status(405).json({
      success: false,
      error: '方法不允许'
    })
  }

  try {
    const {
      page = '1',
      pageSize = '20',
      search = '',
      activityType,
      supplierId,
      isActive
    } = req.query as Partial<ProductSearchParams & { [key: string]: string }>

    const pageNum = parseInt(page)
    const pageSizeNum = parseInt(pageSize)
    const skip = (pageNum - 1) * pageSizeNum

    // 构建查询条件
    const where: any = {}

    // 搜索条件
    if (search) {
      where.OR = [
        { productId: { contains: search } },
        { title: { contains: search } },
        { supplier: { name: { contains: search } } }
      ]
    }

    // 活动类型筛选
    if (activityType) {
      where.activityType = activityType
    }

    // 供应商筛选
    if (supplierId) {
      where.supplierId = supplierId
    }

    // 获取商品列表
    const [products, total] = await Promise.all([
      prisma.productInfo.findMany({
        where,
        include: {
          skus: {
            where: { isActive: true }
          },
          supplier: true
        },
        orderBy: { createdAt: 'desc' },
        skip,
        take: pageSizeNum
      }),
      prisma.productInfo.count({ where })
    ])

    return res.status(200).json({
      success: true,
      data: {
        products,
        total,
        page: pageNum,
        pageSize: pageSizeNum
      }
    })

  } catch (error) {
    console.error('获取商品列表失败:', error)
    return res.status(500).json({
      success: false,
      error: '获取商品列表失败'
    })
  }
}
