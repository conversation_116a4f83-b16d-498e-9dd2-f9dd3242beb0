import type { NextApiRequest, NextApiResponse } from 'next'
import { prisma } from '@/lib/prisma'
import { ExcelService } from '@/lib/excel'
import type { ProductImportResult } from '@/types/product'
import type { ApiResponse } from '@/types/api'

/**
 * 商品批量导入API
 * POST /api/products/import
 */
export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<ApiResponse<ProductImportResult>>
) {
  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      error: '方法不允许'
    })
  }

  try {
    const { filename } = req.body

    if (!filename) {
      return res.status(400).json({
        success: false,
        error: '文件名不能为空'
      })
    }

    // 读取Excel文件数据
    const filePath = `uploads/input/${filename}`
    const excelData = await ExcelService.readProductDataFromFile(filePath)

    if (!excelData || excelData.length === 0) {
      return res.status(400).json({
        success: false,
        error: '文件中没有有效数据'
      })
    }

    const result: ProductImportResult = {
      success: true,
      imported: 0,
      failed: 0,
      errors: [],
      duplicates: []
    }

    // 按商品ID分组数据
    const productGroups = new Map<string, any[]>()
    
    for (const row of excelData) {
      const productId = row['商品ID'] || row['商品ID*']
      if (!productId) continue
      
      if (!productGroups.has(productId)) {
        productGroups.set(productId, [])
      }
      productGroups.get(productId)!.push(row)
    }

    // 处理每个商品
    for (const [productId, rows] of productGroups) {
      try {
        const firstRow = rows[0]
        
        // 验证必填字段
        const title = firstRow['商品标题'] || firstRow['商品标题*']
        const basePrice = parseFloat(firstRow['商品定价'] || firstRow['商品定价*'])
        
        if (!title || isNaN(basePrice)) {
          result.errors.push(`商品 ${productId}: 缺少必填字段`)
          result.failed++
          continue
        }

        // 检查商品是否已存在
        const existingProduct = await prisma.productInfo.findUnique({
          where: { productId }
        })

        if (existingProduct) {
          result.duplicates.push(productId)
          continue
        }

        // 解析商品数据
        const limitPrice = firstRow['限价价格'] ? parseFloat(firstRow['限价价格']) : undefined
        const purchaseDiscount = firstRow['进货折扣'] ? (parseFloat(firstRow['进货折扣']) / 10) : undefined // 转换为小数
        const activityType = firstRow['活动形式'] || '多件多折'
        const supplierName = firstRow['供应商']
        
        // 解析活动时间
        const activityStartTime = firstRow['活动开始时间'] ? new Date(firstRow['活动开始时间']) : undefined
        const activityEndTime = firstRow['活动结束时间'] ? new Date(firstRow['活动结束时间']) : undefined

        // 查找供应商
        let supplierId: string | undefined
        if (supplierCode) {
          const supplier = await prisma.supplier.findUnique({
            where: { code: supplierCode }
          })
          supplierId = supplier?.id
        }

        // 解析SKU数据
        const skus = rows.map(row => ({
          skuId: row['SKU ID'] || row['SKU ID*'],
          skuName: row['SKU名称'],
          price: row['SKU价格'] ? parseFloat(row['SKU价格']) : undefined,
          stock: row['库存数量'] ? parseInt(row['库存数量']) : undefined
        })).filter(sku => sku.skuId)

        if (skus.length === 0) {
          result.errors.push(`商品 ${productId}: 没有有效的SKU数据`)
          result.failed++
          continue
        }

        // 检查SKU ID重复
        const skuIds = skus.map(sku => sku.skuId)
        const uniqueSkuIds = new Set(skuIds)
        if (skuIds.length !== uniqueSkuIds.size) {
          result.errors.push(`商品 ${productId}: SKU ID重复`)
          result.failed++
          continue
        }

        // 检查SKU ID是否已存在
        const existingSkus = await prisma.productSku.findMany({
          where: { skuId: { in: skuIds } }
        })

        if (existingSkus.length > 0) {
          result.errors.push(`商品 ${productId}: SKU ID已存在 - ${existingSkus.map(s => s.skuId).join(', ')}`)
          result.failed++
          continue
        }

        // 使用事务创建商品和SKU
        await prisma.$transaction(async (tx) => {
          // 创建商品
          const product = await tx.productInfo.create({
            data: {
              productId,
              title,
              basePrice,
              limitPrice,
              purchaseDiscount,
              activityType: activityType as any,
              activityStartTime,
              activityEndTime,
              supplierId
            }
          })

          // 创建SKU
          await Promise.all(
            skus.map(skuData =>
              tx.productSku.create({
                data: {
                  skuId: skuData.skuId,
                  productId: product.productId,
                  skuName: skuData.skuName,
                  price: skuData.price,
                  stock: skuData.stock,
                  isActive: true
                }
              })
            )
          )
        })

        result.imported++

      } catch (error) {
        console.error(`导入商品 ${productId} 失败:`, error)
        result.errors.push(`商品 ${productId}: ${error instanceof Error ? error.message : '导入失败'}`)
        result.failed++
      }
    }

    return res.status(200).json({
      success: true,
      data: result
    })

  } catch (error) {
    console.error('批量导入商品失败:', error)
    return res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : '批量导入失败'
    })
  }
}

export const config = {
  api: {
    bodyParser: {
      sizeLimit: '10mb'
    }
  }
}
