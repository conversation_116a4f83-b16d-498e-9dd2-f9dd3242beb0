import type { NextApiRequest, NextApiResponse } from 'next'
import { prisma } from '@/lib/prisma'
import type { ProductFormData } from '@/types/product'
import type { ApiResponse } from '@/types/api'

/**
 * 商品详情API
 * GET /api/products/[id] - 获取商品详情
 * PUT /api/products/[id] - 更新商品信息
 * DELETE /api/products/[id] - 删除商品
 */
export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<ApiResponse<any>>
) {
  const { id } = req.query

  if (!id || typeof id !== 'string') {
    return res.status(400).json({
      success: false,
      error: '商品ID无效'
    })
  }

  if (req.method === 'GET') {
    try {
      const product = await prisma.productInfo.findUnique({
        where: { id },
        include: {
          skus: true,
          supplier: true
        }
      })

      if (!product) {
        return res.status(404).json({
          success: false,
          error: '商品不存在'
        })
      }

      return res.status(200).json({
        success: true,
        data: product
      })

    } catch (error) {
      console.error('获取商品详情失败:', error)
      return res.status(500).json({
        success: false,
        error: '获取商品详情失败'
      })
    }

  } else if (req.method === 'PUT') {
    try {
      const productData: ProductFormData = req.body

      // 验证必填字段
      if (!productData.productId || !productData.title || !productData.basePrice) {
        return res.status(400).json({
          success: false,
          error: '商品ID、标题和定价为必填字段'
        })
      }

      // 检查商品是否存在
      const existingProduct = await prisma.productInfo.findUnique({
        where: { id }
      })

      if (!existingProduct) {
        return res.status(404).json({
          success: false,
          error: '商品不存在'
        })
      }

      // 如果商品ID发生变化，检查新ID是否已存在
      if (productData.productId !== existingProduct.productId) {
        const duplicateProduct = await prisma.productInfo.findUnique({
          where: { productId: productData.productId }
        })

        if (duplicateProduct) {
          return res.status(400).json({
            success: false,
            error: '商品ID已存在'
          })
        }
      }

      // 验证SKU数据
      if (!productData.skus || productData.skus.length === 0) {
        return res.status(400).json({
          success: false,
          error: '至少需要一个SKU'
        })
      }

      // 检查SKU ID重复
      const skuIds = productData.skus.map(sku => sku.skuId).filter(id => id.trim())
      const uniqueSkuIds = new Set(skuIds)
      if (skuIds.length !== uniqueSkuIds.size) {
        return res.status(400).json({
          success: false,
          error: 'SKU ID不能重复'
        })
      }

      // 使用事务更新商品和SKU
      const result = await prisma.$transaction(async (tx) => {
        // 更新商品信息
        const updatedProduct = await tx.productInfo.update({
          where: { id },
          data: {
            productId: productData.productId,
            title: productData.title,
            basePrice: productData.basePrice,
            limitPrice: productData.limitPrice,
            purchaseDiscount: productData.purchaseDiscount,
            activityType: productData.activityType,
            activityStartTime: productData.activityStartTime,
            activityEndTime: productData.activityEndTime,
            supplierId: productData.supplierId
          }
        })

        // 删除现有SKU
        await tx.productSku.deleteMany({
          where: { productId: existingProduct.productId }
        })

        // 创建新的SKU
        const skus = await Promise.all(
          productData.skus.map(skuData =>
            tx.productSku.create({
              data: {
                skuId: skuData.skuId,
                productId: updatedProduct.productId,
                skuName: skuData.skuName,
                price: skuData.price,
                stock: skuData.stock,
                isActive: skuData.isActive
              }
            })
          )
        )

        return { product: updatedProduct, skus }
      })

      return res.status(200).json({
        success: true,
        data: result.product
      })

    } catch (error) {
      console.error('更新商品失败:', error)
      return res.status(500).json({
        success: false,
        error: error instanceof Error ? error.message : '更新商品失败'
      })
    }

  } else if (req.method === 'DELETE') {
    try {
      // 检查商品是否存在
      const existingProduct = await prisma.productInfo.findUnique({
        where: { id }
      })

      if (!existingProduct) {
        return res.status(404).json({
          success: false,
          error: '商品不存在'
        })
      }

      // 删除商品（级联删除SKU）
      await prisma.productInfo.delete({
        where: { id }
      })

      return res.status(200).json({
        success: true,
        data: { message: '商品删除成功' }
      })

    } catch (error) {
      console.error('删除商品失败:', error)
      return res.status(500).json({
        success: false,
        error: '删除商品失败'
      })
    }

  } else {
    return res.status(405).json({
      success: false,
      error: '方法不允许'
    })
  }
}

export const config = {
  api: {
    bodyParser: {
      sizeLimit: '1mb'
    }
  }
}
