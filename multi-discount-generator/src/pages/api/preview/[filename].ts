import type { NextApiRequest, NextApiResponse } from 'next'
import { join } from 'path'
import { FileUploadService } from '@/lib/upload'
import { ExcelService } from '@/lib/excel'
import type { ApiResponse } from '@/types/api'

/**
 * 文件预览API
 * GET /api/preview/[filename]
 */
export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<ApiResponse>
) {
  if (req.method !== 'GET') {
    return res.status(405).json({
      success: false,
      error: '方法不允许'
    })
  }

  try {
    const { filename } = req.query
    
    if (!filename || typeof filename !== 'string') {
      return res.status(400).json({
        success: false,
        error: '文件名参数无效'
      })
    }

    // 构建文件路径
    const config = FileUploadService.getConfig()
    const filePath = join(config.uploadDir, filename)

    // 检查文件是否存在
    const fileInfo = FileUploadService.getFileInfo(filePath)
    if (!fileInfo.exists) {
      return res.status(404).json({
        success: false,
        error: '文件不存在'
      })
    }

    // 获取查询参数
    const { 
      limit = '50', 
      offset = '0',
      type = 'products' 
    } = req.query

    const limitNum = parseInt(limit as string, 10)
    const offsetNum = parseInt(offset as string, 10)

    if (isNaN(limitNum) || limitNum < 1 || limitNum > 1000) {
      return res.status(400).json({
        success: false,
        error: '限制数量参数无效（1-1000）'
      })
    }

    if (isNaN(offsetNum) || offsetNum < 0) {
      return res.status(400).json({
        success: false,
        error: '偏移量参数无效'
      })
    }

    if (type === 'products') {
      // 预览商品ID列表
      const allProductIds = await FileUploadService.extractProductIds(filePath)
      const totalCount = allProductIds.length
      const productIds = allProductIds.slice(offsetNum, offsetNum + limitNum)

      // 生成预览统计
      const uniqueIds = new Set(allProductIds)
      const duplicateCount = allProductIds.length - uniqueIds.size
      
      // 检查格式错误
      const invalidIds = allProductIds.filter(id => 
        !id || !/^[A-Za-z0-9_-]+$/.test(id)
      )

      return res.status(200).json({
        success: true,
        data: {
          type: 'products',
          products: productIds,
          pagination: {
            total: totalCount,
            limit: limitNum,
            offset: offsetNum,
            hasMore: offsetNum + limitNum < totalCount
          },
          statistics: {
            totalCount,
            uniqueCount: uniqueIds.size,
            duplicateCount,
            invalidCount: invalidIds.length,
            validCount: totalCount - invalidIds.length
          },
          fileInfo: {
            filename,
            size: fileInfo.size,
            mtime: fileInfo.mtime,
            extension: fileInfo.extension
          }
        }
      })
    } else if (type === 'validation') {
      // 预览验证结果
      const productIds = await FileUploadService.extractProductIds(filePath)
      const validationResult = await FileUploadService.parseBatchFile(filePath)

      // 详细验证分析
      const uniqueIds = new Set(productIds.filter(id => id && id.trim().length > 0))
      const emptyIds = productIds.filter(id => !id || id.trim().length === 0)
      const invalidIds = productIds.filter(id => 
        id && id.trim().length > 0 && !/^[A-Za-z0-9_-]+$/.test(id.trim())
      )
      const tooLongIds = productIds.filter(id => id && id.length > 50)
      const duplicateCount = productIds.length - uniqueIds.size

      return res.status(200).json({
        success: true,
        data: {
          type: 'validation',
          validation: {
            isValid: validationResult.success,
            message: validationResult.message,
            errors: validationResult.errors || []
          },
          analysis: {
            totalCount: productIds.length,
            validCount: uniqueIds.size,
            emptyCount: emptyIds.length,
            invalidCount: invalidIds.length,
            duplicateCount,
            tooLongCount: tooLongIds.length
          },
          samples: {
            validIds: Array.from(uniqueIds).slice(0, 10),
            invalidIds: invalidIds.slice(0, 10),
            emptyPositions: emptyIds.map((_, index) => 
              productIds.indexOf('') + 1
            ).slice(0, 10)
          },
          fileInfo: {
            filename,
            size: fileInfo.size,
            mtime: fileInfo.mtime,
            extension: fileInfo.extension
          }
        }
      })
    } else {
      return res.status(400).json({
        success: false,
        error: '预览类型无效'
      })
    }

  } catch (error) {
    console.error('文件预览失败:', error)
    return res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : '文件预览失败'
    })
  }
}