import type { NextApiRequest, NextApiResponse } from 'next'
import { prisma } from '@/lib/prisma'
import * as XLSX from 'xlsx'
import type { ProductExportParams } from '@/types/product'

/**
 * 商品批量导出API
 * POST /api/export/products
 */
export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      error: '方法不允许'
    })
  }

  try {
    const {
      productIds,
      activityType,
      supplierId,
      format = 'excel',
      includeSkus = true
    }: ProductExportParams = req.body

    if (!productIds || productIds.length === 0) {
      return res.status(400).json({
        success: false,
        error: '请选择要导出的商品'
      })
    }

    // 构建查询条件
    const where: any = {
      id: { in: productIds }
    }

    if (activityType) {
      where.activityType = activityType
    }

    if (supplierId) {
      where.supplierId = supplierId
    }

    // 查询商品数据
    const products = await prisma.productInfo.findMany({
      where,
      include: {
        skus: includeSkus,
        supplier: true
      },
      orderBy: { createdAt: 'desc' }
    })

    if (products.length === 0) {
      return res.status(404).json({
        success: false,
        error: '没有找到符合条件的商品'
      })
    }

    // 准备导出数据
    const exportData = []

    for (const product of products) {
      if (includeSkus && product.skus && product.skus.length > 0) {
        // 包含SKU信息，每个SKU一行
        for (const sku of product.skus) {
          exportData.push({
            '商品ID': product.productId,
            '商品标题': product.title,
            '商品定价': product.basePrice,
            '限价价格': product.limitPrice || '',
            '进货折扣': product.purchaseDiscount || '',
            '活动形式': getActivityTypeLabel(product.activityType),
            '活动开始时间': product.activityStartTime ? 
              formatDateTime(product.activityStartTime) : '',
            '活动结束时间': product.activityEndTime ? 
              formatDateTime(product.activityEndTime) : '',
            '供应商': product.supplier?.name || '',
            'SKU ID': sku.skuId,
            'SKU名称': sku.skuName || '',
            'SKU价格': sku.price || '',
            '库存数量': sku.stock || '',
            'SKU状态': sku.isActive ? '启用' : '禁用',
            '创建时间': formatDateTime(product.createdAt),
            '更新时间': formatDateTime(product.updatedAt)
          })
        }
      } else {
        // 不包含SKU信息，每个商品一行
        exportData.push({
          '商品ID': product.productId,
          '商品标题': product.title,
          '商品定价': product.basePrice,
          '限价价格': product.limitPrice || '',
          '进货折扣': product.purchaseDiscount || '',
          '活动形式': getActivityTypeLabel(product.activityType),
          '活动开始时间': product.activityStartTime ? 
            formatDateTime(product.activityStartTime) : '',
          '活动结束时间': product.activityEndTime ? 
            formatDateTime(product.activityEndTime) : '',
          '供应商': product.supplier?.name || '',
          'SKU数量': product.skus?.length || 0,
          '创建时间': formatDateTime(product.createdAt),
          '更新时间': formatDateTime(product.updatedAt)
        })
      }
    }

    // 生成文件
    if (format === 'excel') {
      // 生成Excel文件
      const worksheet = XLSX.utils.json_to_sheet(exportData)
      const workbook = XLSX.utils.book_new()
      XLSX.utils.book_append_sheet(workbook, worksheet, '商品数据')

      // 设置列宽
      const colWidths = Object.keys(exportData[0] || {}).map(() => ({ wch: 15 }))
      worksheet['!cols'] = colWidths

      const buffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' })

      res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
      res.setHeader('Content-Disposition', `attachment; filename="商品导出_${new Date().toISOString().slice(0, 10)}.xlsx"`)
      res.send(buffer)

    } else if (format === 'csv') {
      // 生成CSV文件
      const headers = Object.keys(exportData[0] || {})
      const csvContent = [
        headers.join(','),
        ...exportData.map(row => 
          headers.map(header => `"${row[header] || ''}"`).join(',')
        )
      ].join('\n')

      const buffer = Buffer.from('\ufeff' + csvContent, 'utf8') // 添加BOM以支持中文

      res.setHeader('Content-Type', 'text/csv; charset=utf-8')
      res.setHeader('Content-Disposition', `attachment; filename="商品导出_${new Date().toISOString().slice(0, 10)}.csv"`)
      res.send(buffer)

    } else {
      return res.status(400).json({
        success: false,
        error: '不支持的导出格式'
      })
    }

  } catch (error) {
    console.error('导出商品失败:', error)
    return res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : '导出失败'
    })
  }
}

/**
 * 获取活动类型标签
 */
function getActivityTypeLabel(type: string): string {
  const labels = {
    DISCOUNT: '打折',
    DIRECT_REDUCTION: '直降',
    INSTANT_REDUCTION: '立减'
  }
  return labels[type] || type
}

/**
 * 格式化日期时间
 */
function formatDateTime(date: Date): string {
  return new Date(date).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false
  }).replace(/\//g, '-')
}

export const config = {
  api: {
    bodyParser: {
      sizeLimit: '10mb'
    }
  }
}
