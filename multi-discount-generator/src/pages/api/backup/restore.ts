import { NextApiRequest, NextApiResponse } from 'next'
import { dataBackupService } from '@/lib/backup/DataBackupService'

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    const {
      backupId,
      restoreTables = ['Task', 'TaskLog', 'File'],
      restoreFiles = true,
      overwriteExisting = false,
      validateBeforeRestore = true
    } = req.body

    if (!backupId) {
      return res.status(400).json({
        success: false,
        error: '备份ID是必需的'
      })
    }

    const result = await dataBackupService.restoreBackup({
      backupId,
      restoreTables,
      restoreFiles,
      overwriteExisting,
      validateBeforeRestore
    })

    if (result.success) {
      return res.status(200).json({
        success: true,
        data: {
          restoredTables: result.restoredTables,
          restoredFiles: result.restoredFiles,
          duration: result.duration
        },
        message: '数据恢复成功'
      })
    } else {
      return res.status(500).json({
        success: false,
        error: '数据恢复失败',
        details: result.error
      })
    }

  } catch (error) {
    console.error('恢复API错误:', error)
    
    return res.status(500).json({
      success: false,
      error: '恢复操作失败',
      details: error.message
    })
  }
}