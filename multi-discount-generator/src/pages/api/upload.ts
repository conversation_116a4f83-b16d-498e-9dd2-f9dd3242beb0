import type { NextApiRequest, NextApiResponse } from 'next'
import multer from 'multer'
import { FileUploadService, FileValidationService } from '@/lib/upload'
import type { 
  UploadFileResponse, 
  ApiResponse 
} from '@/types/api'

// 配置multer
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB
    files: 1
  },
  fileFilter: (req, file, cb) => {
    const validation = FileValidationService.validateFileBasics({
      name: file.originalname,
      size: 0, // 这里无法获取实际大小，在后续验证
      type: file.mimetype
    })
    
    if (validation.isValid) {
      cb(null, true)
    } else {
      cb(new Error(validation.errors[0] || '文件格式不支持'))
    }
  }
})

// 包装multer中间件
function runMiddleware(req: any, res: any, fn: any) {
  return new Promise((resolve, reject) => {
    fn(req, res, (result: any) => {
      if (result instanceof Error) {
        return reject(result)
      }
      return resolve(result)
    })
  })
}

/**
 * 文件上传API
 * POST /api/upload
 */
export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<ApiResponse<UploadFileResponse>>
) {
  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      error: '方法不允许'
    })
  }

  try {
    // 运行multer中间件
    await runMiddleware(req, res, upload.single('file'))
    
    const file = (req as any).file
    if (!file) {
      return res.status(400).json({
        success: false,
        error: '没有上传文件'
      })
    }

    // 验证文件
    const fileForValidation = new File([file.buffer], file.originalname, {
      type: file.mimetype
    })
    
    const validationResult = await FileValidationService.validateFile(fileForValidation)
    
    if (!validationResult.success) {
      return res.status(400).json({
        success: false,
        error: validationResult.errors?.[0] || '文件验证失败'
      })
    }

    // 保存文件
    const fileInfo = await FileUploadService.saveUploadedFile(
      file.buffer,
      file.originalname,
      file.mimetype
    )

    // 解析文件内容
    const parseResult = await FileUploadService.parseBatchFile(fileInfo.path)
    
    if (!parseResult.success) {
      // 清理上传的文件
      FileUploadService.cleanupFile(fileInfo.path)
      
      return res.status(400).json({
        success: false,
        error: parseResult.message
      })
    }

    // 提取商品ID列表
    const productIds = await FileUploadService.extractProductIds(fileInfo.path)

    return res.status(200).json({
      success: true,
      data: {
        filename: fileInfo.filename,
        originalName: fileInfo.originalName,
        size: fileInfo.size,
        path: fileInfo.path,
        products: productIds,
        productCount: productIds.length
      }
    })

  } catch (error) {
    console.error('文件上传失败:', error)
    
    // 如果是multer错误，提供更友好的错误信息
    if (error instanceof Error) {
      if (error.message.includes('File too large')) {
        return res.status(400).json({
          success: false,
          error: '文件大小超过10MB限制'
        })
      }
      if (error.message.includes('Unexpected field')) {
        return res.status(400).json({
          success: false,
          error: '请使用字段名"file"上传文件'
        })
      }
    }

    return res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : '文件上传失败'
    })
  }
}

export const config = {
  api: {
    bodyParser: false, // 禁用默认的body parser，使用multer
  },
}