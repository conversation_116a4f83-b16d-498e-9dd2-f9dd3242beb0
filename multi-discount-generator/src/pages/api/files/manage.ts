import type { NextApiRequest, NextApiResponse } from 'next'
import { FileUploadService } from '@/lib/upload'
import type { ApiResponse } from '@/types/api'

/**
 * 文件管理API
 * GET /api/files/manage - 获取文件列表和使用情况
 * DELETE /api/files/manage - 清理文件
 */
export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<ApiResponse>
) {
  try {
    switch (req.method) {
      case 'GET':
        return await handleGetFiles(req, res)
      case 'DELETE':
        return await handleDeleteFiles(req, res)
      default:
        return res.status(405).json({
          success: false,
          error: '方法不允许'
        })
    }
  } catch (error) {
    console.error('文件管理API失败:', error)
    return res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : '文件管理失败'
    })
  }
}

/**
 * 获取文件列表和使用情况
 */
async function handleGetFiles(
  req: NextApiRequest,
  res: NextApiResponse<ApiResponse>
) {
  const { type = 'all' } = req.query
  const config = FileUploadService.getConfig()

  const result: any = {
    directories: {},
    summary: {
      totalFiles: 0,
      totalSize: 0
    }
  }

  // 获取各目录的使用情况
  const directories = [
    { name: 'input', path: config.uploadDir, label: '上传文件' },
    { name: 'output', path: config.outputDir, label: '输出文件' },
    { name: 'temp', path: config.tempDir, label: '临时文件' }
  ]

  for (const dir of directories) {
    if (type === 'all' || type === dir.name) {
      const usage = FileUploadService.getDirectoryUsage(dir.path)
      result.directories[dir.name] = {
        label: dir.label,
        path: dir.path,
        fileCount: usage.fileCount,
        totalSize: usage.totalSize,
        files: usage.files.map(file => ({
          ...file,
          downloadUrl: FileUploadService.generateDownloadUrl(file.name, dir.name as any)
        }))
      }
      
      result.summary.totalFiles += usage.fileCount
      result.summary.totalSize += usage.totalSize
    }
  }

  return res.status(200).json({
    success: true,
    data: result
  })
}

/**
 * 清理文件
 */
async function handleDeleteFiles(
  req: NextApiRequest,
  res: NextApiResponse<ApiResponse>
) {
  const { 
    type = 'expired', 
    directory,
    filename,
    maxAge = '24' 
  } = req.body

  const config = FileUploadService.getConfig()
  let cleanedCount = 0

  if (type === 'expired') {
    // 清理过期文件
    const maxAgeMs = parseInt(maxAge, 10) * 60 * 60 * 1000 // 转换为毫秒
    
    if (directory) {
      // 清理指定目录
      const dirPath = directory === 'input' ? config.uploadDir :
                     directory === 'output' ? config.outputDir :
                     directory === 'temp' ? config.tempDir : null
      
      if (dirPath) {
        FileUploadService.cleanupExpiredFiles(dirPath, maxAgeMs)
        cleanedCount = 1 // 简化计数
      }
    } else {
      // 清理所有目录
      FileUploadService.cleanupExpiredFiles(config.uploadDir, maxAgeMs)
      FileUploadService.cleanupExpiredFiles(config.outputDir, maxAgeMs)
      FileUploadService.cleanupExpiredFiles(config.tempDir, maxAgeMs)
      cleanedCount = 3
    }
  } else if (type === 'specific' && directory && filename) {
    // 删除指定文件
    const dirPath = directory === 'input' ? config.uploadDir :
                   directory === 'output' ? config.outputDir :
                   directory === 'temp' ? config.tempDir : null
    
    if (dirPath) {
      const filePath = require('path').join(dirPath, filename)
      FileUploadService.cleanupFile(filePath)
      cleanedCount = 1
    }
  } else {
    return res.status(400).json({
      success: false,
      error: '清理参数无效'
    })
  }

  return res.status(200).json({
    success: true,
    data: {
      message: `成功清理文件`,
      cleanedCount,
      type,
      directory,
      filename
    }
  })
}