import type { NextApiRequest, NextApiResponse } from 'next'
import { Server as SocketIOServer } from 'socket.io'
import { Server as HTTPServer } from 'http'
import { SocketServer } from '@/lib/websocket/SocketServer'

let socketServer: SocketServer | null = null

/**
 * Socket.io API端点
 * 用于初始化WebSocket服务器
 */
export default function handler(req: NextApiRequest, res: NextApiResponse) {
  if (!res.socket.server.io) {
    console.log('初始化Socket.io服务器...')
    
    // 获取HTTP服务器实例
    const httpServer = res.socket.server as any
    
    // 初始化Socket服务器
    socketServer = SocketServer.getInstance()
    socketServer.initialize(httpServer)
    
    // 将io实例附加到服务器
    res.socket.server.io = true
  }

  res.end()
}

export const config = {
  api: {
    bodyParser: false,
  },
}