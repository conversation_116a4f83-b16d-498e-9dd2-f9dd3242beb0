import type { NextApiRequest, NextApiResponse } from 'next'
import { taskMonitor } from '@/lib/monitoring'
import type { 
  ListTasksRequest, 
  ListTasksResponse, 
  ApiResponse 
} from '@/types/api'
import { TaskStatus } from '@prisma/client'

/**
 * 任务列表API
 * GET /api/tasks/list
 */
export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<ApiResponse<ListTasksResponse>>
) {
  if (req.method !== 'GET') {
    return res.status(405).json({
      success: false,
      error: '方法不允许'
    })
  }

  try {
    const {
      page = '1',
      limit = '20',
      status,
      search
    } = req.query

    // 验证参数
    const pageNum = parseInt(page as string, 10)
    const limitNum = parseInt(limit as string, 10)

    if (isNaN(pageNum) || pageNum < 1) {
      return res.status(400).json({
        success: false,
        error: '页码参数无效'
      })
    }

    if (isNaN(limitNum) || limitNum < 1 || limitNum > 100) {
      return res.status(400).json({
        success: false,
        error: '每页数量参数无效（1-100）'
      })
    }

    // 验证状态参数
    let taskStatus: TaskStatus | undefined
    if (status && typeof status === 'string') {
      if (Object.values(TaskStatus).includes(status as TaskStatus)) {
        taskStatus = status as TaskStatus
      } else {
        return res.status(400).json({
          success: false,
          error: '任务状态参数无效'
        })
      }
    }

    // 构建查询参数
    const queryParams = {
      page: pageNum,
      limit: limitNum,
      status: taskStatus,
      search: search as string
    }

    // 获取任务列表
    const result = await taskMonitor.getTaskList(queryParams)

    return res.status(200).json({
      success: true,
      data: {
        tasks: result.tasks,
        pagination: {
          page: result.page,
          limit: result.limit,
          total: result.total,
          pages: result.pages
        }
      }
    })

  } catch (error) {
    console.error('获取任务列表失败:', error)
    return res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : '获取任务列表失败'
    })
  }
}