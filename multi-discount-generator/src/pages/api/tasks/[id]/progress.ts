import type { NextApiRequest, NextApiResponse } from 'next'
import { taskMonitor } from '@/lib/monitoring'
import type { 
  TaskProgressResponse, 
  ApiResponse 
} from '@/types/api'

/**
 * 任务进度查询API
 * GET /api/tasks/[id]/progress
 */
export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<ApiResponse<TaskProgressResponse>>
) {
  if (req.method !== 'GET') {
    return res.status(405).json({
      success: false,
      error: '方法不允许'
    })
  }

  const { id } = req.query
  
  if (!id || typeof id !== 'string') {
    return res.status(400).json({
      success: false,
      error: '任务ID无效'
    })
  }

  try {
    const progress = await taskMonitor.getTaskProgress(id)
    
    if (!progress) {
      return res.status(404).json({
        success: false,
        error: '任务不存在'
      })
    }

    return res.status(200).json({
      success: true,
      data: { progress }
    })

  } catch (error) {
    console.error('获取任务进度失败:', error)
    return res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : '获取任务进度失败'
    })
  }
}