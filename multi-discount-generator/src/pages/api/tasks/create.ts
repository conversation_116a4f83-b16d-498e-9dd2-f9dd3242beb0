import type { NextApiRequest, NextApiResponse } from 'next'
import { taskQueue } from '@/lib/queue'
import { FileUploadService } from '@/lib/upload'
import type { 
  CreateTaskRequest, 
  CreateTaskResponse, 
  ApiResponse 
} from '@/types/api'
import type { CreateTaskInput } from '@/types/batch'

/**
 * 创建新任务API
 * POST /api/tasks/create
 */
export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<ApiResponse<CreateTaskResponse>>
) {
  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      error: '方法不允许'
    })
  }

  try {
    const {
      name,
      type,
      config,
      products,
      priority = 0
    } = req.body as CreateTaskRequest

    // 验证必填字段
    if (!type || !config || !products || !Array.isArray(products)) {
      return res.status(400).json({
        success: false,
        error: '缺少必填字段'
      })
    }

    // 验证商品列表
    if (products.length === 0) {
      return res.status(400).json({
        success: false,
        error: '商品列表不能为空'
      })
    }

    if (products.length > 1000) {
      return res.status(400).json({
        success: false,
        error: '商品数量不能超过1000个'
      })
    }

    // 验证商品ID格式
    const invalidProducts = products.filter(id => 
      !id || typeof id !== 'string' || !/^[A-Za-z0-9_-]+$/.test(id)
    )
    
    if (invalidProducts.length > 0) {
      return res.status(400).json({
        success: false,
        error: `发现${invalidProducts.length}个格式不正确的商品ID`
      })
    }

    // 去重商品ID
    const uniqueProducts = [...new Set(products)]
    
    // 创建任务输入
    const taskInput: CreateTaskInput = {
      name: name || `批量任务_${new Date().toISOString().slice(0, 19)}`,
      type,
      config,
      products: uniqueProducts,
      priority
    }

    // 添加任务到队列
    const taskId = await taskQueue.addTask(taskInput)

    return res.status(201).json({
      success: true,
      data: {
        taskId,
        message: '任务创建成功'
      }
    })

  } catch (error) {
    console.error('创建任务失败:', error)
    return res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : '创建任务失败'
    })
  }
}

export const config = {
  api: {
    bodyParser: {
      sizeLimit: '10mb'
    }
  }
}