import type { NextApiRequest, NextApiResponse } from 'next'
import { taskMonitor } from '@/lib/monitoring'
import { taskQueue } from '@/lib/queue'
import type { 
  GetTaskResponse, 
  CancelTaskResponse,
  RetryTaskResponse,
  ApiResponse 
} from '@/types/api'

/**
 * 任务详情API
 * GET /api/tasks/[id] - 获取任务详情
 * POST /api/tasks/[id] - 操作任务（取消、重试等）
 */
export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<ApiResponse>
) {
  const { id } = req.query
  
  if (!id || typeof id !== 'string') {
    return res.status(400).json({
      success: false,
      error: '任务ID无效'
    })
  }

  try {
    switch (req.method) {
      case 'GET':
        return await handleGetTask(id, res)
      case 'POST':
        return await handleTaskOperation(id, req, res)
      default:
        return res.status(405).json({
          success: false,
          error: '方法不允许'
        })
    }
  } catch (error) {
    console.error('任务API处理失败:', error)
    return res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : '服务器内部错误'
    })
  }
}

/**
 * 获取任务详情
 */
async function handleGetTask(
  taskId: string,
  res: NextApiResponse<ApiResponse<GetTaskResponse>>
) {
  const task = await taskMonitor.getTaskDetails(taskId)
  
  if (!task) {
    return res.status(404).json({
      success: false,
      error: '任务不存在'
    })
  }

  return res.status(200).json({
    success: true,
    data: { task }
  })
}

/**
 * 处理任务操作
 */
async function handleTaskOperation(
  taskId: string,
  req: NextApiRequest,
  res: NextApiResponse<ApiResponse>
) {
  const { action } = req.body
  
  if (!action) {
    return res.status(400).json({
      success: false,
      error: '缺少操作类型'
    })
  }

  switch (action) {
    case 'cancel':
      return await handleCancelTask(taskId, res)
    case 'retry':
      return await handleRetryTask(taskId, res)
    default:
      return res.status(400).json({
        success: false,
        error: '不支持的操作类型'
      })
  }
}

/**
 * 取消任务
 */
async function handleCancelTask(
  taskId: string,
  res: NextApiResponse<ApiResponse<CancelTaskResponse>>
) {
  const success = await taskQueue.cancelTask(taskId)
  
  if (!success) {
    return res.status(400).json({
      success: false,
      error: '任务取消失败'
    })
  }

  return res.status(200).json({
    success: true,
    data: {
      message: '任务已取消',
      taskId
    }
  })
}

/**
 * 重试任务
 */
async function handleRetryTask(
  taskId: string,
  res: NextApiResponse<ApiResponse<RetryTaskResponse>>
) {
  const newTaskId = await taskQueue.retryTask(taskId)
  
  if (!newTaskId) {
    return res.status(400).json({
      success: false,
      error: '任务重试失败'
    })
  }

  return res.status(200).json({
    success: true,
    data: {
      message: '任务重试成功',
      taskId,
      newTaskId
    }
  })
}