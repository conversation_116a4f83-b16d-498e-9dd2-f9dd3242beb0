import { NextApiRequest, NextApiResponse } from 'next'
import { prisma } from '@/lib/prisma'
import { performanceMonitor } from '@/lib/performance/PerformanceMonitor'
import { databaseOptimizer } from '@/lib/performance/DatabaseOptimizer'
import { taskQueue } from '@/lib/queue'

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    const { type = 'overview', timeWindow = '3600000' } = req.query
    const timeWindowMs = parseInt(timeWindow as string)

    switch (type) {
      case 'overview':
        return res.json(await getOverviewStats(timeWindowMs))
      
      case 'tasks':
        return res.json(await getTaskStats(timeWindowMs))
      
      case 'performance':
        return res.json(await getPerformanceStats(timeWindowMs))
      
      case 'database':
        return res.json(await getDatabaseStats(timeWindowMs))
      
      case 'system':
        return res.json(await getSystemStats())
      
      default:
        return res.status(400).json({ error: 'Invalid stats type' })
    }
  } catch (error) {
    console.error('统计API错误:', error)
    return res.status(500).json({
      success: false,
      error: '获取统计信息失败',
      details: error.message
    })
  }
}

/**
 * 获取系统概览统计
 */
async function getOverviewStats(timeWindow: number) {
  const cutoffTime = new Date(Date.now() - timeWindow)
  
  // 任务统计
  const totalTasks = await prisma.task.count()
  const recentTasks = await prisma.task.count({
    where: { createdAt: { gte: cutoffTime } }
  })
  
  const tasksByStatus = await prisma.task.groupBy({
    by: ['status'],
    _count: { status: true },
    where: { createdAt: { gte: cutoffTime } }
  })
  
  // 文件统计
  const totalFiles = await prisma.file.count()
  const recentFiles = await prisma.file.count({
    where: { createdAt: { gte: cutoffTime } }
  })
  
  // 日志统计
  const totalLogs = await prisma.taskLog.count()
  const recentLogs = await prisma.taskLog.count({
    where: { timestamp: { gte: cutoffTime } }
  })
  
  const logsByLevel = await prisma.taskLog.groupBy({
    by: ['level'],
    _count: { level: true },
    where: { timestamp: { gte: cutoffTime } }
  })
  
  // 性能指标
  const metrics = performanceMonitor.getMetrics(timeWindow)
  const avgResponseTime = metrics
    .filter(m => m.name.includes('response_time'))
    .reduce((sum, m, _, arr) => sum + m.value / arr.length, 0)
  
  return {
    success: true,
    data: {
      tasks: {
        total: totalTasks,
        recent: recentTasks,
        byStatus: tasksByStatus.reduce((acc, item) => {
          acc[item.status] = item._count.status
          return acc
        }, {} as Record<string, number>)
      },
      files: {
        total: totalFiles,
        recent: recentFiles
      },
      logs: {
        total: totalLogs,
        recent: recentLogs,
        byLevel: logsByLevel.reduce((acc, item) => {
          acc[item.level] = item._count.level
          return acc
        }, {} as Record<string, number>)
      },
      performance: {
        averageResponseTime: Math.round(avgResponseTime),
        totalMetrics: metrics.length
      },
      timeWindow: timeWindow,
      timestamp: new Date()
    }
  }
}

/**
 * 获取任务统计
 */
async function getTaskStats(timeWindow: number) {
  const cutoffTime = new Date(Date.now() - timeWindow)
  
  // 基础任务统计
  const taskStats = await prisma.task.aggregate({
    _count: { id: true },
    _avg: { progress: true },
    where: { createdAt: { gte: cutoffTime } }
  })
  
  // 按状态分组
  const tasksByStatus = await prisma.task.groupBy({
    by: ['status'],
    _count: { status: true },
    where: { createdAt: { gte: cutoffTime } }
  })
  
  // 按类型分组
  const tasksByType = await prisma.task.groupBy({
    by: ['type'],
    _count: { type: true },
    where: { createdAt: { gte: cutoffTime } }
  })
  
  // 按优先级分组
  const tasksByPriority = await prisma.task.groupBy({
    by: ['priority'],
    _count: { priority: true },
    _avg: { progress: true },
    where: { createdAt: { gte: cutoffTime } }
  })
  
  // 任务执行时间统计
  const completedTasks = await prisma.task.findMany({
    where: {
      status: 'COMPLETED',
      createdAt: { gte: cutoffTime },
      completedAt: { not: null }
    },
    select: {
      createdAt: true,
      completedAt: true
    }
  })
  
  const executionTimes = completedTasks.map(task => 
    task.completedAt!.getTime() - task.createdAt.getTime()
  )
  
  const avgExecutionTime = executionTimes.length > 0 
    ? executionTimes.reduce((sum, time) => sum + time, 0) / executionTimes.length 
    : 0
  
  // 获取任务队列状态
  const queueStats = await taskQueue.getStats()
  
  return {
    success: true,
    data: {
      summary: {
        total: taskStats._count.id,
        averageProgress: Math.round(taskStats._avg.progress || 0),
        averageExecutionTime: Math.round(avgExecutionTime)
      },
      byStatus: tasksByStatus.reduce((acc, item) => {
        acc[item.status] = item._count.status
        return acc
      }, {} as Record<string, number>),
      byType: tasksByType.reduce((acc, item) => {
        acc[item.type] = item._count.type
        return acc
      }, {} as Record<string, number>),
      byPriority: tasksByPriority.map(item => ({
        priority: item.priority,
        count: item._count.priority,
        averageProgress: Math.round(item._avg.progress || 0)
      })),
      queue: queueStats,
      executionTimes: {
        average: Math.round(avgExecutionTime),
        min: executionTimes.length > 0 ? Math.min(...executionTimes) : 0,
        max: executionTimes.length > 0 ? Math.max(...executionTimes) : 0,
        samples: executionTimes.length
      },
      timeWindow: timeWindow,
      timestamp: new Date()
    }
  }
}

/**
 * 获取性能统计
 */
async function getPerformanceStats(timeWindow: number) {
  const metrics = performanceMonitor.getMetrics(timeWindow)
  
  // 按指标名称分组
  const metricsByName = metrics.reduce((acc, metric) => {
    if (!acc[metric.name]) {
      acc[metric.name] = []
    }
    acc[metric.name].push(metric)
    return acc
  }, {} as Record<string, typeof metrics>)
  
  // 计算每个指标的统计信息
  const metricStats = Object.entries(metricsByName).map(([name, values]) => {
    const numericValues = values.map(v => v.value)
    return {
      name,
      count: values.length,
      average: numericValues.reduce((sum, val) => sum + val, 0) / numericValues.length,
      min: Math.min(...numericValues),
      max: Math.max(...numericValues),
      latest: values[values.length - 1]?.value || 0,
      unit: values[0]?.unit || '',
      trend: calculateTrend(numericValues)
    }
  })
  
  // 系统资源使用情况
  const memoryUsage = process.memoryUsage()
  const cpuUsage = process.cpuUsage()
  
  return {
    success: true,
    data: {
      metrics: metricStats,
      system: {
        memory: {
          heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024),
          heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024),
          external: Math.round(memoryUsage.external / 1024 / 1024),
          rss: Math.round(memoryUsage.rss / 1024 / 1024)
        },
        cpu: {
          user: cpuUsage.user,
          system: cpuUsage.system
        },
        uptime: Math.round(process.uptime()),
        nodeVersion: process.version
      },
      timeWindow: timeWindow,
      timestamp: new Date()
    }
  }
}

/**
 * 获取数据库统计
 */
async function getDatabaseStats(timeWindow: number) {
  const dbStats = databaseOptimizer.getQueryStats(timeWindow)
  const trends = databaseOptimizer.analyzePerformanceTrends(timeWindow)
  const report = databaseOptimizer.generateOptimizationReport(timeWindow)
  
  // 获取数据库表统计
  const tableStats = await Promise.all([
    prisma.task.count(),
    prisma.file.count(),
    prisma.taskLog.count()
  ])
  
  return {
    success: true,
    data: {
      queries: {
        total: dbStats.totalQueries,
        slow: dbStats.slowQueries,
        averageDuration: Math.round(dbStats.averageDuration),
        byType: dbStats.queryTypes
      },
      performance: {
        trend: trends.trend,
        averageDurationChange: trends.averageDurationChange,
        slowQueryChange: trends.slowQueryChange
      },
      optimization: {
        suggestions: report.recommendations,
        topIssues: report.topIssues
      },
      tables: {
        tasks: tableStats[0],
        files: tableStats[1],
        logs: tableStats[2]
      },
      timeWindow: timeWindow,
      timestamp: new Date()
    }
  }
}

/**
 * 获取系统统计
 */
async function getSystemStats() {
  const startTime = Date.now()
  
  // 系统信息
  const systemInfo = {
    platform: process.platform,
    arch: process.arch,
    nodeVersion: process.version,
    uptime: Math.round(process.uptime()),
    pid: process.pid
  }
  
  // 环境变量（安全的）
  const envInfo = {
    nodeEnv: process.env.NODE_ENV,
    timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
  }
  
  // 内存使用情况
  const memoryUsage = process.memoryUsage()
  const memoryInfo = {
    heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024),
    heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024),
    external: Math.round(memoryUsage.external / 1024 / 1024),
    rss: Math.round(memoryUsage.rss / 1024 / 1024),
    arrayBuffers: Math.round(memoryUsage.arrayBuffers / 1024 / 1024)
  }
  
  // CPU使用情况
  const cpuUsage = process.cpuUsage()
  const cpuInfo = {
    user: Math.round(cpuUsage.user / 1000), // 转换为毫秒
    system: Math.round(cpuUsage.system / 1000)
  }
  
  const responseTime = Date.now() - startTime
  
  return {
    success: true,
    data: {
      system: systemInfo,
      environment: envInfo,
      memory: memoryInfo,
      cpu: cpuInfo,
      responseTime,
      timestamp: new Date()
    }
  }
}

/**
 * 计算数值趋势
 */
function calculateTrend(values: number[]): 'up' | 'down' | 'stable' {
  if (values.length < 2) return 'stable'
  
  const firstHalf = values.slice(0, Math.floor(values.length / 2))
  const secondHalf = values.slice(Math.floor(values.length / 2))
  
  const firstAvg = firstHalf.reduce((sum, val) => sum + val, 0) / firstHalf.length
  const secondAvg = secondHalf.reduce((sum, val) => sum + val, 0) / secondHalf.length
  
  const change = (secondAvg - firstAvg) / firstAvg
  
  if (change > 0.1) return 'up'
  if (change < -0.1) return 'down'
  return 'stable'
}