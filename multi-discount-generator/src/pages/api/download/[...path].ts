import type { NextApiRequest, NextApiResponse } from 'next'
import { createReadStream, existsSync, statSync } from 'fs'
import { join, extname, basename } from 'path'
import { FileUploadService } from '@/lib/upload'

/**
 * 文件下载API
 * GET /api/download/[type]/[filename]
 * GET /api/download/tasks/[taskId]
 */
export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== 'GET') {
    return res.status(405).json({
      success: false,
      error: '方法不允许'
    })
  }

  try {
    const { path: pathSegments } = req.query
    
    if (!pathSegments || !Array.isArray(pathSegments)) {
      return res.status(400).json({
        success: false,
        error: '路径参数无效'
      })
    }

    let filePath: string
    let filename: string

    if (pathSegments.length === 2) {
      // /api/download/[type]/[filename]
      const [type, fileName] = pathSegments
      
      if (!['input', 'output', 'temp'].includes(type)) {
        return res.status(400).json({
          success: false,
          error: '文件类型无效'
        })
      }

      const config = FileUploadService.getConfig()
      const baseDir = type === 'input' ? config.uploadDir : 
                     type === 'output' ? config.outputDir : 
                     config.tempDir

      filePath = join(baseDir, fileName)
      filename = fileName
    } else if (pathSegments.length === 2 && pathSegments[0] === 'tasks') {
      // /api/download/tasks/[taskId] - 下载任务结果文件
      const taskId = pathSegments[1]
      
      // 从数据库获取任务信息
      const { prisma } = await import('@/lib/prisma')
      const task = await prisma.task.findUnique({
        where: { id: taskId },
        select: { outputFile: true, name: true }
      })

      if (!task || !task.outputFile) {
        return res.status(404).json({
          success: false,
          error: '任务文件不存在'
        })
      }

      filePath = join(process.cwd(), task.outputFile)
      filename = `${task.name}_${taskId}.xlsx`
    } else {
      return res.status(400).json({
        success: false,
        error: '路径格式不正确'
      })
    }

    // 检查文件是否存在
    if (!existsSync(filePath)) {
      return res.status(404).json({
        success: false,
        error: '文件不存在'
      })
    }

    // 获取文件信息
    const stats = statSync(filePath)
    const fileExtension = extname(filePath).toLowerCase()
    
    // 设置响应头
    let contentType = 'application/octet-stream'
    switch (fileExtension) {
      case '.xlsx':
        contentType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        break
      case '.xls':
        contentType = 'application/vnd.ms-excel'
        break
      case '.csv':
        contentType = 'text/csv'
        break
    }

    res.setHeader('Content-Type', contentType)
    res.setHeader('Content-Length', stats.size)
    res.setHeader('Content-Disposition', `attachment; filename="${encodeURIComponent(filename)}"`)
    res.setHeader('Cache-Control', 'no-cache')

    // 创建文件流并发送
    const fileStream = createReadStream(filePath)
    
    fileStream.on('error', (error) => {
      console.error('文件流读取错误:', error)
      if (!res.headersSent) {
        res.status(500).json({
          success: false,
          error: '文件读取失败'
        })
      }
    })

    fileStream.pipe(res)

  } catch (error) {
    console.error('文件下载失败:', error)
    
    if (!res.headersSent) {
      res.status(500).json({
        success: false,
        error: error instanceof Error ? error.message : '文件下载失败'
      })
    }
  }
}