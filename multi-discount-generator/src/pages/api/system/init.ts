import { NextApiRequest, NextApiResponse } from 'next'
import { systemInitializer } from '@/lib/system/SystemInitializer'

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method === 'POST') {
    try {
      await systemInitializer.initialize()
      
      return res.status(200).json({
        success: true,
        message: '系统初始化成功',
        status: systemInitializer.getSystemStatus()
      })
    } catch (error) {
      console.error('系统初始化失败:', error)
      
      return res.status(500).json({
        success: false,
        error: '系统初始化失败',
        details: error.message
      })
    }
  } else if (req.method === 'GET') {
    // 获取系统状态
    const status = systemInitializer.getSystemStatus()
    
    return res.status(200).json({
      success: true,
      data: status
    })
  } else if (req.method === 'DELETE') {
    // 关闭系统
    try {
      await systemInitializer.shutdown()
      
      return res.status(200).json({
        success: true,
        message: '系统关闭成功'
      })
    } catch (error) {
      console.error('系统关闭失败:', error)
      
      return res.status(500).json({
        success: false,
        error: '系统关闭失败',
        details: error.message
      })
    }
  } else {
    return res.status(405).json({ error: 'Method not allowed' })
  }
}