import { NextApiRequest, NextApiResponse } from 'next'
import { systemRecoveryService } from '@/lib/system/SystemRecoveryService'
import { taskRetryService } from '@/lib/batch/TaskRetryService'
import { globalErrorHandler } from '@/lib/error/GlobalErrorHandler'

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    switch (req.method) {
      case 'POST':
        return await handleRecoveryTrigger(req, res)
      
      case 'GET':
        return await handleRecoveryStats(req, res)
      
      default:
        return res.status(405).json({ error: 'Method not allowed' })
    }
  } catch (error) {
    console.error('系统恢复API错误:', error)
    
    return res.status(500).json({
      success: false,
      error: '系统恢复操作失败',
      details: error.message
    })
  }
}

async function handleRecoveryTrigger(req: NextApiRequest, res: NextApiResponse) {
  const { action, reason = 'manual' } = req.body

  switch (action) {
    case 'system_recovery':
      const recoveryResult = await systemRecoveryService.triggerRecovery(reason)
      
      return res.status(200).json({
        success: recoveryResult.success,
        data: recoveryResult,
        message: recoveryResult.success 
          ? '系统恢复完成' 
          : '系统恢复部分成功'
      })

    case 'create_checkpoint':
      await systemRecoveryService.createCheckpoint(reason)
      
      return res.status(200).json({
        success: true,
        message: '系统检查点创建成功'
      })

    default:
      return res.status(400).json({
        success: false,
        error: '无效的恢复操作'
      })
  }
}

async function handleRecoveryStats(req: NextApiRequest, res: NextApiResponse) {
  const { type = 'system', timeWindow = '86400000' } = req.query
  const timeWindowMs = parseInt(timeWindow as string)

  switch (type) {
    case 'system':
      const systemStats = await systemRecoveryService.getRecoveryStats(timeWindowMs)
      
      return res.status(200).json({
        success: true,
        data: systemStats
      })

    case 'retry':
      const retryStats = await taskRetryService.getRetryStats(timeWindowMs)
      
      return res.status(200).json({
        success: true,
        data: retryStats
      })

    case 'error':
      const errorStats = await globalErrorHandler.getErrorStats(timeWindowMs)
      
      return res.status(200).json({
        success: true,
        data: errorStats
      })

    default:
      return res.status(400).json({
        success: false,
        error: '无效的统计类型'
      })
  }
}