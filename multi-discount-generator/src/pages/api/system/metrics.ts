import type { NextApiRequest, NextApiResponse } from 'next'
import { taskMonitor } from '@/lib/monitoring'
import { taskQueue } from '@/lib/queue'
import type { 
  SystemMetricsResponse, 
  ApiResponse 
} from '@/types/api'

/**
 * 系统指标API
 * GET /api/system/metrics
 */
export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<ApiResponse<SystemMetricsResponse>>
) {
  if (req.method !== 'GET') {
    return res.status(405).json({
      success: false,
      error: '方法不允许'
    })
  }

  try {
    // 收集系统指标
    const metrics = await taskMonitor.collectSystemMetrics()
    
    return res.status(200).json({
      success: true,
      data: { metrics }
    })

  } catch (error) {
    console.error('获取系统指标失败:', error)
    return res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : '获取系统指标失败'
    })
  }
}