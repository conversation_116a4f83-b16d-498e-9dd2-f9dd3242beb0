import type { NextApiRequest, NextApiResponse } from 'next'
import { taskMonitor } from '@/lib/monitoring'
import { taskQueue } from '@/lib/queue'
import type { 
  SystemStatsResponse, 
  ApiResponse 
} from '@/types/api'

/**
 * 系统统计API
 * GET /api/system/stats
 */
export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<ApiResponse<SystemStatsResponse>>
) {
  if (req.method !== 'GET') {
    return res.status(405).json({
      success: false,
      error: '方法不允许'
    })
  }

  try {
    // 获取任务统计
    const taskStats = await taskMonitor.getTaskStats()
    
    // 获取队列状态
    const queueStatus = await taskQueue.getStats()
    
    // 获取系统信息
    const memoryUsage = process.memoryUsage()
    const cpuUsage = process.cpuUsage()
    const uptime = process.uptime()

    return res.status(200).json({
      success: true,
      data: {
        tasks: taskStats,
        queue: {
          length: queueStatus.queueLength,
          processing: queueStatus.running
        },
        system: {
          uptime,
          memoryUsage,
          cpuUsage
        }
      }
    })

  } catch (error) {
    console.error('获取系统统计失败:', error)
    return res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : '获取系统统计失败'
    })
  }
}