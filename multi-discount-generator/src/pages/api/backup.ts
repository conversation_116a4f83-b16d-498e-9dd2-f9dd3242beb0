import { NextApiRequest, NextApiResponse } from 'next'
import { dataBackupService } from '@/lib/backup/DataBackupService'

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    switch (req.method) {
      case 'POST':
        return await handleCreateBackup(req, res)
      
      case 'GET':
        return await handleListBackups(req, res)
      
      case 'DELETE':
        return await handleDeleteBackup(req, res)
      
      default:
        return res.status(405).json({ error: 'Method not allowed' })
    }
  } catch (error) {
    console.error('备份API错误:', error)
    
    return res.status(500).json({
      success: false,
      error: '备份操作失败',
      details: error.message
    })
  }
}

async function handleCreateBackup(req: NextApiRequest, res: NextApiResponse) {
  const {
    includeFiles = true,
    includeLogs = true,
    includeMetrics = false,
    compression = true
  } = req.body

  const result = await dataBackupService.createBackup({
    includeFiles,
    includeLogs,
    includeMetrics,
    compression,
    encryption: false,
    retentionDays: 30,
    maxBackupSize: 1024 * 1024 * 1024 // 1GB
  })

  if (result.success) {
    return res.status(200).json({
      success: true,
      data: {
        backupId: result.backupId,
        size: result.size,
        duration: result.duration
      },
      message: '备份创建成功'
    })
  } else {
    return res.status(500).json({
      success: false,
      error: '备份创建失败',
      details: result.error
    })
  }
}

async function handleListBackups(req: NextApiRequest, res: NextApiResponse) {
  const backups = await dataBackupService.listBackups()
  
  return res.status(200).json({
    success: true,
    data: backups
  })
}

async function handleDeleteBackup(req: NextApiRequest, res: NextApiResponse) {
  const { backupId } = req.query

  if (!backupId || typeof backupId !== 'string') {
    return res.status(400).json({
      success: false,
      error: '备份ID是必需的'
    })
  }

  const result = await dataBackupService.deleteBackup(backupId)

  if (result.success) {
    return res.status(200).json({
      success: true,
      message: '备份删除成功'
    })
  } else {
    return res.status(500).json({
      success: false,
      error: '备份删除失败',
      details: result.error
    })
  }
}