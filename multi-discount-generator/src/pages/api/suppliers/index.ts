import type { NextApiRequest, NextApiResponse } from 'next'
import { prisma } from '@/lib/prisma'
import type { Supplier, SupplierFormData } from '@/types/product'
import type { ApiResponse } from '@/types/api'

/**
 * 供应商管理API
 * GET /api/suppliers - 获取供应商列表
 * POST /api/suppliers - 创建供应商
 */
export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<ApiResponse<Supplier[] | Supplier>>
) {
  if (req.method === 'GET') {
    try {
      const { search, isActive } = req.query

      const where: any = {}

      // 搜索条件
      if (search && typeof search === 'string') {
        where.OR = [
          { name: { contains: search } },
          { code: { contains: search } },
          { contact: { contains: search } }
        ]
      }

      // 状态筛选
      if (isActive !== undefined) {
        where.isActive = isActive === 'true'
      }

      const suppliers = await prisma.supplier.findMany({
        where,
        orderBy: [
          { isActive: 'desc' },
          { name: 'asc' }
        ]
      })

      return res.status(200).json({
        success: true,
        data: suppliers
      })

    } catch (error) {
      console.error('获取供应商列表失败:', error)
      return res.status(500).json({
        success: false,
        error: '获取供应商列表失败'
      })
    }

  } else if (req.method === 'POST') {
    try {
      const supplierData: SupplierFormData = req.body

      // 验证必填字段
      if (!supplierData.name || !supplierData.code) {
        return res.status(400).json({
          success: false,
          error: '供应商名称和编码为必填字段'
        })
      }

      // 验证供应商编码格式
      if (!/^[A-Za-z0-9_-]+$/.test(supplierData.code)) {
        return res.status(400).json({
          success: false,
          error: '供应商编码格式不正确，只能包含字母、数字、下划线和横线'
        })
      }

      // 检查供应商编码是否已存在
      const existingSupplier = await prisma.supplier.findUnique({
        where: { code: supplierData.code }
      })

      if (existingSupplier) {
        return res.status(400).json({
          success: false,
          error: '供应商编码已存在'
        })
      }

      // 创建供应商
      const supplier = await prisma.supplier.create({
        data: {
          name: supplierData.name,
          code: supplierData.code,
          contact: supplierData.contact,
          phone: supplierData.phone,
          email: supplierData.email,
          address: supplierData.address,
          isActive: supplierData.isActive ?? true
        }
      })

      return res.status(201).json({
        success: true,
        data: supplier
      })

    } catch (error) {
      console.error('创建供应商失败:', error)
      
      // 处理Prisma错误
      if (error.code === 'P2002') {
        return res.status(400).json({
          success: false,
          error: '供应商编码已存在'
        })
      }

      return res.status(500).json({
        success: false,
        error: error instanceof Error ? error.message : '创建供应商失败'
      })
    }

  } else {
    return res.status(405).json({
      success: false,
      error: '方法不允许'
    })
  }
}

export const config = {
  api: {
    bodyParser: {
      sizeLimit: '1mb'
    }
  }
}
