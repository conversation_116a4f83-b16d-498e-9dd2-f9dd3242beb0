import { NextApiRequest, NextApiResponse } from 'next'
import { healthCheckService } from '@/lib/monitoring/HealthCheckService'

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    const { detailed = 'false', history = 'false' } = req.query

    if (history === 'true') {
      // 返回健康检查历史
      const hours = parseInt(req.query.hours as string) || 24
      const healthHistory = await healthCheckService.getHealthHistory(hours)
      
      return res.status(200).json({
        success: true,
        data: healthHistory
      })
    }

    if (detailed === 'true') {
      // 执行完整的健康检查
      const healthCheck = await healthCheckService.performHealthCheck()
      
      const statusCode = healthCheck.overall === 'healthy' ? 200 : 
                        healthCheck.overall === 'warning' ? 200 : 503
      
      return res.status(statusCode).json({
        success: true,
        data: healthCheck
      })
    } else {
      // 返回简单的健康状态
      const lastCheck = healthCheckService.getLastHealthCheck()
      
      if (!lastCheck) {
        // 如果没有最近的检查结果，执行一次快速检查
        const quickCheck = await healthCheckService.performHealthCheck()
        return res.status(200).json({
          success: true,
          data: {
            status: quickCheck.overall,
            uptime: healthCheckService.getUptime(),
            timestamp: new Date(),
            message: `系统状态: ${quickCheck.overall}`
          }
        })
      }
      
      const statusCode = lastCheck.overall === 'healthy' ? 200 : 
                        lastCheck.overall === 'warning' ? 200 : 503
      
      return res.status(statusCode).json({
        success: true,
        data: {
          status: lastCheck.overall,
          uptime: healthCheckService.getUptime(),
          timestamp: lastCheck.timestamp,
          summary: lastCheck.summary,
          message: `系统状态: ${lastCheck.overall}`
        }
      })
    }
  } catch (error) {
    console.error('健康检查API错误:', error)
    
    return res.status(500).json({
      success: false,
      error: '健康检查失败',
      details: error.message
    })
  }
}