import type { NextApiRequest, NextApiResponse } from 'next'
import * as XLSX from 'xlsx'
import type { ApiResponse } from '@/types/api'
import { prisma } from '@/lib/prisma'
import { TaskType, TaskStatus } from '@prisma/client'
import {
  EXCEL_HEADERS,
  EXCEL_COLUMN_WIDTHS,
  EXCEL_CONFIG
} from '@/constants'

/**
 * 解析区域信息，转换为FormData期望的格式
 */
function parseRegion(regionStr: string): any {
  if (!regionStr || regionStr === '全国') {
    return { type: 'national', regions: [] }
  }

  // 处理逗号分隔的多个区域
  const regions = regionStr.split(',').map(r => r.trim()).filter(r => r)
  return { type: 'regional', regions }
}

/**
 * 解析时间模式，转换中文为英文
 */
function parseTimeMode(modeStr: string): 'continuous' | 'random' {
  if (modeStr === '连续' || modeStr === 'continuous') {
    return 'continuous'
  }
  if (modeStr === '随机' || modeStr === 'random') {
    return 'random'
  }
  return 'continuous' // 默认连续模式
}

/**
 * 批量任务管理API
 * GET /api/batch/tasks - 获取所有任务
 * GET /api/batch/tasks?taskId=xxx - 获取特定任务状态
 * GET /api/batch/tasks?action=export&taskId=xxx - 导出任务结果
 */
export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<ApiResponse<any>>
) {
  if (req.method !== 'GET') {
    return res.status(405).json({
      success: false,
      error: '方法不允许'
    })
  }

  try {
    const { taskId, action } = req.query

    // 导出任务结果
    if (action === 'export' && taskId) {
      const task = await prisma.task.findUnique({
        where: { id: taskId as string }
      })

      if (!task || task.status !== TaskStatus.COMPLETED) {
        return res.status(404).json({
          success: false,
          error: '任务不存在或未完成'
        })
      }

      // 重新生成Excel文件（因为我们没有实际保存文件到磁盘）
      try {
        const config = JSON.parse(task.config || '{}')
        const { templateData, configs } = config

        if (!templateData || !Array.isArray(templateData)) {
          return res.status(400).json({
            success: false,
            error: '任务配置数据无效'
          })
        }

        // 重新生成Excel数据（使用与生成时相同的逻辑）
        const allExcelData: any[] = []

        for (const rowData of templateData) {
          try {
            // 处理日期时间字段（与模板上传API保持一致）
            const processedRowData = { ...rowData }

            // 处理日期字段
            const dateFields = ['开始日期', '结束日期']
            dateFields.forEach(field => {
              if (processedRowData[field] !== undefined && processedRowData[field] !== '') {
                const value = processedRowData[field]

                // 如果是数字（Excel日期序列号），转换为日期
                if (typeof value === 'number') {
                  const excelEpoch = new Date(1899, 11, 30) // 1899年12月30日
                  const jsDate = new Date(excelEpoch.getTime() + value * 24 * 60 * 60 * 1000)

                  if (!isNaN(jsDate.getTime())) {
                    const year = jsDate.getFullYear()
                    const month = String(jsDate.getMonth() + 1).padStart(2, '0')
                    const day = String(jsDate.getDate()).padStart(2, '0')
                    processedRowData[field] = `${year}-${month}-${day}`
                  }
                }
              }
            })

            // 处理时间字段
            const timeFields = ['每日开始时间', '每日结束时间']
            timeFields.forEach(field => {
              if (processedRowData[field] !== undefined && processedRowData[field] !== '') {
                const value = processedRowData[field]

                // 如果是数字（Excel时间小数），转换为时间
                if (typeof value === 'number') {
                  const totalMinutes = Math.round(value * 24 * 60)
                  let hours = Math.floor(totalMinutes / 60)
                  let minutes = totalMinutes % 60

                  // 处理边界情况
                  if (hours >= 24) {
                    hours = 23
                    minutes = 59
                  }

                  const hoursStr = String(hours).padStart(2, '0')
                  const minutesStr = String(minutes).padStart(2, '0')
                  processedRowData[field] = `${hoursStr}:${minutesStr}:00`
                }
              }
            })

            // 构建FormData
            const formData = {
              productId: processedRowData['商品ID'] || '',
              region: parseRegion(processedRowData['活动区域'] || '全国'),
              minQuantity: parseInt(processedRowData['满几件']) || 1,
              discount: parseFloat(processedRowData['折扣']) || 8,
              timeMode: parseTimeMode(processedRowData['时间模式'] || configs.timeMode || 'continuous'),
              startDate: processedRowData['开始日期'] || configs.startDate,
              endDate: processedRowData['结束日期'] || configs.endDate,
              dailyStartTime: processedRowData['每日开始时间'] || configs.dailyStartTime,
              dailyEndTime: processedRowData['每日结束时间'] || configs.dailyEndTime,
              weekendFullDay: (processedRowData['周末全天'] === '是') || configs.weekendFullDay,
              holidayFullDay: (processedRowData['节假日全天'] === '是') || configs.holidayFullDay,
              timeSlotCount: parseInt(processedRowData['时间段数量']) || configs.timeSlotCount || 5,
              slotDuration: parseInt(processedRowData['时段时长']) || configs.slotDuration || 1
            }

            // 使用TimeGenerator生成时间段
            const { TimeGenerator } = await import('@/lib/time-generator')
            let timeResult

            if (formData.timeMode === 'continuous') {
              // 连续模式：需要构建连续时间配置
              const startDate = new Date(formData.startDate)
              const endDate = new Date(formData.endDate)

              // 计算天数：结束日期 - 开始日期 + 1
              const timeDiff = endDate.getTime() - startDate.getTime()
              const days = Math.ceil(timeDiff / (1000 * 60 * 60 * 24)) + 1

              // 构建时间对象
              const startTime = new Date(`2024-01-01 ${formData.dailyStartTime}`)
              const endTime = new Date(`2024-01-01 ${formData.dailyEndTime}`)

              timeResult = TimeGenerator.generateContinuousTimeSlots({
                startDate: startDate,
                startTime: startTime,
                endTime: endTime,
                days: days,
                weekendFullDay: formData.weekendFullDay ?? true
              })
            } else {
              // 随机模式
              const startTime = new Date(`2024-01-01 ${formData.dailyStartTime}`)

              timeResult = TimeGenerator.generateRandomTimeSlots({
                startTime: startTime,
                slotCount: formData.timeSlotCount || 5,
                slotDuration: formData.slotDuration || 1
              })
            }

            if (timeResult.errors.length === 0) {
              // 使用ExcelService生成Excel数据
              const { ExcelService } = await import('@/lib/excel')
              const excelData = ExcelService.generateExcelData(formData, timeResult.slots)

              // 转换为数组格式
              const excelRows = excelData.map(row => [
                row.活动类型,
                row.商品ID,
                row['开始时间\n示例：\n2020-03-27 00:00:00'],
                row['结束时间\n示例：\n2020-03-27 23:59:59'],
                row['活动区域\n支持填写"全国"及5大区\n5大区：华中,西南/西北,华南,华东,华北'],
                row['满几件\n说明：表示满N件，件数只能在下拉框中选择'],
                row['折扣-打几折\n说明：表示打几折，折扣只能下拉选择'],
                row['立减金额-多件总额下的扣减金额\n表示扣减的金额，单位元，支持最多2位小数，与折扣只能择一生效']
              ])

              allExcelData.push(...excelRows)
            }
          } catch (error) {
            console.error(`重新生成商品 ${rowData['商品ID']} 数据失败:`, error)
          }
        }

        // 创建Excel工作簿
        const workbook = XLSX.utils.book_new()
        const headers = Array.from(EXCEL_HEADERS)
        const worksheet = XLSX.utils.aoa_to_sheet([headers, ...allExcelData])
        worksheet['!cols'] = [...EXCEL_COLUMN_WIDTHS]

        // 设置行高
        worksheet['!rows'] = [
          { hpt: 60 }, // 标题行高度
          ...allExcelData.map(() => ({ hpt: 20 })) // 数据行高度
        ]

        XLSX.utils.book_append_sheet(workbook, worksheet, EXCEL_CONFIG.SHEET_NAME)

        const buffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' })
        const filename = `批量活动时间表_${taskId}_${new Date().toISOString().slice(0, 10)}.xlsx`

        res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
        res.setHeader('Content-Disposition', `attachment; filename="${encodeURIComponent(filename)}"`)
        res.setHeader('Content-Length', buffer.length)

        return res.send(buffer)

      } catch (error) {
        console.error('重新生成Excel失败:', error)
        return res.status(500).json({
          success: false,
          error: '导出失败，请重试'
        })
      }
    }

    // 获取特定任务状态
    if (taskId) {
      const task = await prisma.task.findUnique({
        where: { id: taskId as string }
      })

      if (!task) {
        return res.status(404).json({
          success: false,
          error: '任务不存在'
        })
      }

      // 转换为前端期望的格式
      const taskData = {
        id: task.id,
        productId: 'BATCH', // 批量任务
        title: task.name,
        status: task.status.toLowerCase(),
        progress: task.totalItems > 0 ? Math.round((task.processedItems / task.totalItems) * 100) : 0,
        timeSlots: [],
        excelData: [],
        createdAt: task.createdAt.toISOString(),
        completedAt: task.completedAt?.toISOString(),
        errorMessage: task.status === TaskStatus.FAILED ? '任务处理失败' : undefined
      }

      return res.status(200).json({
        success: true,
        data: taskData
      })
    }

    // 获取所有批量任务
    const tasks = await prisma.task.findMany({
      where: { type: TaskType.BATCH },
      orderBy: { createdAt: 'desc' }
    })

    // 转换为前端期望的格式
    const tasksData = tasks.map(task => ({
      id: task.id,
      productId: 'BATCH',
      title: task.name,
      status: task.status.toLowerCase(),
      progress: task.totalItems > 0 ? Math.round((task.processedItems / task.totalItems) * 100) : 0,
      timeSlots: [],
      excelData: [],
      createdAt: task.createdAt.toISOString(),
      completedAt: task.completedAt?.toISOString(),
      errorMessage: task.status === TaskStatus.FAILED ? '任务处理失败' : undefined
    }))

    return res.status(200).json({
      success: true,
      data: tasksData
    })

  } catch (error) {
    console.error('任务查询失败:', error)
    return res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : '任务查询失败'
    })
  }
}
