import type { NextApiRequest, NextApiResponse } from 'next'
import * as XLSX from 'xlsx'
import { format } from 'date-fns'
import type { ApiResponse } from '@/types/api'
import { prisma } from '@/lib/prisma'
import { TaskType, TaskStatus } from '@prisma/client'
import { TimeGenerator } from '@/lib/time-generator'
import { ExcelService } from '@/lib/excel'
import type { FormData, TimeSlot } from '@/types'
import {
  EXCEL_HEADERS,
  EXCEL_COLUMN_WIDTHS,
  EXCEL_CONFIG,
  DEFAULT_VALUES,
  TIME_FORMAT
} from '@/constants'

interface BatchTaskRequest {
  templateData: any[]
  configs: {
    timeMode: 'continuous' | 'random'
    startDate: string
    endDate: string
    dailyStartTime: string
    dailyEndTime: string
    weekendFullDay: boolean
    holidayFullDay: boolean
    timeSlotCount?: number
    slotDuration?: number
  }
}

interface BatchTaskConfig {
  templateData: any[]
  configs: {
    timeMode: 'continuous' | 'random'
    startDate: string
    endDate: string
    dailyStartTime: string
    dailyEndTime: string
    weekendFullDay: boolean
    holidayFullDay: boolean
    timeSlotCount?: number
    slotDuration?: number
  }
}

/**
 * 批量生成活动时间任务API
 * POST /api/batch/generate-tasks
 */
export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<ApiResponse<{ taskIds: string[] }>>
) {
  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      error: '方法不允许'
    })
  }

  try {
    const { templateData, configs } = req.body as BatchTaskConfig

    if (!templateData || !Array.isArray(templateData) || templateData.length === 0) {
      return res.status(400).json({
        success: false,
        error: '模板数据不能为空'
      })
    }

    // 创建批量任务
    const task = await prisma.task.create({
      data: {
        name: `批量生成活动时间表 - ${templateData.length}个商品`,
        type: TaskType.BATCH,
        status: TaskStatus.PENDING,
        config: JSON.stringify({ templateData, configs }),
        totalItems: templateData.length,
        processedItems: 0,
        failedItems: 0
      }
    })

    // 异步处理任务
    processTask(task.id, templateData, configs).catch(async (error) => {
      console.error(`任务 ${task.id} 处理失败:`, error)
      await prisma.task.update({
        where: { id: task.id },
        data: {
          status: TaskStatus.FAILED,
          completedAt: new Date()
        }
      })
    })

    return res.status(200).json({
      success: true,
      data: { taskIds: [task.id] }
    })

  } catch (error) {
    console.error('批量生成任务失败:', error)
    return res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : '批量生成任务失败'
    })
  }
}

/**
 * 处理批量任务
 */
async function processTask(taskId: string, templateData: any[], configs: any) {
  try {
    // 更新任务状态为运行中
    await prisma.task.update({
      where: { id: taskId },
      data: {
        status: TaskStatus.RUNNING,
        startedAt: new Date()
      }
    })

    const allExcelData: any[] = []
    let processedCount = 0

    // 处理每个商品
    for (const rowData of templateData) {
      try {
        console.log('处理商品数据:', rowData)

        // 处理日期时间字段（与模板上传API保持一致）
        const processedRowData = { ...rowData }

        // 处理日期字段
        const dateFields = ['开始日期', '结束日期']
        dateFields.forEach(field => {
          if (processedRowData[field] !== undefined && processedRowData[field] !== '') {
            const value = processedRowData[field]

            // 如果是数字（Excel日期序列号），转换为日期
            if (typeof value === 'number') {
              const excelEpoch = new Date(1899, 11, 30) // 1899年12月30日
              const jsDate = new Date(excelEpoch.getTime() + value * 24 * 60 * 60 * 1000)

              if (!isNaN(jsDate.getTime())) {
                const year = jsDate.getFullYear()
                const month = String(jsDate.getMonth() + 1).padStart(2, '0')
                const day = String(jsDate.getDate()).padStart(2, '0')
                processedRowData[field] = `${year}-${month}-${day}`
              }
            }
          }
        })

        // 处理时间字段
        const timeFields = ['每日开始时间', '每日结束时间']
        timeFields.forEach(field => {
          if (processedRowData[field] !== undefined && processedRowData[field] !== '') {
            const value = processedRowData[field]

            // 如果是数字（Excel时间小数），转换为时间
            if (typeof value === 'number') {
              const totalMinutes = Math.round(value * 24 * 60)
              let hours = Math.floor(totalMinutes / 60)
              let minutes = totalMinutes % 60

              // 处理边界情况
              if (hours >= 24) {
                hours = 23
                minutes = 59
              }

              const hoursStr = String(hours).padStart(2, '0')
              const minutesStr = String(minutes).padStart(2, '0')
              processedRowData[field] = `${hoursStr}:${minutesStr}:00`
            }
          }
        })

        console.log('处理后的商品数据:', processedRowData)

        // 构建与首页相同的FormData
        const formData: FormData = {
          productId: processedRowData['商品ID'] || '',
          region: parseRegion(processedRowData['活动区域'] || '全国'),
          minQuantity: parseInt(processedRowData['满几件']) || 1,
          discount: parseFloat(processedRowData['折扣']) || 8,
          timeMode: parseTimeMode(processedRowData['时间模式'] || configs.timeMode || 'continuous'),
          startDate: processedRowData['开始日期'] || configs.startDate,
          endDate: processedRowData['结束日期'] || configs.endDate,
          dailyStartTime: processedRowData['每日开始时间'] || configs.dailyStartTime,
          dailyEndTime: processedRowData['每日结束时间'] || configs.dailyEndTime,
          weekendFullDay: (processedRowData['周末全天'] === '是') || configs.weekendFullDay,
          holidayFullDay: (processedRowData['节假日全天'] === '是') || configs.holidayFullDay,
          timeSlotCount: parseInt(processedRowData['时间段数量']) || configs.timeSlotCount || 5,
          slotDuration: parseInt(processedRowData['时段时长']) || configs.slotDuration || 1
        }

        console.log('构建的FormData:', formData)

        // 使用与首页相同的时间生成器
        console.log('开始生成时间段...')
        let timeResult

        if (formData.timeMode === 'continuous') {
          // 连续模式：需要构建连续时间配置
          const startDate = new Date(formData.startDate)
          const endDate = new Date(formData.endDate)

          // 计算天数：结束日期 - 开始日期 + 1
          const timeDiff = endDate.getTime() - startDate.getTime()
          const days = Math.ceil(timeDiff / (1000 * 60 * 60 * 24)) + 1

          console.log(`连续模式配置: 开始=${formData.startDate}, 结束=${formData.endDate}, 天数=${days}`)

          // 构建时间对象
          const startTime = new Date(`2024-01-01 ${formData.dailyStartTime}`)
          const endTime = new Date(`2024-01-01 ${formData.dailyEndTime}`)

          timeResult = TimeGenerator.generateContinuousTimeSlots({
            startDate: startDate,
            startTime: startTime,
            endTime: endTime,
            days: days,
            weekendFullDay: formData.weekendFullDay ?? true
          })
        } else {
          // 随机模式
          const startTime = new Date(`2024-01-01 ${formData.dailyStartTime}`)

          timeResult = TimeGenerator.generateRandomTimeSlots({
            startTime: startTime,
            slotCount: formData.timeSlotCount || 5,
            slotDuration: formData.slotDuration || 1
          })
        }

        console.log('时间生成结果:', {
          slotsCount: timeResult.slots.length,
          errors: timeResult.errors,
          firstSlot: timeResult.slots[0]
        })

        if (timeResult.errors.length > 0) {
          console.error(`商品 ${formData.productId} 时间生成错误:`, timeResult.errors)
          await prisma.task.update({
            where: { id: taskId },
            data: { failedItems: { increment: 1 } }
          })
          continue
        }

        if (timeResult.slots.length === 0) {
          console.error(`商品 ${formData.productId} 没有生成任何时间段`)
          await prisma.task.update({
            where: { id: taskId },
            data: { failedItems: { increment: 1 } }
          })
          continue
        }

        // 使用与首页相同的Excel数据生成器
        console.log('开始生成Excel数据...')
        const excelData = ExcelService.generateExcelData(formData, timeResult.slots)
        console.log('Excel数据生成结果:', {
          dataCount: excelData.length,
          firstRow: excelData[0]
        })

        // 转换为数组格式
        const excelRows = excelData.map(row => [
          row.活动类型,
          row.商品ID,
          row['开始时间\n示例：\n2020-03-27 00:00:00'],
          row['结束时间\n示例：\n2020-03-27 23:59:59'],
          row['活动区域\n支持填写"全国"及5大区\n5大区：华中,西南/西北,华南,华东,华北'],
          row['满几件\n说明：表示满N件，件数只能在下拉框中选择'],
          row['折扣-打几折\n说明：表示打几折，折扣只能下拉选择'],
          row['立减金额-多件总额下的扣减金额\n表示扣减的金额，单位元，支持最多2位小数，与折扣只能择一生效']
        ])

        console.log('转换后的Excel行数:', excelRows.length)
        console.log('第一行数据:', excelRows[0])

        allExcelData.push(...excelRows)
        processedCount++

        // 更新进度
        await prisma.task.update({
          where: { id: taskId },
          data: { processedItems: processedCount }
        })

      } catch (error) {
        console.error(`处理商品 ${rowData['商品ID']} 失败:`, error)
        await prisma.task.update({
          where: { id: taskId },
          data: { failedItems: { increment: 1 } }
        })
      }
    }

    // 生成Excel文件
    const workbook = XLSX.utils.book_new()
    const headers = Array.from(EXCEL_HEADERS)
    const worksheet = XLSX.utils.aoa_to_sheet([headers, ...allExcelData])
    worksheet['!cols'] = [...EXCEL_COLUMN_WIDTHS]
    XLSX.utils.book_append_sheet(workbook, worksheet, EXCEL_CONFIG.SHEET_NAME)

    // 保存文件
    const filename = `批量生成_${taskId}_${new Date().toISOString().slice(0, 10)}.xlsx`
    const buffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' })

    // 这里应该保存到文件系统或云存储，暂时存储文件名
    const outputFile = `uploads/${filename}`

    // 任务完成
    await prisma.task.update({
      where: { id: taskId },
      data: {
        status: TaskStatus.COMPLETED,
        completedAt: new Date(),
        outputFile: outputFile
      }
    })

  } catch (error) {
    console.error(`任务 ${taskId} 处理失败:`, error)
    await prisma.task.update({
      where: { id: taskId },
      data: {
        status: TaskStatus.FAILED,
        completedAt: new Date()
      }
    })
  }
}

/**
 * 解析区域信息，转换为FormData期望的格式
 */
function parseRegion(regionStr: string): any {
  if (!regionStr || regionStr === '全国') {
    return { type: 'national', regions: [] }
  }

  // 处理逗号分隔的多个区域
  const regions = regionStr.split(',').map(r => r.trim()).filter(r => r)
  return { type: 'regional', regions }
}

/**
 * 解析时间模式，转换中文为英文
 */
function parseTimeMode(modeStr: string): 'continuous' | 'random' {
  if (modeStr === '连续' || modeStr === 'continuous') {
    return 'continuous'
  }
  if (modeStr === '随机' || modeStr === 'random') {
    return 'random'
  }
  return 'continuous' // 默认连续模式
}


