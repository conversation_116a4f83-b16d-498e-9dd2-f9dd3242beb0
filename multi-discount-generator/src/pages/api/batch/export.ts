import type { NextApiRequest, NextApiResponse } from 'next'
import * as XLSX from 'xlsx'
import { format } from 'date-fns'
import type { ApiResponse } from '@/types/api'
import {
  EXCEL_HEADERS,
  EXCEL_COLUMN_WIDTHS,
  EXCEL_CONFIG,
  TIME_FORMAT,
  DEFAULT_VALUES
} from '@/constants'

interface BatchExportRequest {
  productIds: string[]
  configs: {
    activityType: string
    timeMode: string
    startDate: string
    endDate: string
    dailyStartTime: string
    dailyEndTime: string
    region: string
    minQuantity: number
    discount: number
    weekendFullDay: boolean
    holidayFullDay: boolean
  }
}

/**
 * 批量导出活动时间表格API
 * POST /api/batch/export
 */
export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<ApiResponse<any>>
) {
  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      error: '方法不允许'
    })
  }

  try {
    const { productIds, configs } = req.body as BatchExportRequest

    if (!productIds || !Array.isArray(productIds) || productIds.length === 0) {
      return res.status(400).json({
        success: false,
        error: '商品ID列表不能为空'
      })
    }

    // 生成活动时间表格数据（与前端手动导出格式完全一致）
    const scheduleData = generateActivitySchedule(productIds, configs)

    // 创建Excel工作簿
    const workbook = XLSX.utils.book_new()

    // 使用与前端完全一致的表头
    const headers = Array.from(EXCEL_HEADERS)

    // 生成与前端格式完全一致的数据行
    const rows = scheduleData.map(item => [
      DEFAULT_VALUES.ACTIVITY_TYPE, // 活动类型：固定"多件多折"
      item.productId, // 商品ID
      `${item.date} ${item.startTime}`, // 开始时间（完整格式）
      `${item.date} ${item.endTime}`, // 结束时间（完整格式）
      formatRegion(item.region), // 活动区域
      1, // 满几件：固定为1
      item.discount, // 折扣
      '' // 立减金额：空值
    ])

    const worksheet = XLSX.utils.aoa_to_sheet([headers, ...rows])

    // 设置与前端一致的列宽
    worksheet['!cols'] = [...EXCEL_COLUMN_WIDTHS]

    // 设置行高（为多行标题预留空间）
    worksheet['!rows'] = [
      { hpt: 60 }, // 标题行高度
      ...rows.map(() => ({ hpt: 20 })) // 数据行高度
    ]

    XLSX.utils.book_append_sheet(workbook, worksheet, EXCEL_CONFIG.SHEET_NAME)



    // 生成文件名
    const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '')
    const filename = `活动时间表_${timestamp}.xlsx`

    // 将工作簿转换为buffer
    const buffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' })

    // 设置响应头
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
    res.setHeader('Content-Disposition', `attachment; filename="${encodeURIComponent(filename)}"`)
    res.setHeader('Content-Length', buffer.length)

    // 发送文件
    res.send(buffer)

  } catch (error) {
    console.error('批量导出失败:', error)
    return res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : '批量导出失败'
    })
  }
}

/**
 * 生成活动时间表数据（与前端手动导出格式一致）
 */
function generateActivitySchedule(productIds: string[], configs: any) {
  const scheduleData: any[] = []

  const startDate = new Date(configs.startDate)
  const endDate = new Date(configs.endDate)

  // 遍历每个商品ID
  productIds.forEach(productId => {
    // 遍历日期范围
    for (let date = new Date(startDate); date <= endDate; date.setDate(date.getDate() + 1)) {
      const currentDate = new Date(date)
      const dateStr = format(currentDate, 'yyyy-MM-dd')
      const dayOfWeek = currentDate.getDay() // 0=周日, 6=周六
      const isWeekend = dayOfWeek === 0 || dayOfWeek === 6

      // 判断是否全天活动
      const isFullDay = (isWeekend && configs.weekendFullDay) ||
                       (isHoliday(currentDate) && configs.holidayFullDay)

      if (isFullDay) {
        // 全天活动
        scheduleData.push({
          productId,
          date: dateStr,
          startTime: '00:00:00',
          endTime: '23:59:59',
          region: configs.region,
          discount: configs.discount
        })
      } else {
        // 按设定时间段
        scheduleData.push({
          productId,
          date: dateStr,
          startTime: configs.dailyStartTime,
          endTime: configs.dailyEndTime,
          region: configs.region,
          discount: configs.discount
        })
      }
    }
  })

  return scheduleData
}

/**
 * 格式化区域信息（与前端ExcelService.formatRegion一致）
 */
function formatRegion(region: string): string {
  if (region === '全国') {
    return '全国'
  }

  // 如果是多个区域，用逗号分隔
  if (Array.isArray(region)) {
    return region.join(',')
  }

  return region
}



/**
 * 判断是否为节假日（简单实现，可以根据需要扩展）
 */
function isHoliday(date: Date): boolean {
  // 这里可以添加节假日判断逻辑
  // 暂时返回false，可以根据实际需求添加节假日数据
  return false
}
