import type { NextApiRequest, NextApiResponse } from 'next'
import multer from 'multer'
import * as XLSX from 'xlsx'
import path from 'path'
import fs from 'fs'
import type { ApiResponse } from '@/types/api'

// 配置multer
const upload = multer({
  storage: multer.diskStorage({
    destination: (req, file, cb) => {
      const uploadDir = path.join(process.cwd(), 'uploads', 'templates')
      if (!fs.existsSync(uploadDir)) {
        fs.mkdirSync(uploadDir, { recursive: true })
      }
      cb(null, uploadDir)
    },
    filename: (req, file, cb) => {
      const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9)
      cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname))
    }
  }),
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB
    files: 1
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = [
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-excel',
      'text/csv',
      'application/csv'
    ]
    
    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true)
    } else {
      cb(new Error('不支持的文件类型'))
    }
  }
})

// 运行中间件的辅助函数
function runMiddleware(req: any, res: any, fn: any) {
  return new Promise((resolve, reject) => {
    fn(req, res, (result: any) => {
      if (result instanceof Error) {
        return reject(result)
      }
      return resolve(result)
    })
  })
}

/**
 * 模板文件上传API
 * POST /api/templates/upload
 */
export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<ApiResponse<any>>
) {
  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      error: '方法不允许'
    })
  }

  try {
    // 运行multer中间件
    await runMiddleware(req, res, upload.single('file'))
    
    const file = (req as any).file
    if (!file) {
      return res.status(400).json({
        success: false,
        error: '没有上传文件'
      })
    }

    // 解析Excel文件
    const workbook = XLSX.readFile(file.path)
    const sheetName = workbook.SheetNames[0]
    const worksheet = workbook.Sheets[sheetName]
    
    // 转换为JSON数据，使用默认设置让XLSX自动处理日期
    const jsonData = XLSX.utils.sheet_to_json(worksheet)

    if (jsonData.length === 0) {
      return res.status(400).json({
        success: false,
        error: '文件中没有数据'
      })
    }

    // 获取列名（表头）
    const headers = jsonData.length > 0 ? Object.keys(jsonData[0]) : []

    // 处理日期字段的转换
    const parsedData = jsonData.map((row: any) => {
      const processedRow = { ...row }

      // 处理日期字段
      const dateFields = ['开始日期', '结束日期']
      dateFields.forEach(field => {
        if (processedRow[field] !== undefined && processedRow[field] !== '') {
          const value = processedRow[field]

          // 如果是数字（Excel日期序列号），转换为日期
          if (typeof value === 'number') {
            // Excel日期从1900年1月1日开始计算（但有1900年闰年bug，实际从1899年12月30日开始）
            const excelEpoch = new Date(1899, 11, 30) // 1899年12月30日
            const jsDate = new Date(excelEpoch.getTime() + value * 24 * 60 * 60 * 1000)

            if (!isNaN(jsDate.getTime())) {
              const year = jsDate.getFullYear()
              const month = String(jsDate.getMonth() + 1).padStart(2, '0')
              const day = String(jsDate.getDate()).padStart(2, '0')
              processedRow[field] = `${year}-${month}-${day}`
            }
          }
          // 如果是字符串日期，尝试标准化格式
          else if (typeof value === 'string') {
            try {
              const date = new Date(value)
              if (!isNaN(date.getTime())) {
                const year = date.getFullYear()
                const month = String(date.getMonth() + 1).padStart(2, '0')
                const day = String(date.getDate()).padStart(2, '0')
                processedRow[field] = `${year}-${month}-${day}`
              }
            } catch (error) {
              console.warn(`无法解析日期字段 ${field}:`, value)
            }
          }
        }
      })

      // 处理时间字段
      const timeFields = ['每日开始时间', '每日结束时间']
      timeFields.forEach(field => {
        if (processedRow[field] !== undefined && processedRow[field] !== '') {
          const value = processedRow[field]

          // 如果是数字（Excel时间小数），转换为时间
          if (typeof value === 'number') {
            // Excel时间是一天的小数部分，0.5表示12:00:00
            const totalMinutes = Math.round(value * 24 * 60)
            let hours = Math.floor(totalMinutes / 60)
            let minutes = totalMinutes % 60

            // 处理边界情况
            if (hours >= 24) {
              hours = 23
              minutes = 59
            }

            const hoursStr = String(hours).padStart(2, '0')
            const minutesStr = String(minutes).padStart(2, '0')
            processedRow[field] = `${hoursStr}:${minutesStr}:00`
          }
          // 如果是字符串时间，尝试标准化格式
          else if (typeof value === 'string') {
            // 如果已经是HH:MM:SS格式，保持不变
            if (/^\d{2}:\d{2}:\d{2}$/.test(value)) {
              processedRow[field] = value
            }
            // 如果是HH:MM格式，添加秒
            else if (/^\d{1,2}:\d{2}$/.test(value)) {
              const [hours, minutes] = value.split(':')
              const hoursStr = String(parseInt(hours)).padStart(2, '0')
              const minutesStr = String(parseInt(minutes)).padStart(2, '0')
              processedRow[field] = `${hoursStr}:${minutesStr}:00`
            }
          }
        }
      })

      return processedRow
    }).filter(row => {
      // 过滤空行
      return Object.values(row).some(value => value && value.toString().trim() !== '')
    })

    // 验证数据
    const errors: string[] = []
    const products: string[] = []
    
    parsedData.forEach((row, index) => {
      const productId = row['商品ID']
      if (!productId || typeof productId !== 'string' || productId.trim() === '') {
        errors.push(`第${index + 2}行: 商品ID不能为空`)
      } else {
        products.push(productId.trim())
      }
    })

    // 生成批量任务配置
    const configs = parsedData.map(row => ({
      productId: row['商品ID'],
      activityType: row['活动类型'] || '多件多折',
      timeMode: row['时间模式'] || '连续',
      startDate: row['开始日期'],
      endDate: row['结束日期'],
      dailyStartTime: row['每日开始时间'] || '09:00:00',
      dailyEndTime: row['每日结束时间'] || '21:00:00',
      region: row['活动区域'] || '全国',
      minQuantity: parseInt(row['满几件']) || 1,
      discount: parseInt(row['折扣']) || 8,
      weekendFullDay: row['周末全天'] === '是',
      holidayFullDay: row['节假日全天'] === '是'
    }))

    return res.status(200).json({
      success: true,
      data: {
        filename: file.filename,
        originalName: Buffer.from(file.originalname, 'latin1').toString('utf8'), // 正确解码中文文件名
        templateData: parsedData,
        columns: headers,
        products: [...new Set(products)], // 去重
        configs,
        errors,
        summary: {
          totalRows: parsedData.length,
          validProducts: products.length,
          errorCount: errors.length
        }
      }
    })

  } catch (error) {
    console.error('模板上传失败:', error)
    
    if (error instanceof Error) {
      if (error.message.includes('File too large')) {
        return res.status(400).json({
          success: false,
          error: '文件大小超过10MB限制'
        })
      }
      if (error.message.includes('不支持的文件类型')) {
        return res.status(400).json({
          success: false,
          error: '不支持的文件类型，请上传Excel或CSV文件'
        })
      }
    }

    return res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : '模板上传失败'
    })
  }
}

export const config = {
  api: {
    bodyParser: false, // 禁用默认的body parser，使用multer
  },
}
