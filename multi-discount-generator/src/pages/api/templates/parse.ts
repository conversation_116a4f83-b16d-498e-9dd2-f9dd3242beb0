import type { NextApiRequest, NextApiResponse } from 'next'
import multer from 'multer'
import { TemplateService } from '@/lib/template/TemplateService'
import { FileUploadService } from '@/lib/upload'
import type { ApiResponse } from '@/types/api'

// 配置multer
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB
    files: 1
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = [
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-excel',
      'text/csv',
      'application/csv'
    ]
    
    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true)
    } else {
      cb(new Error('不支持的文件类型'))
    }
  }
})

// 运行中间件的辅助函数
function runMiddleware(req: any, res: any, fn: any) {
  return new Promise((resolve, reject) => {
    fn(req, res, (result: any) => {
      if (result instanceof Error) {
        return reject(result)
      }
      return resolve(result)
    })
  })
}

/**
 * 模板解析API
 * POST /api/templates/parse
 */
export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<ApiResponse<any>>
) {
  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      error: '方法不允许'
    })
  }

  try {
    // 运行multer中间件
    await runMiddleware(req, res, upload.single('file'))
    
    const file = (req as any).file
    if (!file) {
      return res.status(400).json({
        success: false,
        error: '没有上传文件'
      })
    }

    // 创建File对象用于解析
    const fileForParsing = new File([file.buffer], file.originalname, {
      type: file.mimetype
    })

    // 解析模板
    const parseResult = await TemplateService.parseTemplate(fileForParsing)

    if (parseResult.errors.length > 0) {
      return res.status(400).json({
        success: false,
        error: '模板解析失败',
        details: parseResult.errors
      })
    }

    // 生成配置
    const configResult = TemplateService.generateConfigFromTemplate(
      parseResult.data,
      'standard' // 暂时使用标准格式
    )

    return res.status(200).json({
      success: true,
      data: {
        templateData: parseResult.data,
        columns: parseResult.columns,
        products: configResult.products,
        configs: configResult.configs,
        errors: configResult.errors,
        summary: {
          totalRows: parseResult.data.length,
          validProducts: configResult.products.length,
          errorCount: configResult.errors.length
        }
      }
    })

  } catch (error) {
    console.error('模板解析失败:', error)
    
    if (error instanceof Error) {
      if (error.message.includes('File too large')) {
        return res.status(400).json({
          success: false,
          error: '文件大小超过10MB限制'
        })
      }
      if (error.message.includes('不支持的文件类型')) {
        return res.status(400).json({
          success: false,
          error: '不支持的文件类型，请上传Excel或CSV文件'
        })
      }
    }

    return res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : '模板解析失败'
    })
  }
}

export const config = {
  api: {
    bodyParser: false, // 禁用默认的body parser，使用multer
  },
}
