import type { NextApiRequest, NextApiResponse } from 'next'
import { taskQueue } from '@/lib/queue/TaskQueue'
import type { CreateTaskInput } from '@/types/batch'
import type { ApiResponse } from '@/types/api'

/**
 * 基于模板创建批量任务API
 * POST /api/templates/create-task
 */
export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<ApiResponse<{ taskId: string }>>
) {
  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      error: '方法不允许'
    })
  }

  try {
    const { 
      name,
      products,
      configs,
      templateData,
      priority = 0
    } = req.body

    // 验证必填字段
    if (!products || !Array.isArray(products) || products.length === 0) {
      return res.status(400).json({
        success: false,
        error: '商品列表不能为空'
      })
    }

    if (!configs || !Array.isArray(configs) || configs.length === 0) {
      return res.status(400).json({
        success: false,
        error: '配置信息不能为空'
      })
    }

    // 去重商品ID
    const uniqueProducts = [...new Set(products)]

    // 构建任务配置
    const taskConfig = {
      type: 'template_batch',
      configs,
      templateData,
      generatedAt: new Date().toISOString()
    }

    // 创建任务输入
    const taskInput: CreateTaskInput = {
      name: name || `模板批量任务_${new Date().toISOString().slice(0, 19)}`,
      type: 'BATCH',
      config: taskConfig,
      products: uniqueProducts,
      priority
    }

    // 添加任务到队列
    const taskId = await taskQueue.addTask(taskInput)

    return res.status(201).json({
      success: true,
      data: {
        taskId
      }
    })

  } catch (error) {
    console.error('创建模板任务失败:', error)
    return res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : '创建任务失败'
    })
  }
}

export const config = {
  api: {
    bodyParser: {
      sizeLimit: '10mb'
    }
  }
}
