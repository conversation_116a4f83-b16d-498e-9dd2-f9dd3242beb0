import { NextApiRequest, NextApiResponse } from 'next'
import { logAggregationService } from '@/lib/monitoring/LogAggregationService'

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    const { retentionDays = 30 } = req.body
    const retentionDaysNum = parseInt(retentionDays)

    if (retentionDaysNum < 1 || retentionDaysNum > 365) {
      return res.status(400).json({
        success: false,
        error: '保留天数必须在1-365之间'
      })
    }

    // 执行日志清理
    const result = await logAggregationService.cleanupOldLogs(retentionDaysNum)

    return res.status(200).json({
      success: true,
      data: {
        deletedCount: result.deletedCount,
        oldestRemaining: result.oldestRemaining,
        retentionDays: retentionDaysNum,
        timestamp: new Date()
      },
      message: `成功清理了 ${result.deletedCount} 条旧日志`
    })
    
  } catch (error) {
    console.error('日志清理失败:', error)
    
    return res.status(500).json({
      success: false,
      error: '日志清理失败',
      details: error.message
    })
  }
}