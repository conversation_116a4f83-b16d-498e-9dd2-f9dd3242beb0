import { NextApiRequest, NextApiResponse } from 'next'
import { logAggregationService } from '@/lib/monitoring/LogAggregationService'

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    const {
      level,
      taskId,
      source,
      startTime,
      endTime,
      search,
      format = 'json'
    } = req.query

    // 构建查询参数
    const query: any = {}
    
    if (level) {
      query.level = Array.isArray(level) ? level : [level]
    }
    
    if (taskId) {
      query.taskId = taskId as string
    }
    
    if (source) {
      query.source = source as string
    }
    
    if (startTime) {
      query.startTime = new Date(startTime as string)
    }
    
    if (endTime) {
      query.endTime = new Date(endTime as string)
    }
    
    if (search) {
      query.search = search as string
    }

    // 导出日志
    const exportData = await logAggregationService.exportLogs(
      query,
      format as 'json' | 'csv'
    )

    // 设置响应头
    const timestamp = new Date().toISOString().split('T')[0]
    const filename = `logs-export-${timestamp}.${format}`
    
    if (format === 'csv') {
      res.setHeader('Content-Type', 'text/csv')
      res.setHeader('Content-Disposition', `attachment; filename="${filename}"`)
    } else {
      res.setHeader('Content-Type', 'application/json')
      res.setHeader('Content-Disposition', `attachment; filename="${filename}"`)
    }

    return res.status(200).send(exportData)
    
  } catch (error) {
    console.error('日志导出失败:', error)
    
    return res.status(500).json({
      success: false,
      error: '日志导出失败',
      details: error.message
    })
  }
}