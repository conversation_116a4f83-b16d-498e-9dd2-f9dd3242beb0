import { NextApiRequest, NextApiResponse } from 'next'
import { logAggregationService } from '@/lib/monitoring/LogAggregationService'

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    const { timeWindow = '86400000' } = req.query // 默认24小时
    const timeWindowMs = parseInt(timeWindow as string)

    // 检测日志模式
    const patterns = await logAggregationService.detectLogPatterns(timeWindowMs)

    return res.status(200).json({
      success: true,
      data: {
        patterns,
        timeWindow: timeWindowMs,
        timestamp: new Date()
      }
    })
    
  } catch (error) {
    console.error('日志模式分析失败:', error)
    
    return res.status(500).json({
      success: false,
      error: '日志模式分析失败',
      details: error.message
    })
  }
}