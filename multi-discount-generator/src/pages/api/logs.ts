import type { NextApiRequest, NextApiResponse } from 'next'
import { prisma } from '@/lib/prisma'
import { LogLevel } from '@prisma/client'
import type { ApiResponse } from '@/types/api'

/**
 * 日志查询API
 * GET /api/logs
 */
export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<ApiResponse>
) {
  if (req.method !== 'GET') {
    return res.status(405).json({
      success: false,
      error: '方法不允许'
    })
  }

  try {
    const {
      taskId,
      level,
      search,
      startDate,
      endDate,
      limit = '100',
      offset = '0'
    } = req.query

    const limitNum = parseInt(limit as string, 10)
    const offsetNum = parseInt(offset as string, 10)

    // 构建查询条件
    const where: any = {}
    
    if (taskId) {
      where.taskId = taskId as string
    }
    
    if (level && Object.values(LogLevel).includes(level as LogLevel)) {
      where.level = level as LogLevel
    }
    
    if (search) {
      where.OR = [
        { message: { contains: search as string, mode: 'insensitive' } },
        { details: { contains: search as string, mode: 'insensitive' } }
      ]
    }
    
    if (startDate || endDate) {
      where.createdAt = {}
      if (startDate) {
        where.createdAt.gte = new Date(startDate as string)
      }
      if (endDate) {
        where.createdAt.lte = new Date(endDate as string)
      }
    }

    // 查询日志
    const logs = await prisma.taskLog.findMany({
      where,
      include: {
        task: {
          select: {
            name: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: limitNum,
      skip: offsetNum
    })

    // 获取总数
    const total = await prisma.taskLog.count({ where })

    return res.status(200).json({
      success: true,
      data: {
        logs,
        pagination: {
          total,
          limit: limitNum,
          offset: offsetNum,
          hasMore: offsetNum + limitNum < total
        }
      }
    })

  } catch (error) {
    console.error('查询日志失败:', error)
    return res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : '查询日志失败'
    })
  }
}