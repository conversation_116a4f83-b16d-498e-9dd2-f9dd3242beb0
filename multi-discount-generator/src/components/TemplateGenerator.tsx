'use client'

import { useState, useCallback, useEffect } from 'react'
import { Card, CardHeader, CardTitle, CardContent, Input, Button } from '@/components/ui'
import { 
  RegionSelector, 
  DiscountSelector, 
  QuantitySelector,
  TimeModeSelector,
  ContinuousTimeConfig,
  RandomTimeConfig
} from '@/components/forms'
import { PreviewTable } from '@/components/templates'
import { ExcelService } from '@/lib/excel'
import { TimeGenerator } from '@/lib/time-generator'
import { validateFormData } from '@/lib/validations'
import { DEFAULT_VALUES } from '@/constants'
import type { FormData, TimeSlot, ExcelRowData, RegionType } from '@/types'
import { clsx } from 'clsx'

interface TemplateGeneratorProps {
  initialData?: Partial<FormData>
}

export function TemplateGenerator({ initialData }: TemplateGeneratorProps) {
  // 表单状态
  const [formData, setFormData] = useState<Partial<FormData>>({
    productId: '',
    region: { type: 'national' },
    minQuantity: DEFAULT_VALUES.MIN_QUANTITY,
    discount: DEFAULT_VALUES.DISCOUNT,
    timeMode: 'continuous',
    startDate: new Date(),
    weekendFullDay: DEFAULT_VALUES.WEEKEND_FULL_DAY,
    days: DEFAULT_VALUES.DAYS,
    timeSlotCount: DEFAULT_VALUES.TIME_SLOT_COUNT,
    slotDuration: DEFAULT_VALUES.SLOT_DURATION,
    ...initialData
  })

  // UI状态
  const [currentStep, setCurrentStep] = useState(1)
  const [timeSlots, setTimeSlots] = useState<TimeSlot[]>([])
  const [excelData, setExcelData] = useState<ExcelRowData[]>([])
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [isGenerating, setIsGenerating] = useState(false)
  const [isExporting, setIsExporting] = useState(false)
  const [exportSuccess, setExportSuccess] = useState(false)

  // 步骤配置
  const steps = [
    { id: 1, title: '基础信息', description: '商品ID和活动参数' },
    { id: 2, title: '时间配置', description: '设置活动时间规则' },
    { id: 3, title: '预览导出', description: '查看结果并导出Excel' }
  ]

  // 表单字段更新
  const updateFormData = useCallback((updates: Partial<FormData>) => {
    setFormData(prev => ({ ...prev, ...updates }))
    // 清除相关错误
    const newErrors = { ...errors }
    Object.keys(updates).forEach(key => {
      delete newErrors[key]
    })
    setErrors(newErrors)
  }, [errors])

  // 验证当前步骤
  const validateCurrentStep = useCallback(() => {
    const validation = validateFormData(formData)
    
    if (!validation.success) {
      const stepErrors: Record<string, string> = {}
      validation.error.issues.forEach((issue: any) => {
        if (issue.path.length > 0) {
          stepErrors[issue.path[0]] = issue.message
        }
      })
      setErrors(stepErrors)
      return false
    }
    
    setErrors({})
    return true
  }, [formData])

  // 生成时间段
  const generateTimeSlots = useCallback(async () => {
    if (!validateCurrentStep()) return

    setIsGenerating(true)
    try {
      let result
      
      if (formData.timeMode === 'continuous') {
        if (!formData.startDate || !formData.startTime || !formData.endTime || !formData.days) {
          throw new Error('连续模式缺少必要参数')
        }
        
        result = TimeGenerator.generateContinuousTimeSlots({
          startDate: formData.startDate,
          startTime: formData.startTime,
          endTime: formData.endTime,
          days: formData.days,
          weekendFullDay: formData.weekendFullDay ?? true
        })
      } else {
        if (!formData.randomStartTime || !formData.timeSlotCount || !formData.slotDuration) {
          throw new Error('随机模式缺少必要参数')
        }
        
        result = TimeGenerator.generateRandomTimeSlots({
          startTime: formData.randomStartTime,
          slotCount: formData.timeSlotCount,
          slotDuration: formData.slotDuration
        })
      }

      if (result.errors.length > 0) {
        throw new Error(result.errors.join('; '))
      }

      const slots = result.slots.map(slot => ({
        startTime: slot.startTime,
        endTime: slot.endTime
      }))

      setTimeSlots(slots)
      
      // 生成Excel数据
      if (formData.productId && formData.region && formData.minQuantity && formData.discount) {
        const excel = ExcelService.generateExcelData(formData as FormData, slots)
        setExcelData(excel)
      }

      // 显示警告
      if (result.warnings.length > 0) {
        console.warn('时间生成警告:', result.warnings)
      }

    } catch (error) {
      console.error('时间生成失败:', error)
      setErrors({ 
        timeGeneration: error instanceof Error ? error.message : '时间生成失败' 
      })
    } finally {
      setIsGenerating(false)
    }
  }, [formData, validateCurrentStep])

  // 导出Excel
  const handleExport = useCallback(async () => {
    if (excelData.length === 0) return

    setIsExporting(true)
    setExportSuccess(false)
    try {
      const workbook = ExcelService.generateWorkbook(formData as FormData, timeSlots)
      const filename = ExcelService.generateFileName(formData.productId || 'template')
      ExcelService.exportToFile(workbook, filename)
      
      // 显示成功提示
      setExportSuccess(true)
      setTimeout(() => setExportSuccess(false), 3000) // 3秒后隐藏
    } catch (error) {
      console.error('Excel导出失败:', error)
      setErrors({ 
        export: error instanceof Error ? error.message : 'Excel导出失败' 
      })
    } finally {
      setIsExporting(false)
    }
  }, [excelData, formData, timeSlots])

  // 下一步
  const handleNext = useCallback(() => {
    if (currentStep === 2) {
      generateTimeSlots()
    }
    if (currentStep < steps.length) {
      setCurrentStep(currentStep + 1)
    }
  }, [currentStep, generateTimeSlots, steps.length])

  // 上一步
  const handlePrevious = useCallback(() => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
    }
  }, [currentStep])

  // 重置表单
  const handleReset = useCallback(() => {
    setFormData({
      productId: '',
      region: { type: 'national' },
      minQuantity: DEFAULT_VALUES.MIN_QUANTITY,
      discount: DEFAULT_VALUES.DISCOUNT,
      timeMode: 'continuous',
      startDate: new Date(),
      weekendFullDay: DEFAULT_VALUES.WEEKEND_FULL_DAY,
      days: DEFAULT_VALUES.DAYS,
      timeSlotCount: DEFAULT_VALUES.TIME_SLOT_COUNT,
      slotDuration: DEFAULT_VALUES.SLOT_DURATION
    })
    setTimeSlots([])
    setExcelData([])
    setErrors({})
    setExportSuccess(false)
    setCurrentStep(1)
  }, [])

  return (
    <div className="mx-auto max-w-6xl space-y-6">
      {/* 步骤指示器 */}
      <div className="flex items-center justify-center space-x-8">
        {steps.map((step, index) => (
          <div key={step.id} className="flex items-center">
            <div className="flex items-center space-x-2">
              <div
                className={clsx(
                  'flex h-8 w-8 items-center justify-center rounded-full text-sm font-medium',
                  currentStep >= step.id
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-200 text-gray-600'
                )}
              >
                {step.id}
              </div>
              <div className="text-sm">
                <div className="font-medium text-gray-900">{step.title}</div>
                <div className="text-gray-500">{step.description}</div>
              </div>
            </div>
            {index < steps.length - 1 && (
              <div className="ml-4 h-px w-16 bg-gray-300" />
            )}
          </div>
        ))}
      </div>

      {/* 步骤内容 */}
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
        {/* 主要内容区域 */}
        <div className="lg:col-span-2">
          {currentStep === 1 && (
            <Card>
              <CardHeader>
                <CardTitle>基础信息配置</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <Input
                    label="商品ID"
                    value={formData.productId || ''}
                    onChange={(e) => updateFormData({ productId: e.target.value })}
                    error={errors.productId}
                    placeholder="请输入商品ID"
                    helperText="商品的唯一标识符"
                  />

                  <RegionSelector
                    label="活动区域"
                    value={formData.region || { type: 'national' }}
                    onChange={(region) => updateFormData({ region })}
                    error={errors.region}
                  />

                  <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                    <QuantitySelector
                      value={formData.minQuantity || DEFAULT_VALUES.MIN_QUANTITY}
                      onChange={(minQuantity) => updateFormData({ minQuantity })}
                      error={errors.minQuantity}
                    />

                    <DiscountSelector
                      value={formData.discount || DEFAULT_VALUES.DISCOUNT}
                      onChange={(discount) => updateFormData({ discount })}
                      error={errors.discount}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {currentStep === 2 && (
            <Card>
              <CardHeader>
                <CardTitle>时间配置</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <TimeModeSelector
                    value={formData.timeMode || 'continuous'}
                    onChange={(timeMode) => updateFormData({ timeMode })}
                  />

                  {formData.timeMode === 'continuous' ? (
                    <ContinuousTimeConfig
                      value={{
                        startDate: formData.startDate,
                        startTime: formData.startTime,
                        endTime: formData.endTime,
                        days: formData.days,
                        weekendFullDay: formData.weekendFullDay
                      }}
                      onChange={(config) => updateFormData(config)}
                      errors={{
                        startDate: errors.startDate,
                        startTime: errors.startTime,
                        endTime: errors.endTime,
                        days: errors.days
                      }}
                    />
                  ) : (
                    <RandomTimeConfig
                      value={{
                        startTime: formData.randomStartTime,
                        slotCount: formData.timeSlotCount,
                        slotDuration: formData.slotDuration
                      }}
                      onChange={(config) => updateFormData(config)}
                      errors={{
                        startTime: errors.randomStartTime,
                        slotCount: errors.timeSlotCount,
                        slotDuration: errors.slotDuration
                      }}
                    />
                  )}

                  {errors.timeGeneration && (
                    <div className="rounded-md bg-red-50 p-3">
                      <p className="text-sm text-red-700">{errors.timeGeneration}</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          )}

          {currentStep === 3 && (
            <Card>
              <CardHeader>
                <CardTitle>预览和导出</CardTitle>
              </CardHeader>
              <CardContent>
                <PreviewTable
                  data={excelData}
                  onExport={handleExport}
                  loading={isExporting}
                />
                
                {/* 导出成功提示 */}
                {exportSuccess && (
                  <div className="mt-4 rounded-md bg-green-50 border border-green-200 p-3">
                    <div className="flex items-center">
                      <span className="text-green-600 mr-2">✅</span>
                      <p className="text-sm text-green-700 font-medium">
                        Excel文件导出成功！请检查浏览器下载文件夹。
                      </p>
                    </div>
                  </div>
                )}
                
                {errors.export && (
                  <div className="mt-4 rounded-md bg-red-50 p-3">
                    <p className="text-sm text-red-700">{errors.export}</p>
                  </div>
                )}
              </CardContent>
            </Card>
          )}
        </div>

        {/* 侧边栏 */}
        <div className="space-y-4">
          {/* 进度卡片 */}
          <Card>
            <CardHeader>
              <CardTitle>配置进度</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-center justify-between text-sm">
                  <span>商品ID</span>
                  <span className={formData.productId ? 'text-green-600' : 'text-gray-400'}>
                    {formData.productId ? '✓' : '○'}
                  </span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span>活动区域</span>
                  <span className={formData.region ? 'text-green-600' : 'text-gray-400'}>
                    {formData.region ? '✓' : '○'}
                  </span>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span>时间配置</span>
                  <span className={timeSlots.length > 0 ? 'text-green-600' : 'text-gray-400'}>
                    {timeSlots.length > 0 ? '✓' : '○'}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 操作按钮 */}
          <div className="space-y-3">
            <div className="flex space-x-2">
              <Button
                variant="outline"
                onClick={handlePrevious}
                disabled={currentStep === 1}
                className="flex-1"
              >
                上一步
              </Button>
              <Button
                onClick={handleNext}
                disabled={currentStep === steps.length}
                loading={isGenerating}
                className="flex-1"
              >
                {currentStep === steps.length ? '完成' : '下一步'}
              </Button>
            </div>
            
            <Button
              variant="outline"
              onClick={handleReset}
              className="w-full"
            >
              重置表单
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}