'use client'

import { useState, useEffect } from 'react'
import { Checkbox } from '@/components/ui'
import { REGIONAL_AREAS } from '@/constants'
import type { RegionType, RegionalArea } from '@/types'
import { clsx } from 'clsx'

interface RegionSelectorProps {
  value: RegionType
  onChange: (value: RegionType) => void
  error?: string
  label?: string
}

export function RegionSelector({ value, onChange, error, label }: RegionSelectorProps) {
  const [selectedRegions, setSelectedRegions] = useState<RegionalArea[]>(
    value.type === 'regional' ? value.regions : []
  )
  const [isNational, setIsNational] = useState(value.type === 'national')

  // 处理全国选择
  const handleNationalChange = (checked: boolean) => {
    setIsNational(checked)
    if (checked) {
      setSelectedRegions([])
      onChange({ type: 'national' })
    } else {
      onChange({ type: 'regional', regions: [] })
    }
  }

  // 处理大区选择
  const handleRegionChange = (region: RegionalArea, checked: boolean) => {
    if (isNational) return // 如果选择了全国，禁用大区选择

    let newRegions: RegionalArea[]
    if (checked) {
      newRegions = [...selectedRegions, region]
    } else {
      newRegions = selectedRegions.filter(r => r !== region)
    }

    setSelectedRegions(newRegions)
    onChange({ type: 'regional', regions: newRegions })
  }

  // 格式化预览结果
  const getPreviewText = () => {
    if (isNational) {
      return '全国'
    } else if (selectedRegions.length > 0) {
      return selectedRegions.join(',')
    } else {
      return '请选择活动区域'
    }
  }

  // 同步外部value变化
  useEffect(() => {
    if (value.type === 'national') {
      setIsNational(true)
      setSelectedRegions([])
    } else {
      setIsNational(false)
      setSelectedRegions(value.regions)
    }
  }, [value])

  return (
    <div className="w-full">
      {label && (
        <label className="mb-2 block text-sm font-medium text-gray-700">
          {label}
        </label>
      )}
      
      <div className="space-y-4 rounded-md border border-gray-300 p-4">
        {/* 全国选项 */}
        <div className="border-b border-gray-200 pb-3">
          <Checkbox
            id="national"
            checked={isNational}
            onChange={(e) => handleNationalChange(e.target.checked)}
            label="全国"
          />
        </div>

        {/* 大区选项 */}
        <div>
          <p className="mb-2 text-sm font-medium text-gray-700">
            或选择指定大区：
          </p>
          <div className="grid grid-cols-2 gap-2 sm:grid-cols-3">
            {REGIONAL_AREAS.map((region) => (
              <Checkbox
                key={region}
                id={`region-${region}`}
                checked={selectedRegions.includes(region)}
                onChange={(e) => handleRegionChange(region, e.target.checked)}
                disabled={isNational}
                label={region}
                className={clsx(
                  isNational && 'opacity-50 cursor-not-allowed'
                )}
              />
            ))}
          </div>
        </div>

        {/* 实时预览 */}
        <div className="mt-3 rounded-md bg-gray-50 p-3">
          <p className="text-sm text-gray-600">
            <span className="font-medium">预览结果：</span>
            <span className={clsx(
              'ml-2',
              isNational || selectedRegions.length > 0 
                ? 'text-gray-900' 
                : 'text-red-500'
            )}>
              {getPreviewText()}
            </span>
          </p>
        </div>
      </div>

      {error && (
        <p className="mt-1 text-sm text-red-600">{error}</p>
      )}

      {/* 帮助文本 */}
      <p className="mt-1 text-xs text-gray-500">
        选择"全国"后将禁用大区选择；选择大区时支持多选
      </p>
    </div>
  )
}