'use client'

import { useState, useEffect } from 'react'
import { format, isBefore, isAfter } from 'date-fns'
import { DateTimePicker, Input, Checkbox, Button } from '@/components/ui'
import { DEFAULT_VALUES, TIME_FORMAT, QUICK_DAYS_OPTIONS } from '@/constants'
import type { ContinuousConfig } from '@/types'
import { clsx } from 'clsx'

interface ContinuousTimeConfigProps {
  value: Partial<ContinuousConfig>
  onChange: (config: Partial<ContinuousConfig>) => void
  errors?: {
    startDate?: string
    startTime?: string
    endTime?: string
    days?: string
  }
}

export function ContinuousTimeConfig({ 
  value, 
  onChange, 
  errors = {} 
}: ContinuousTimeConfigProps) {
  const [isCrossDay, setIsCrossDay] = useState(false)

  // 检测跨天时间段
  useEffect(() => {
    if (value.startTime && value.endTime) {
      const startHour = value.startTime.getHours()
      const startMinute = value.startTime.getMinutes()
      const endHour = value.endTime.getHours()
      const endMinute = value.endTime.getMinutes()
      
      const startTimeInMinutes = startHour * 60 + startMinute
      const endTimeInMinutes = endHour * 60 + endMinute
      
      setIsCrossDay(endTimeInMinutes < startTimeInMinutes)
    }
  }, [value.startTime, value.endTime])

  const handleStartDateChange = (date: Date | null) => {
    onChange({
      ...value,
      startDate: date || undefined
    })
  }

  const handleStartTimeChange = (date: Date | null) => {
    onChange({
      ...value,
      startTime: date || undefined
    })
  }

  const handleEndTimeChange = (date: Date | null) => {
    onChange({
      ...value,
      endTime: date || undefined
    })
  }

  const handleDaysChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const days = parseInt(e.target.value)
    onChange({
      ...value,
      days: isNaN(days) ? undefined : days
    })
  }

  const handleQuickDaysSelect = (days: number) => {
    onChange({
      ...value,
      days
    })
  }

  const handleWeekendFullDayChange = (checked: boolean) => {
    onChange({
      ...value,
      weekendFullDay: checked
    })
  }

  // 生成时间连续性预览
  const generatePreview = () => {
    if (!value.startDate || !value.startTime || !value.endTime || !value.days) {
      return null
    }

    const examples = []
    const today = new Date()
    
    // 工作日示例
    examples.push({
      type: '工作日',
      time: isCrossDay 
        ? `${format(value.startTime, 'HH:mm:ss')} → 次日${format(value.endTime, 'HH:mm:ss')}`
        : `${format(value.startTime, 'HH:mm:ss')} → ${format(value.endTime, 'HH:mm:ss')}`,
      description: '周一至周五按此时间段执行'
    })

    if (value.weekendFullDay) {
      examples.push({
        type: '周末/节假日',
        time: '00:00:00 → 23:59:59',
        description: '周六、周日及法定节假日全天24小时'
      })
    }

    return examples
  }

  const preview = generatePreview()

  return (
    <div className="space-y-6">
      {/* 开始日期 */}
      <DateTimePicker
        label="活动开始日期"
        value={value.startDate}
        onChange={handleStartDateChange}
        showTime={false}
        error={errors.startDate}
        helperText="活动的第一天日期"
      />

      {/* 时间段配置 */}
      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
        <DateTimePicker
          label="开始时间"
          value={value.startTime}
          onChange={handleStartTimeChange}
          timeOnly
          error={errors.startTime}
          helperText="每天活动开始的时间"
        />
        
        <DateTimePicker
          label="结束时间"
          value={value.endTime}
          onChange={handleEndTimeChange}
          timeOnly
          error={errors.endTime}
          helperText="每天活动结束的时间"
        />
      </div>

      {/* 跨天提示 */}
      {isCrossDay && (
        <div className="rounded-md bg-yellow-50 border border-yellow-200 p-3">
          <div className="flex items-center">
            <span className="text-yellow-600 mr-2">⚠️</span>
            <p className="text-sm text-yellow-700">
              <span className="font-medium">检测到跨天时间段：</span>
              活动将从每天{format(value.startTime!, 'HH:mm:ss')}开始，
              到次日{format(value.endTime!, 'HH:mm:ss')}结束
            </p>
          </div>
        </div>
      )}

      {/* 天数配置 */}
      <div className="space-y-3">
        <Input
          label="活动天数"
          type="number"
          min="1"
          max="365"
          value={value.days || DEFAULT_VALUES.DAYS}
          onChange={handleDaysChange}
          error={errors.days}
          helperText="连续活动的天数，范围1-365天"
        />
        
        {/* 快速天数选择 */}
        <div>
          <p className="mb-2 text-sm font-medium text-gray-700">快速选择：</p>
          <div className="flex flex-wrap gap-2">
            {QUICK_DAYS_OPTIONS.map((option) => (
              <Button
                key={option.value}
                variant="outline"
                size="sm"
                onClick={() => handleQuickDaysSelect(option.value)}
                className={clsx(
                  value.days === option.value && 'border-blue-500 bg-blue-50 text-blue-700'
                )}
              >
                {option.label}
              </Button>
            ))}
          </div>
        </div>
      </div>

      {/* 周末全天选项 */}
      <div className="space-y-3">
        <Checkbox
          checked={value.weekendFullDay ?? DEFAULT_VALUES.WEEKEND_FULL_DAY}
          onChange={(e) => handleWeekendFullDayChange(e.target.checked)}
          label="周末及节假日全天24小时"
          helperText="启用后，周六、周日及中国法定节假日将设置为全天活动时间"
        />
      </div>

      {/* 时间连续性预览 */}
      {preview && (
        <div className="rounded-md bg-blue-50 border border-blue-200 p-4">
          <h4 className="text-sm font-medium text-blue-900 mb-3">
            📋 时间安排预览
          </h4>
          <div className="space-y-2">
            {preview.map((item, index) => (
              <div key={index} className="flex items-start space-x-3">
                <div className="min-w-0 flex-1">
                  <div className="flex items-center space-x-2">
                    <span className="text-sm font-medium text-blue-800">
                      {item.type}:
                    </span>
                    <code className="text-sm bg-white px-2 py-1 rounded border">
                      {item.time}
                    </code>
                  </div>
                  <p className="text-xs text-blue-600 mt-1">
                    {item.description}
                  </p>
                </div>
              </div>
            ))}
          </div>
          
          {value.days && (
            <div className="mt-3 pt-3 border-t border-blue-200">
              <p className="text-sm text-blue-700">
                <span className="font-medium">总计：</span>
                将生成连续{value.days}天的活动时间安排
              </p>
            </div>
          )}
        </div>
      )}

      {/* 特殊说明 */}
      <div className="rounded-md bg-gray-50 p-3">
        <h5 className="text-sm font-medium text-gray-900 mb-2">
          ℹ️ 时间处理说明
        </h5>
        <ul className="text-xs text-gray-600 space-y-1">
          <li>• 支持跨天时间段（如19:30-08:00表示晚上到次日早上）</li>
          <li>• 周五按工作日时间，周六延续到23:59:59，周日从00:00:00到周一结束时间</li>
          <li>• 自动识别中国法定节假日，节假日可选择全天或指定时间</li>
          <li>• 所有时间均基于中国时区(Asia/Shanghai)计算</li>
        </ul>
      </div>
    </div>
  )
}