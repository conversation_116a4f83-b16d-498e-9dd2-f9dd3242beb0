'use client'

import { useState } from 'react'
import { clsx } from 'clsx'

interface TimeModeSelectorProps {
  value: 'continuous' | 'random'
  onChange: (mode: 'continuous' | 'random') => void
  label?: string
}

export function TimeModeSelector({ 
  value, 
  onChange, 
  label = '时间生成模式' 
}: TimeModeSelectorProps) {
  const modes = [
    {
      value: 'continuous' as const,
      label: '连续模式',
      description: '生成连续多天的活动时间，支持跨天和节假日处理',
      icon: '📅'
    },
    {
      value: 'random' as const,
      label: '随机模式', 
      description: '生成多个随机时间段，时间段不重叠',
      icon: '🎲'
    }
  ]

  return (
    <div className="w-full">
      {label && (
        <label className="mb-3 block text-sm font-medium text-gray-700">
          {label}
        </label>
      )}
      
      <div className="grid grid-cols-1 gap-3 sm:grid-cols-2">
        {modes.map((mode) => (
          <div
            key={mode.value}
            className={clsx(
              'relative cursor-pointer rounded-lg border p-4 transition-all',
              'hover:border-blue-300 hover:shadow-sm',
              value === mode.value
                ? 'border-blue-500 bg-blue-50 ring-2 ring-blue-500 ring-opacity-20'
                : 'border-gray-300 bg-white'
            )}
            onClick={() => onChange(mode.value)}
          >
            <div className="flex items-start space-x-3">
              <div className="text-2xl">{mode.icon}</div>
              <div className="flex-1">
                <div className="flex items-center">
                  <input
                    type="radio"
                    name="timeMode"
                    value={mode.value}
                    checked={value === mode.value}
                    onChange={() => onChange(mode.value)}
                    className="h-4 w-4 border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <label className="ml-2 text-sm font-medium text-gray-900">
                    {mode.label}
                  </label>
                </div>
                <p className="mt-1 text-xs text-gray-500">
                  {mode.description}
                </p>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* 模式说明 */}
      <div className="mt-3 rounded-md bg-gray-50 p-3">
        <p className="text-sm text-gray-600">
          <span className="font-medium">当前选择：</span>
          {value === 'continuous' ? '连续模式 - 适合长期活动规划' : '随机模式 - 适合灵活时间安排'}
        </p>
      </div>
    </div>
  )
}