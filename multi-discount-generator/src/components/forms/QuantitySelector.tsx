'use client'

import { Select, type SelectOption } from '@/components/ui'
import { QUANTITY_OPTIONS, DEFAULT_VALUES } from '@/constants'

interface QuantitySelectorProps {
  value: number
  onChange: (value: number) => void
  error?: string
  label?: string
}

export function QuantitySelector({ 
  value, 
  onChange, 
  error, 
  label = '满几件' 
}: QuantitySelectorProps) {
  // 生成数量选项
  const quantityOptions: SelectOption[] = QUANTITY_OPTIONS.map(quantity => ({
    value: quantity,
    label: `满${quantity}件`
  }))

  const handleChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const selectedValue = parseInt(e.target.value)
    if (!isNaN(selectedValue)) {
      onChange(selectedValue)
    }
  }

  return (
    <div className="w-full">
      <Select
        label={label}
        value={value || DEFAULT_VALUES.MIN_QUANTITY}
        onChange={handleChange}
        options={quantityOptions}
        placeholder="请选择满件数量"
        error={error}
        helperText={`默认${DEFAULT_VALUES.MIN_QUANTITY}件，范围1-10件`}
      />
      
      {/* 数量说明 */}
      {value && (
        <div className="mt-2 rounded-md bg-green-50 p-2">
          <p className="text-sm text-green-700">
            <span className="font-medium">活动说明：</span>
            购买满{value}件商品即可享受折扣优惠
          </p>
        </div>
      )}
    </div>
  )
}