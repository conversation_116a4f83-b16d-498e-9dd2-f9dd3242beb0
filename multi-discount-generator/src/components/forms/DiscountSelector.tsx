'use client'

import { Select, type SelectOption } from '@/components/ui'
import { DISCOUNT_OPTIONS, DEFAULT_VALUES } from '@/constants'

interface DiscountSelectorProps {
  value: number
  onChange: (value: number) => void
  error?: string
  label?: string
}

export function DiscountSelector({ 
  value, 
  onChange, 
  error, 
  label = '折扣-打几折' 
}: DiscountSelectorProps) {
  // 生成折扣选项
  const discountOptions: SelectOption[] = DISCOUNT_OPTIONS.map(discount => ({
    value: discount,
    label: `${discount}折`
  }))

  const handleChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const selectedValue = parseFloat(e.target.value)
    if (!isNaN(selectedValue)) {
      onChange(selectedValue)
    }
  }

  return (
    <div className="w-full">
      <Select
        label={label}
        value={value || DEFAULT_VALUES.DISCOUNT}
        onChange={handleChange}
        options={discountOptions}
        placeholder="请选择折扣"
        error={error}
        helperText={`默认${DEFAULT_VALUES.DISCOUNT}折，范围5.0-9.9折，步长0.1`}
      />
      
      {/* 折扣预览 */}
      {value && (
        <div className="mt-2 rounded-md bg-blue-50 p-2">
          <p className="text-sm text-blue-700">
            <span className="font-medium">折扣预览：</span>
            原价100元，打{value}折后为{(100 * value / 10).toFixed(2)}元
          </p>
        </div>
      )}
    </div>
  )
}