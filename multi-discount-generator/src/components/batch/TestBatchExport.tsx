'use client'

import { useState, useEffect } from 'react'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { FileUpload } from './FileUpload'

interface TemplateData {
  filename: string
  originalName: string
  templateData: any[]
  columns: string[]
  products: string[]
  summary: {
    totalRows: number
    validProducts: number
    errorCount: number
  }
}

export function TestBatchExport() {
  const [templateData, setTemplateData] = useState<TemplateData | null>(null)
  const [uploadCount, setUploadCount] = useState(0)

  // 处理模板上传成功
  const handleTemplateUpload = (result: any) => {
    console.log('=== 上传回调被调用 ===')
    console.log('上传次数:', uploadCount + 1)
    console.log('result:', result)
    
    setUploadCount(prev => prev + 1)
    
    if (result.success && result.data) {
      console.log('设置模板数据...')
      setTemplateData(result.data)
      console.log('模板数据设置完成')
    } else {
      console.error('上传失败:', result)
      alert('上传失败: ' + (result.error || '未知错误'))
    }
  }

  // 处理模板上传错误
  const handleTemplateUploadError = (error: string) => {
    console.error('上传错误:', error)
    alert('上传错误: ' + error)
  }

  // 监控状态变化
  useEffect(() => {
    console.log('templateData 状态变化:', templateData)
  }, [templateData])

  useEffect(() => {
    console.log('uploadCount 变化:', uploadCount)
  }, [uploadCount])

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">测试批量上传</h1>
        <p className="text-gray-600">测试模板上传功能</p>
      </div>

      {/* 调试信息 */}
      <Card className="p-4 bg-yellow-50">
        <h3 className="font-medium mb-2">调试信息</h3>
        <div className="text-sm space-y-1">
          <div>上传次数: {uploadCount}</div>
          <div>templateData: {templateData ? 'exists' : 'null'}</div>
          <div>数据行数: {templateData?.templateData?.length || 0}</div>
          <div>列数: {templateData?.columns?.length || 0}</div>
        </div>
      </Card>

      {/* 模板上传 */}
      <Card className="p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Excel模板上传</h2>
        
        {!templateData ? (
          <FileUpload
            onSuccess={handleTemplateUpload}
            onError={handleTemplateUploadError}
            loading={false}
            accept=".xlsx,.xls,.csv"
            uploadUrl="/api/templates/upload"
          />
        ) : (
          <div className="space-y-4">
            <div className="text-green-800 bg-green-50 p-4 rounded-lg">
              <div className="font-medium">模板上传成功</div>
              <div className="text-sm mt-1">
                <p>文件名: {templateData.originalName}</p>
                <p>解析到 {templateData.summary.totalRows} 行数据</p>
                <p>有效商品: {templateData.summary.validProducts} 个</p>
              </div>
            </div>
            
            <Button
              variant="outline"
              onClick={() => {
                console.log('重置模板数据')
                setTemplateData(null)
                setUploadCount(0)
              }}
            >
              重新上传
            </Button>
          </div>
        )}
      </Card>

      {/* 数据预览 */}
      {templateData && (
        <Card className="p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">数据预览</h2>
          
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">
                    序号
                  </th>
                  {templateData.columns.map((column, index) => (
                    <th
                      key={index}
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase"
                    >
                      {column}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {templateData.templateData.slice(0, 5).map((row, index) => (
                  <tr key={index} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {index + 1}
                    </td>
                    {templateData.columns.map((column, colIndex) => (
                      <td
                        key={colIndex}
                        className="px-6 py-4 whitespace-nowrap text-sm text-gray-900"
                      >
                        {row[column] || '-'}
                      </td>
                    ))}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          
          {templateData.templateData.length > 5 && (
            <div className="mt-4 text-sm text-gray-500">
              显示前5行，共 {templateData.templateData.length} 行数据
            </div>
          )}
        </Card>
      )}
    </div>
  )
}
