'use client'

import { useState, useEffect } from 'react'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { FileUpload } from './FileUpload'
import { 
  Download, 
  FileText, 
  Upload,
  CheckCircle,
  ChevronLeft,
  ChevronRight,
  Calendar
} from 'lucide-react'

interface TemplateRow {
  '商品ID': string
  '商品标题'?: string
  '活动区域'?: string
  '满几件'?: string
  '折扣'?: string
  [key: string]: any
}

interface TemplateData {
  filename: string
  originalName: string
  templateData: TemplateRow[]
  columns: string[]
  products: string[]
  summary: {
    totalRows: number
    validProducts: number
    errorCount: number
  }
}

export function SimpleBatchExport() {
  const [templateData, setTemplateData] = useState<TemplateData | null>(null)
  const [loading, setLoading] = useState(false)
  const [exporting, setExporting] = useState(false)
  const [currentPage, setCurrentPage] = useState(1)
  const pageSize = 20

  // 处理模板上传成功
  const handleTemplateUpload = (result: any) => {
    console.log('模板上传结果:', result)
    console.log('result.success:', result.success)
    console.log('result.data:', result.data)

    if (result.success && result.data) {
      console.log('设置模板数据:', result.data)
      setTemplateData(result.data)
      setCurrentPage(1)
      console.log('模板数据设置完成')
    } else {
      console.error('模板上传失败:', result)
      alert('模板上传失败: ' + (result.error || '未知错误'))
    }
  }

  // 处理模板上传错误
  const handleTemplateUploadError = (error: string) => {
    alert('模板上传失败: ' + error)
  }

  // 处理批量生成活动时间表
  const handleGenerateSchedule = async () => {
    if (!templateData || !templateData.templateData || templateData.templateData.length === 0) {
      alert('请先上传模板文件')
      return
    }

    try {
      setExporting(true)

      // 验证模板数据格式
      const firstRow = templateData.templateData[0]
      const requiredFields = ['商品ID', '活动区域', '满几件', '折扣']
      const missingFields = requiredFields.filter(field => !(field in firstRow))

      if (missingFields.length > 0) {
        alert(`模板格式不正确，缺少必需字段: ${missingFields.join(', ')}`)
        return
      }

      const response = await fetch('/api/batch/generate-tasks', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          templateData: templateData.templateData,
          configs: {
            // 使用模板中的配置，如果没有则使用默认值
            timeMode: firstRow['时间模式'] || 'continuous',
            startDate: firstRow['开始日期'] || '2024-01-15',
            endDate: firstRow['结束日期'] || '2024-01-21',
            dailyStartTime: firstRow['每日开始时间'] || '09:00:00',
            dailyEndTime: firstRow['每日结束时间'] || '21:00:00',
            weekendFullDay: firstRow['周末全天'] === '是',
            holidayFullDay: firstRow['节假日全天'] === '是',
            timeSlotCount: parseInt(firstRow['时间段数量']) || 5,
            slotDuration: parseInt(firstRow['时段时长']) || 1
          }
        })
      })

      const result = await response.json()

      if (result.success) {
        alert(`成功创建 ${result.data.taskIds.length} 个活动时间生成任务！请到任务管理页面查看进度和导出结果。`)
      } else {
        throw new Error(result.error || '创建任务失败')
      }
    } catch (error) {
      console.error('批量生成失败:', error)
      alert('批量生成失败: ' + error.message)
    } finally {
      setExporting(false)
    }
  }

  // 监控templateData变化
  useEffect(() => {
    console.log('templateData 状态变化:', templateData)
  }, [templateData])

  // 分页数据
  const totalPages = templateData ? Math.ceil(templateData.templateData.length / pageSize) : 0
  const startIndex = (currentPage - 1) * pageSize
  const endIndex = startIndex + pageSize
  const currentData = templateData ? templateData.templateData.slice(startIndex, endIndex) : []

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">批量生成活动时间表</h1>
        <p className="text-gray-600">上传Excel模板，批量生成活动时间任务</p>
        {/* 调试信息 */}
        {process.env.NODE_ENV === 'development' && (
          <div className="mt-2 p-2 bg-yellow-50 border border-yellow-200 rounded text-xs">
            <strong>调试信息:</strong> templateData = {templateData ? 'exists' : 'null'},
            rows = {templateData?.templateData?.length || 0}
          </div>
        )}
      </div>

      {/* 模板上传 */}
      <Card className="p-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold text-gray-900">Excel模板上传</h2>
          {templateData && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                setTemplateData(null)
                setCurrentPage(1)
              }}
            >
              重新上传
            </Button>
          )}
        </div>

        <div className="space-y-4">
          {!templateData && (
            <>
              <FileUpload
                onSuccess={handleTemplateUpload}
                onError={handleTemplateUploadError}
                loading={loading}
                accept=".xlsx,.xls,.csv"
                uploadUrl="/api/templates/upload"
              />
              <div className="text-sm text-gray-600">
                <p>• 支持Excel (.xlsx, .xls) 和CSV格式</p>
                <p>• 请使用模板管理中的"批量模板"格式</p>
                <p>• 必需字段：商品ID、活动区域、满几件、折扣</p>
                <p>• 可选字段：时间模式、开始日期、结束日期、每日时间等</p>
                <p>• 如需下载标准模板，请访问 <a href="/templates" className="text-blue-600 hover:underline">模板管理</a> 页面</p>
              </div>
            </>
          )}

          {templateData && (
            <div className="space-y-4">
              <div className="flex items-center text-green-800 bg-green-50 p-4 rounded-lg">
                <CheckCircle className="w-5 h-5 mr-2" />
                <div>
                  <span className="font-medium">模板上传成功</span>
                  <div className="text-sm text-green-700 mt-1">
                    <p>文件名: {templateData.originalName}</p>
                    <p>解析到 {templateData.summary.totalRows} 行数据，{templateData.summary.validProducts} 个有效商品ID</p>
                  </div>
                </div>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  setTemplateData(null)
                  setCurrentPage(1)
                }}
              >
                重新上传
              </Button>
            </div>
          )}
        </div>
      </Card>

      {/* 表格数据显示 */}
      {templateData && (
        <Card className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-gray-900">表格数据预览</h2>
            <Button
              onClick={handleGenerateSchedule}
              disabled={exporting}
              className="bg-blue-600 hover:bg-blue-700"
            >
              <Calendar className="w-4 h-4 mr-2" />
              {exporting ? '生成中...' : '生成活动时间表'}
            </Button>
          </div>

          {/* 数据统计 */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div className="bg-blue-50 p-4 rounded-lg">
              <div className="flex items-center">
                <FileText className="w-8 h-8 text-blue-500 mr-3" />
                <div>
                  <p className="text-sm font-medium text-gray-600">总行数</p>
                  <p className="text-2xl font-bold text-gray-900">{templateData.summary.totalRows}</p>
                </div>
              </div>
            </div>
            
            <div className="bg-green-50 p-4 rounded-lg">
              <div className="flex items-center">
                <CheckCircle className="w-8 h-8 text-green-500 mr-3" />
                <div>
                  <p className="text-sm font-medium text-gray-600">有效商品</p>
                  <p className="text-2xl font-bold text-gray-900">{templateData.summary.validProducts}</p>
                </div>
              </div>
            </div>
            
            <div className="bg-gray-50 p-4 rounded-lg">
              <div className="flex items-center">
                <Calendar className="w-8 h-8 text-gray-500 mr-3" />
                <div>
                  <p className="text-sm font-medium text-gray-600">当前页</p>
                  <p className="text-2xl font-bold text-gray-900">{currentPage}/{totalPages}</p>
                </div>
              </div>
            </div>
          </div>

          {/* 数据表格 */}
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    序号
                  </th>
                  {templateData.columns.map((column, index) => (
                    <th
                      key={index}
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      {column}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {currentData.map((row, index) => (
                  <tr key={startIndex + index} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {startIndex + index + 1}
                    </td>
                    {templateData.columns.map((column, colIndex) => (
                      <td
                        key={colIndex}
                        className="px-6 py-4 whitespace-nowrap text-sm text-gray-900"
                      >
                        {row[column] || '-'}
                      </td>
                    ))}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* 分页控件 */}
          {totalPages > 1 && (
            <div className="flex items-center justify-between mt-6">
              <div className="text-sm text-gray-700">
                显示第 {startIndex + 1} 到 {Math.min(endIndex, templateData.templateData.length)} 条，
                共 {templateData.templateData.length} 条记录
              </div>
              
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                  disabled={currentPage === 1}
                >
                  <ChevronLeft className="w-4 h-4" />
                  上一页
                </Button>
                
                <span className="text-sm text-gray-700">
                  第 {currentPage} 页，共 {totalPages} 页
                </span>
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                  disabled={currentPage === totalPages}
                >
                  下一页
                  <ChevronRight className="w-4 h-4" />
                </Button>
              </div>
            </div>
          )}
        </Card>
      )}
    </div>
  )
}
