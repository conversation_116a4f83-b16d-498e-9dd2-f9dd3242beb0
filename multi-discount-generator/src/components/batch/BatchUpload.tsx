'use client'

import { useState, useCallback } from 'react'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { FileUpload } from './FileUpload'
import { ProductPreview } from './ProductPreview'
import { BatchConfigForm } from './BatchConfigForm'
import type { 
  UploadResult, 
  PreviewData, 
  BatchConfig,
  ProductData 
} from '@/types/batch'

type Step = 'upload' | 'preview' | 'config' | 'creating'

interface BatchUploadProps {
  onTaskCreated?: (taskId: string) => void
}

export function BatchUpload({ onTaskCreated }: BatchUploadProps) {
  const [currentStep, setCurrentStep] = useState<Step>('upload')
  const [uploadResult, setUploadResult] = useState<UploadResult | null>(null)
  const [previewData, setPreviewData] = useState<PreviewData | null>(null)
  const [validProducts, setValidProducts] = useState<ProductData[]>([])
  const [error, setError] = useState<string | null>(null)
  const [loading, setLoading] = useState(false)

  // 处理文件上传成功
  const handleUploadSuccess = useCallback(async (result: UploadResult) => {
    try {
      setLoading(true)
      setError(null)
      setUploadResult(result)

      // 获取预览数据
      const response = await fetch(`/api/files/preview?filename=${result.filename}`)
      const previewResult = await response.json()

      if (previewResult.success) {
        setPreviewData(previewResult.data)
        setValidProducts(previewResult.data.validProducts)
        setCurrentStep('preview')
      } else {
        throw new Error(previewResult.error || '获取预览数据失败')
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : '处理文件失败')
    } finally {
      setLoading(false)
    }
  }, [])

  // 处理上传错误
  const handleUploadError = useCallback((error: string) => {
    setError(error)
    setCurrentStep('upload')
  }, [])

  // 继续到配置步骤
  const handleContinueToConfig = useCallback(() => {
    setCurrentStep('config')
  }, [])

  // 返回上一步
  const handleBack = useCallback(() => {
    if (currentStep === 'preview') {
      setCurrentStep('upload')
      setUploadResult(null)
      setPreviewData(null)
      setValidProducts([])
    } else if (currentStep === 'config') {
      setCurrentStep('preview')
    }
    setError(null)
  }, [currentStep])

  // 处理任务创建
  const handleTaskCreate = useCallback(async (config: BatchConfig) => {
    try {
      setLoading(true)
      setError(null)
      setCurrentStep('creating')

      const response = await fetch('/api/tasks/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          filename: uploadResult?.filename,
          config,
          products: validProducts
        })
      })

      const result = await response.json()

      if (result.success) {
        // 任务创建成功，通知父组件
        onTaskCreated?.(result.data.taskId)
      } else {
        throw new Error(result.error || '创建任务失败')
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : '创建任务失败')
      setCurrentStep('config')
    } finally {
      setLoading(false)
    }
  }, [uploadResult, validProducts, onTaskCreated])

  // 重置状态
  const handleReset = useCallback(() => {
    setCurrentStep('upload')
    setUploadResult(null)
    setPreviewData(null)
    setValidProducts([])
    setError(null)
    setLoading(false)
  }, [])

  // 渲染步骤指示器
  const renderStepIndicator = () => {
    const steps = [
      { key: 'upload', label: '上传文件', completed: currentStep !== 'upload' },
      { key: 'preview', label: '预览数据', completed: ['config', 'creating'].includes(currentStep) },
      { key: 'config', label: '配置任务', completed: currentStep === 'creating' }
    ]

    return (
      <div className="flex items-center justify-center mb-8">
        {steps.map((step, index) => (
          <div key={step.key} className="flex items-center">
            <div className={`
              flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium
              ${step.completed 
                ? 'bg-green-500 text-white' 
                : currentStep === step.key
                  ? 'bg-blue-500 text-white'
                  : 'bg-gray-200 text-gray-600'
              }
            `}>
              {step.completed ? '✓' : index + 1}
            </div>
            <span className={`ml-2 text-sm font-medium ${
              step.completed ? 'text-green-600' : 
              currentStep === step.key ? 'text-blue-600' : 'text-gray-500'
            }`}>
              {step.label}
            </span>
            {index < steps.length - 1 && (
              <div className={`mx-4 h-0.5 w-8 ${
                step.completed ? 'bg-green-500' : 'bg-gray-200'
              }`} />
            )}
          </div>
        ))}
      </div>
    )
  }

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">批量上传</h1>
        <p className="text-gray-600">
          上传包含商品ID的Excel文件，批量生成多件多折活动模板
        </p>
      </div>

      {renderStepIndicator()}

      {error && (
        <Card className="p-4 mb-6 bg-red-50 border-red-200">
          <div className="flex items-center space-x-2">
            <div className="w-4 h-4 bg-red-500 rounded-full flex-shrink-0" />
            <span className="text-red-800 font-medium">错误</span>
          </div>
          <p className="text-red-700 mt-1">{error}</p>
          <Button
            variant="outline"
            size="sm"
            className="mt-2"
            onClick={() => setError(null)}
          >
            关闭
          </Button>
        </Card>
      )}

      {/* 上传步骤 */}
      {currentStep === 'upload' && (
        <FileUpload
          onSuccess={handleUploadSuccess}
          onError={handleUploadError}
          loading={loading}
        />
      )}

      {/* 预览步骤 */}
      {currentStep === 'preview' && uploadResult && previewData && (
        <div className="space-y-6">
          <ProductPreview
            filename={uploadResult.filename}
            previewData={previewData}
          />

          <div className="flex justify-between">
            <Button variant="outline" onClick={handleBack}>
              重新上传
            </Button>
            <Button 
              onClick={handleContinueToConfig}
              disabled={validProducts.length === 0}
            >
              继续配置 ({validProducts.length} 个商品)
            </Button>
          </div>
        </div>
      )}

      {/* 配置步骤 */}
      {currentStep === 'config' && (
        <div className="space-y-6">
          <Card className="p-4 bg-blue-50 border-blue-200">
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 bg-blue-500 rounded-full flex-shrink-0" />
              <span className="text-blue-800 font-medium">准备就绪</span>
            </div>
            <p className="text-blue-700 mt-1">
              已验证 {validProducts.length} 个有效商品ID，请配置活动参数
            </p>
          </Card>

          <BatchConfigForm
            productCount={validProducts.length}
            onSubmit={handleTaskCreate}
            onBack={handleBack}
            disabled={loading}
          />
        </div>
      )}

      {/* 创建中步骤 */}
      {currentStep === 'creating' && (
        <Card className="p-8">
          <div className="text-center space-y-4">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto" />
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">正在创建批量任务</h3>
              <p className="text-gray-600">
                正在处理 {validProducts.length} 个商品，请稍候...
              </p>
            </div>
          </div>
        </Card>
      )}

      {/* 帮助信息 */}
      {currentStep === 'upload' && (
        <Card className="p-4 mt-6 bg-gray-50">
          <h4 className="font-medium text-gray-900 mb-2">使用说明</h4>
          <ul className="text-sm text-gray-600 space-y-1">
            <li>• 支持上传 .xlsx、.xls、.csv 格式的文件</li>
            <li>• 文件第一列应包含商品ID</li>
            <li>• 系统会自动验证商品ID的有效性</li>
            <li>• 单次最多支持处理 1000 个商品</li>
          </ul>
        </Card>
      )}
    </div>
  )
}