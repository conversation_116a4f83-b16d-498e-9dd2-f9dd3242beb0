'use client'

import { useState, useCallback, useRef } from 'react'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import type { UploadResult } from '@/types/batch'

interface FileUploadProps {
  onSuccess: (result: UploadResult) => void
  onError: (error: string) => void
  loading?: boolean
  accept?: string
  uploadUrl?: string
}

export function FileUpload({
  onSuccess,
  onError,
  loading = false,
  accept = '.xlsx,.xls,.csv',
  uploadUrl = '/api/files/upload'
}: FileUploadProps) {
  const [isDragging, setIsDragging] = useState(false)
  const [uploading, setUploading] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const disabled = loading || uploading

  // 验证文件类型和大小
  const validateFile = (file: File): string | null => {
    const allowedTypes = [
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
      'application/vnd.ms-excel', // .xls
      'text/csv' // .csv
    ]

    const allowedExtensions = ['.xlsx', '.xls', '.csv']
    const fileExtension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'))

    if (!allowedTypes.includes(file.type) && !allowedExtensions.includes(fileExtension)) {
      return '不支持的文件格式，请上传 .xlsx、.xls 或 .csv 文件'
    }

    // 限制文件大小为 10MB
    const maxSize = 10 * 1024 * 1024
    if (file.size > maxSize) {
      return '文件大小不能超过 10MB'
    }

    return null
  }

  // 处理文件选择
  const handleFileSelect = useCallback(async (file: File) => {
    if (disabled) return

    // 验证文件
    const error = validateFile(file)
    if (error) {
      onError(error)
      return
    }

    setUploading(true)

    try {
      // 创建FormData
      const formData = new FormData()
      formData.append('file', file)

      // 上传文件
      const response = await fetch(uploadUrl, {
        method: 'POST',
        body: formData
      })

      const result = await response.json()

      if (result.success) {
        onSuccess(result)
      } else {
        throw new Error(result.error || '上传失败')
      }
    } catch (error) {
      onError(error instanceof Error ? error.message : '上传失败')
    } finally {
      setUploading(false)
    }
  }, [disabled, onSuccess, onError])

  // 处理拖拽事件
  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    if (!disabled) {
      setIsDragging(true)
    }
  }, [disabled])

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(false)
  }, [])

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(false)

    if (disabled) return

    const files = Array.from(e.dataTransfer.files)
    if (files.length > 0) {
      handleFileSelect(files[0])
    }
  }, [disabled, handleFileSelect])

  // 处理点击上传
  const handleClick = useCallback(() => {
    if (!disabled && fileInputRef.current) {
      fileInputRef.current.click()
    }
  }, [disabled])

  // 处理文件输入变化
  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files
    if (files && files.length > 0) {
      handleFileSelect(files[0])
    }
    // 清空input值，允许重复选择同一文件
    e.target.value = ''
  }, [handleFileSelect])

  return (
    <Card className="p-6">
      <div
        className={`
          relative border-2 border-dashed rounded-lg p-8 text-center transition-colors
          ${isDragging 
            ? 'border-blue-400 bg-blue-50' 
            : 'border-gray-300 hover:border-gray-400'
          }
          ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
        `}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        onClick={handleClick}
      >
        <input
          ref={fileInputRef}
          type="file"
          accept=".xlsx,.xls,.csv"
          onChange={handleInputChange}
          className="hidden"
          disabled={disabled}
        />

        {uploading ? (
          <div className="space-y-4">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto" />
            <div>
              <h3 className="text-lg font-medium text-gray-900">正在上传文件</h3>
              <p className="text-gray-600 mt-1">请稍候，正在处理您的文件...</p>
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            <div className="mx-auto w-12 h-12 text-gray-400">
              <svg fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={1}
                  d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
                />
              </svg>
            </div>
            
            <div>
              <h3 className="text-lg font-medium text-gray-900">
                {isDragging ? '释放文件以上传' : '上传Excel文件'}
              </h3>
              <p className="text-gray-600 mt-1">
                拖拽文件到此处，或点击选择文件
              </p>
            </div>

            <div className="text-sm text-gray-500">
              <p>支持格式: .xlsx, .xls, .csv</p>
              <p>最大文件大小: 10MB</p>
            </div>

            <Button
              variant="outline"
              disabled={disabled}
              onClick={(e) => {
                e.stopPropagation()
                handleClick()
              }}
            >
              选择文件
            </Button>
          </div>
        )}
      </div>

      {/* 使用提示 */}
      <div className="mt-4 p-3 bg-gray-50 rounded-lg">
        <h4 className="text-sm font-medium text-gray-900 mb-2">文件格式要求</h4>
        <ul className="text-xs text-gray-600 space-y-1">
          <li>• 第一列必须包含商品ID</li>
          <li>• 支持Excel (.xlsx, .xls) 和CSV (.csv) 格式</li>
          <li>• 第一行可以是标题行（会自动识别）</li>
          <li>• 空行和无效数据会被自动过滤</li>
        </ul>
      </div>
    </Card>
  )
}