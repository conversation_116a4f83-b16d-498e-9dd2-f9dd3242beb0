'use client'

import { useState } from 'react'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { RegionSelector } from '@/components/forms/RegionSelector'
import { DiscountSelector } from '@/components/forms/DiscountSelector'
import { TimeModeSelector } from '@/components/forms/TimeModeSelector'
import { ContinuousTimeConfig } from '@/components/forms/ContinuousTimeConfig'
import { RandomTimeConfig } from '@/components/forms/RandomTimeConfig'
import type { FormData, RegionType } from '@/types'

interface BatchConfigFormProps {
  onSubmit: (config: FormData) => void
  onCancel: () => void
  disabled?: boolean
  productCount: number
}

export function BatchConfigForm({
  onSubmit,
  onCancel,
  disabled = false,
  productCount
}: BatchConfigFormProps) {
  const [formData, setFormData] = useState<FormData>({
    productId: '', // 批量处理时不需要单个商品ID
    region: { type: 'national' },
    minQuantity: 2,
    discount: 8.0,
    timeMode: 'continuous',
    startDate: new Date(),
    startTime: new Date(),
    endTime: new Date(),
    days: 7,
    weekendFullDay: true
  })

  const [taskName, setTaskName] = useState('')
  const [errors, setErrors] = useState<Record<string, string>>({})

  // 验证表单
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {}

    if (!taskName.trim()) {
      newErrors.taskName = '请输入任务名称'
    }

    if (formData.timeMode === 'continuous') {
      if (!formData.startDate) {
        newErrors.startDate = '请选择开始日期'
      }
      if (!formData.startTime) {
        newErrors.startTime = '请选择开始时间'
      }
      if (!formData.endTime) {
        newErrors.endTime = '请选择结束时间'
      }
      if (!formData.days || formData.days < 1) {
        newErrors.days = '持续天数必须大于0'
      }
    } else if (formData.timeMode === 'random') {
      if (!formData.randomStartTime) {
        newErrors.randomStartTime = '请选择随机开始时间'
      }
      if (!formData.timeSlotCount || formData.timeSlotCount < 1) {
        newErrors.timeSlotCount = '时间段数量必须大于0'
      }
      if (!formData.slotDuration || formData.slotDuration < 1) {
        newErrors.slotDuration = '时间段时长必须大于0'
      }
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  // 处理表单提交
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    // 添加任务名称到配置中
    const configWithName = {
      ...formData,
      taskName: taskName.trim()
    }

    onSubmit(configWithName)
  }

  // 更新表单数据
  const updateFormData = (updates: Partial<FormData>) => {
    setFormData(prev => ({ ...prev, ...updates }))
    
    // 清除相关错误
    const newErrors = { ...errors }
    Object.keys(updates).forEach(key => {
      delete newErrors[key]
    })
    setErrors(newErrors)
  }

  return (
    <Card className="p-6">
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* 任务信息 */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium">任务配置</h3>
          
          <div className="grid md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                任务名称 *
              </label>
              <Input
                value={taskName}
                onChange={(e) => setTaskName(e.target.value)}
                placeholder="请输入任务名称"
                disabled={disabled}
                className={errors.taskName ? 'border-red-300' : ''}
              />
              {errors.taskName && (
                <p className="text-sm text-red-600 mt-1">{errors.taskName}</p>
              )}
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                商品数量
              </label>
              <Input
                value={productCount.toString()}
                disabled
                className="bg-gray-50"
              />
            </div>
          </div>
        </div>

        {/* 活动区域 */}
        <div className="space-y-4">
          <h4 className="font-medium">活动区域</h4>
          <RegionSelector
            value={formData.region}
            onChange={(region) => updateFormData({ region })}
            disabled={disabled}
          />
        </div>

        {/* 优惠设置 */}
        <div className="space-y-4">
          <h4 className="font-medium">优惠设置</h4>
          <DiscountSelector
            minQuantity={formData.minQuantity}
            discount={formData.discount}
            onMinQuantityChange={(minQuantity) => updateFormData({ minQuantity })}
            onDiscountChange={(discount) => updateFormData({ discount })}
            disabled={disabled}
          />
        </div>

        {/* 时间模式 */}
        <div className="space-y-4">
          <h4 className="font-medium">时间设置</h4>
          <TimeModeSelector
            value={formData.timeMode}
            onChange={(timeMode) => updateFormData({ timeMode })}
            disabled={disabled}
          />

          {formData.timeMode === 'continuous' && (
            <ContinuousTimeConfig
              startDate={formData.startDate}
              startTime={formData.startTime}
              endTime={formData.endTime}
              days={formData.days}
              weekendFullDay={formData.weekendFullDay}
              onStartDateChange={(startDate) => updateFormData({ startDate })}
              onStartTimeChange={(startTime) => updateFormData({ startTime })}
              onEndTimeChange={(endTime) => updateFormData({ endTime })}
              onDaysChange={(days) => updateFormData({ days })}
              onWeekendFullDayChange={(weekendFullDay) => updateFormData({ weekendFullDay })}
              disabled={disabled}
              errors={errors}
            />
          )}

          {formData.timeMode === 'random' && (
            <RandomTimeConfig
              randomStartTime={formData.randomStartTime}
              timeSlotCount={formData.timeSlotCount}
              slotDuration={formData.slotDuration}
              onRandomStartTimeChange={(randomStartTime) => updateFormData({ randomStartTime })}
              onTimeSlotCountChange={(timeSlotCount) => updateFormData({ timeSlotCount })}
              onSlotDurationChange={(slotDuration) => updateFormData({ slotDuration })}
              disabled={disabled}
              errors={errors}
            />
          )}
        </div>

        {/* 预估信息 */}
        <div className="bg-blue-50 p-4 rounded-lg">
          <h4 className="font-medium text-blue-900 mb-2">预估信息</h4>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <span className="text-gray-600">商品数量：</span>
              <span className="font-medium">{productCount}</span>
            </div>
            <div>
              <span className="text-gray-600">时间段数：</span>
              <span className="font-medium">
                {formData.timeMode === 'continuous' 
                  ? (formData.days || 0) 
                  : (formData.timeSlotCount || 0)
                }
              </span>
            </div>
            <div>
              <span className="text-gray-600">预计行数：</span>
              <span className="font-medium">
                {productCount * (formData.timeMode === 'continuous' 
                  ? (formData.days || 0) 
                  : (formData.timeSlotCount || 0)
                )}
              </span>
            </div>
            <div>
              <span className="text-gray-600">文件大小：</span>
              <span className="font-medium">
                ~{Math.round(productCount * (formData.timeMode === 'continuous' 
                  ? (formData.days || 0) 
                  : (formData.timeSlotCount || 0)
                ) * 0.2)}KB
              </span>
            </div>
          </div>
        </div>

        {/* 操作按钮 */}
        <div className="flex justify-end space-x-3 pt-4 border-t">
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            disabled={disabled}
          >
            取消
          </Button>
          <Button
            type="submit"
            disabled={disabled}
          >
            {disabled ? '创建中...' : '创建批量任务'}
          </Button>
        </div>
      </form>
    </Card>
  )
}