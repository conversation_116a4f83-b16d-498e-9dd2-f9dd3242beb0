'use client'

import { useState } from 'react'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import type { PreviewData, ProductData } from '@/types/batch'

interface ProductPreviewProps {
  filename: string
  previewData: PreviewData
}

export function ProductPreview({ filename, previewData }: ProductPreviewProps) {
  const [currentPage, setCurrentPage] = useState(1)
  const [showInvalid, setShowInvalid] = useState(false)
  const itemsPerPage = 10

  const displayProducts = showInvalid ? previewData.invalidProducts : previewData.validProducts
  const totalPages = Math.ceil(displayProducts.length / itemsPerPage)
  const startIndex = (currentPage - 1) * itemsPerPage
  const endIndex = startIndex + itemsPerPage
  const currentProducts = displayProducts.slice(startIndex, endIndex)

  // 渲染统计信息
  const renderStatistics = () => {
    if (!previewData) return null

    const { statistics } = previewData
    
    return (
      <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mb-6">
        <div className="text-center p-3 bg-blue-50 rounded-lg">
          <div className="text-2xl font-bold text-blue-600">{statistics.totalCount}</div>
          <div className="text-sm text-gray-600">总数量</div>
        </div>
        <div className="text-center p-3 bg-green-50 rounded-lg">
          <div className="text-2xl font-bold text-green-600">{statistics.validCount}</div>
          <div className="text-sm text-gray-600">有效</div>
        </div>
        <div className="text-center p-3 bg-yellow-50 rounded-lg">
          <div className="text-2xl font-bold text-yellow-600">{statistics.duplicateCount}</div>
          <div className="text-sm text-gray-600">重复</div>
        </div>
        <div className="text-center p-3 bg-red-50 rounded-lg">
          <div className="text-2xl font-bold text-red-600">{statistics.invalidCount}</div>
          <div className="text-sm text-gray-600">无效</div>
        </div>
        <div className="text-center p-3 bg-gray-50 rounded-lg">
          <div className="text-2xl font-bold text-gray-600">{statistics.emptyCount}</div>
          <div className="text-sm text-gray-600">空值</div>
        </div>
      </div>
    )
  }

  // 渲染产品表格
  const renderProductTable = () => {
    if (currentProducts.length === 0) {
      return (
        <div className="text-center py-8 text-gray-500">
          {showInvalid ? '没有无效的商品ID' : '没有有效的商品ID'}
        </div>
      )
    }

    return (
      <div className="overflow-x-auto">
        <table className="w-full border-collapse">
          <thead>
            <tr className="bg-gray-50">
              <th className="border border-gray-200 px-4 py-2 text-left text-sm font-medium text-gray-900">
                行号
              </th>
              <th className="border border-gray-200 px-4 py-2 text-left text-sm font-medium text-gray-900">
                商品ID
              </th>
              <th className="border border-gray-200 px-4 py-2 text-left text-sm font-medium text-gray-900">
                状态
              </th>
              {!showInvalid && (
                <>
                  <th className="border border-gray-200 px-4 py-2 text-left text-sm font-medium text-gray-900">
                    商品名称
                  </th>
                  <th className="border border-gray-200 px-4 py-2 text-left text-sm font-medium text-gray-900">
                    价格
                  </th>
                </>
              )}
              {showInvalid && (
                <th className="border border-gray-200 px-4 py-2 text-left text-sm font-medium text-gray-900">
                  错误原因
                </th>
              )}
            </tr>
          </thead>
          <tbody>
            {currentProducts.map((product, index) => (
              <tr key={`${product.rowIndex}-${product.productId}`} className="hover:bg-gray-50">
                <td className="border border-gray-200 px-4 py-2 text-sm text-gray-900">
                  {product.rowIndex}
                </td>
                <td className="border border-gray-200 px-4 py-2 text-sm">
                  <code className="bg-gray-100 px-2 py-1 rounded text-xs">
                    {product.productId}
                  </code>
                </td>
                <td className="border border-gray-200 px-4 py-2 text-sm">
                  <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                    product.isValid
                      ? 'bg-green-100 text-green-800'
                      : 'bg-red-100 text-red-800'
                  }`}>
                    {product.isValid ? '有效' : '无效'}
                  </span>
                </td>
                {!showInvalid && product.isValid && (
                  <>
                    <td className="border border-gray-200 px-4 py-2 text-sm text-gray-900">
                      {product.productName || '-'}
                    </td>
                    <td className="border border-gray-200 px-4 py-2 text-sm text-gray-900">
                      {product.price ? `¥${product.price}` : '-'}
                    </td>
                  </>
                )}
                {showInvalid && !product.isValid && (
                  <td className="border border-gray-200 px-4 py-2 text-sm text-red-600">
                    {product.error || '未知错误'}
                  </td>
                )}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    )
  }

  // 渲染分页
  const renderPagination = () => {
    if (totalPages <= 1) return null

    return (
      <div className="flex items-center justify-between mt-4">
        <div className="text-sm text-gray-600">
          显示 {startIndex + 1}-{Math.min(endIndex, displayProducts.length)} 条，
          共 {displayProducts.length} 条记录
        </div>
        <div className="flex space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
            disabled={currentPage === 1}
          >
            上一页
          </Button>
          <span className="flex items-center px-3 py-1 text-sm text-gray-600">
            {currentPage} / {totalPages}
          </span>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
            disabled={currentPage === totalPages}
          >
            下一页
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* 文件信息 */}
      <Card className="p-4">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-medium text-gray-900">文件预览</h3>
            <p className="text-sm text-gray-600 mt-1">
              文件名: <code className="bg-gray-100 px-2 py-1 rounded">{filename}</code>
            </p>
          </div>
          <div className="flex space-x-2">
            <Button
              variant={showInvalid ? "outline" : "default"}
              size="sm"
              onClick={() => {
                setShowInvalid(false)
                setCurrentPage(1)
              }}
            >
              有效商品 ({previewData.statistics.validCount})
            </Button>
            <Button
              variant={showInvalid ? "default" : "outline"}
              size="sm"
              onClick={() => {
                setShowInvalid(true)
                setCurrentPage(1)
              }}
              disabled={previewData.statistics.invalidCount === 0}
            >
              无效商品 ({previewData.statistics.invalidCount})
            </Button>
          </div>
        </div>
      </Card>

      {/* 统计信息 */}
      {renderStatistics()}

      {/* 产品表格 */}
      <Card className="p-4">
        <div className="mb-4">
          <h4 className="text-md font-medium text-gray-900">
            {showInvalid ? '无效商品列表' : '有效商品列表'}
          </h4>
          <p className="text-sm text-gray-600 mt-1">
            {showInvalid 
              ? '以下商品ID无效，将不会包含在批量任务中'
              : '以下商品ID有效，将用于生成活动模板'
            }
          </p>
        </div>

        {renderProductTable()}
        {renderPagination()}
      </Card>

      {/* 提示信息 */}
      {previewData.statistics.invalidCount > 0 && (
        <Card className="p-4 bg-yellow-50 border-yellow-200">
          <div className="flex items-start space-x-2">
            <div className="w-4 h-4 bg-yellow-500 rounded-full flex-shrink-0 mt-0.5" />
            <div>
              <span className="text-yellow-800 font-medium">注意</span>
              <p className="text-yellow-700 mt-1">
                发现 {previewData.statistics.invalidCount} 个无效商品ID，
                这些商品将不会包含在批量任务中。请检查商品ID是否正确。
              </p>
            </div>
          </div>
        </Card>
      )}

      {previewData.statistics.validCount === 0 && (
        <Card className="p-4 bg-red-50 border-red-200">
          <div className="flex items-start space-x-2">
            <div className="w-4 h-4 bg-red-500 rounded-full flex-shrink-0 mt-0.5" />
            <div>
              <span className="text-red-800 font-medium">错误</span>
              <p className="text-red-700 mt-1">
                没有找到有效的商品ID，无法创建批量任务。请检查文件格式和商品ID是否正确。
              </p>
            </div>
          </div>
        </Card>
      )}
    </div>
  )
}