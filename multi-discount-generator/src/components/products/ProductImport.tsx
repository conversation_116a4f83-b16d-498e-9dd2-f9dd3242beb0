'use client'

import { useState, useCallback } from 'react'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { FileUpload } from '@/components/batch/FileUpload'
import { Download, Upload, CheckCircle, XCircle, AlertCircle } from 'lucide-react'
import type { ProductImportResult } from '@/types/product'

interface ProductImportProps {
  onComplete: () => void
  onCancel: () => void
}

export function ProductImport({ onComplete, onCancel }: ProductImportProps) {
  const [step, setStep] = useState<'upload' | 'preview' | 'importing' | 'result'>('upload')
  const [uploadResult, setUploadResult] = useState<any>(null)
  const [importResult, setImportResult] = useState<ProductImportResult | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // 下载模板
  const downloadTemplate = () => {
    // 创建模板数据
    const templateData = [
      [
        '商品ID*',
        '商品标题*',
        '商品定价*',
        '限价价格',
        '进货折扣',
        '活动形式',
        '活动开始时间',
        '活动结束时间',
        '供应商',
        'SKU ID*',
        'SKU名称',
        'SKU价格'
      ],
      [
        'PROD001',
        '示例商品',
        '99',
        '89',
        '8',
        '多件多折',
        '2024-01-01 00:00:00',
        '2024-12-31 23:59:59',
        '示例供应商',
        'SKU001',
        '规格1',
        '99'
      ]
    ]

    // 创建CSV内容
    const csvContent = templateData.map(row => row.join(',')).join('\n')
    const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    
    link.setAttribute('href', url)
    link.setAttribute('download', '商品导入模板.csv')
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  // 处理文件上传成功
  const handleUploadSuccess = useCallback(async (result: any) => {
    try {
      setLoading(true)
      setError(null)
      setUploadResult(result)

      // 预览导入数据
      const response = await fetch(`/api/products/import/preview?filename=${result.filename}`)
      const previewResult = await response.json()

      if (previewResult.success) {
        setStep('preview')
      } else {
        throw new Error(previewResult.error || '预览导入数据失败')
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : '处理文件失败')
    } finally {
      setLoading(false)
    }
  }, [])

  // 处理上传错误
  const handleUploadError = useCallback((error: string) => {
    setError(error)
    setStep('upload')
  }, [])

  // 确认导入
  const handleConfirmImport = async () => {
    if (!uploadResult) return

    try {
      setLoading(true)
      setStep('importing')

      const response = await fetch('/api/products/import', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ filename: uploadResult.filename })
      })

      const result = await response.json()

      if (result.success) {
        setImportResult(result.data)
        setStep('result')
      } else {
        throw new Error(result.error || '导入失败')
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : '导入失败')
      setStep('preview')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      {/* 步骤指示器 */}
      <div className="flex items-center justify-center space-x-4">
        {['upload', 'preview', 'importing', 'result'].map((stepName, index) => {
          const stepLabels = ['上传文件', '预览数据', '导入中', '导入结果']
          const isActive = step === stepName
          const isCompleted = ['upload', 'preview', 'importing', 'result'].indexOf(step) > index
          
          return (
            <div key={stepName} className="flex items-center">
              <div className={`
                w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium
                ${isActive ? 'bg-blue-600 text-white' : 
                  isCompleted ? 'bg-green-600 text-white' : 'bg-gray-200 text-gray-600'}
              `}>
                {isCompleted ? <CheckCircle className="w-4 h-4" /> : index + 1}
              </div>
              <span className={`ml-2 text-sm ${isActive ? 'text-blue-600 font-medium' : 'text-gray-500'}`}>
                {stepLabels[index]}
              </span>
              {index < stepLabels.length - 1 && (
                <div className={`w-8 h-0.5 mx-4 ${isCompleted ? 'bg-green-600' : 'bg-gray-200'}`} />
              )}
            </div>
          )
        })}
      </div>

      {error && (
        <Card className="p-4 bg-red-50 border-red-200">
          <div className="flex items-center space-x-2">
            <XCircle className="w-5 h-5 text-red-500" />
            <span className="text-red-800 font-medium">错误</span>
          </div>
          <p className="text-red-700 mt-1">{error}</p>
          <Button
            variant="outline"
            size="sm"
            className="mt-2"
            onClick={() => setError(null)}
          >
            关闭
          </Button>
        </Card>
      )}

      {/* 上传步骤 */}
      {step === 'upload' && (
        <div className="space-y-4">
          <Card className="p-6">
            <div className="text-center mb-6">
              <h3 className="text-lg font-medium text-gray-900 mb-2">上传商品数据文件</h3>
              <p className="text-gray-600">支持 Excel (.xlsx, .xls) 和 CSV 格式</p>
            </div>

            <FileUpload
              onSuccess={handleUploadSuccess}
              onError={handleUploadError}
              loading={loading}
              accept=".xlsx,.xls,.csv"
            />
          </Card>

          <Card className="p-4 bg-blue-50 border-blue-200">
            <div className="flex items-start space-x-3">
              <AlertCircle className="w-5 h-5 text-blue-500 mt-0.5" />
              <div>
                <h4 className="font-medium text-blue-900">导入说明</h4>
                <ul className="text-sm text-blue-800 mt-1 space-y-1">
                  <li>• 请先下载模板文件，按照模板格式填写数据</li>
                  <li>• 商品ID、商品标题、商品定价、SKU ID 为必填字段</li>
                  <li>• 活动形式默认为"多件多折"，可选：打折、直降、立减</li>
                  <li>• 折扣请填写整数（如：8 表示8折）</li>
                  <li>• 供应商直接填写供应商名称</li>
                  <li>• 时间格式：YYYY-MM-DD HH:mm:ss</li>
                  <li>• 一个商品可以有多个SKU，每行一个SKU</li>
                </ul>
                <Button
                  variant="outline"
                  size="sm"
                  className="mt-3"
                  onClick={downloadTemplate}
                >
                  <Download className="w-4 h-4 mr-2" />
                  下载导入模板
                </Button>
              </div>
            </div>
          </Card>
        </div>
      )}

      {/* 预览步骤 */}
      {step === 'preview' && (
        <Card className="p-6">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-medium text-gray-900">预览导入数据</h3>
            <div className="flex space-x-3">
              <Button
                variant="outline"
                onClick={() => setStep('upload')}
              >
                重新上传
              </Button>
              <Button
                onClick={handleConfirmImport}
                disabled={loading}
              >
                <Upload className="w-4 h-4 mr-2" />
                确认导入
              </Button>
            </div>
          </div>
          
          <div className="text-center py-8 text-gray-500">
            预览功能开发中...
          </div>
        </Card>
      )}

      {/* 导入中步骤 */}
      {step === 'importing' && (
        <Card className="p-8">
          <div className="text-center space-y-4">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto" />
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">正在导入商品数据</h3>
              <p className="text-gray-600">请稍候，正在处理您的数据...</p>
            </div>
          </div>
        </Card>
      )}

      {/* 结果步骤 */}
      {step === 'result' && importResult && (
        <Card className="p-6">
          <div className="text-center mb-6">
            <CheckCircle className="w-12 h-12 text-green-500 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">导入完成</h3>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">{importResult.imported}</div>
              <div className="text-sm text-green-800">成功导入</div>
            </div>
            <div className="text-center p-4 bg-red-50 rounded-lg">
              <div className="text-2xl font-bold text-red-600">{importResult.failed}</div>
              <div className="text-sm text-red-800">导入失败</div>
            </div>
            <div className="text-center p-4 bg-yellow-50 rounded-lg">
              <div className="text-2xl font-bold text-yellow-600">{importResult.duplicates.length}</div>
              <div className="text-sm text-yellow-800">重复跳过</div>
            </div>
          </div>

          {importResult.errors.length > 0 && (
            <div className="mb-6">
              <h4 className="font-medium text-gray-900 mb-2">错误详情</h4>
              <div className="bg-red-50 border border-red-200 rounded-md p-3">
                <ul className="text-sm text-red-800 space-y-1">
                  {importResult.errors.slice(0, 10).map((error, index) => (
                    <li key={index}>• {error}</li>
                  ))}
                  {importResult.errors.length > 10 && (
                    <li>... 还有 {importResult.errors.length - 10} 个错误</li>
                  )}
                </ul>
              </div>
            </div>
          )}

          <div className="flex justify-center">
            <Button onClick={onComplete}>
              返回商品列表
            </Button>
          </div>
        </Card>
      )}

      {/* 底部操作 */}
      {step === 'upload' && (
        <div className="flex justify-end">
          <Button variant="outline" onClick={onCancel}>
            取消
          </Button>
        </div>
      )}
    </div>
  )
}
