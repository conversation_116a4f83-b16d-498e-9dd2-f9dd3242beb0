'use client'

import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { Badge } from '@/components/ui/Badge'
import { Edit, Trash2, Eye, Package } from 'lucide-react'
import type { ProductInfo, ProductSearchParams, ActivityType } from '@/types/product'

interface ProductListProps {
  products: ProductInfo[]
  loading: boolean
  total: number
  searchParams: ProductSearchParams
  onSearchParamsChange: (params: ProductSearchParams) => void
  onEdit: (product: ProductInfo) => void
  onDelete: (productId: string) => void
}

export function ProductList({
  products,
  loading,
  total,
  searchParams,
  onSearchParamsChange,
  onEdit,
  onDelete
}: ProductListProps) {
  
  // 获取活动类型标签
  const getActivityTypeLabel = (type: ActivityType) => {
    const labels = {
      DISCOUNT: '打折',
      DIRECT_REDUCTION: '直降',
      INSTANT_REDUCTION: '立减'
    }
    return labels[type] || type
  }

  // 获取活动类型颜色
  const getActivityTypeColor = (type: ActivityType) => {
    const colors = {
      DISCOUNT: 'blue',
      DIRECT_REDUCTION: 'green',
      INSTANT_REDUCTION: 'purple'
    }
    return colors[type] || 'gray'
  }

  // 处理分页
  const handlePageChange = (page: number) => {
    onSearchParamsChange({ ...searchParams, page })
  }

  // 计算分页信息
  const pageSize = searchParams.pageSize || 20
  const currentPage = searchParams.page || 1
  const totalPages = Math.ceil(total / pageSize)
  const startIndex = (currentPage - 1) * pageSize + 1
  const endIndex = Math.min(currentPage * pageSize, total)

  if (loading) {
    return (
      <Card className="p-8">
        <div className="flex justify-center items-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-3 text-gray-600">加载中...</span>
        </div>
      </Card>
    )
  }

  if (products.length === 0) {
    return (
      <Card className="p-8">
        <div className="text-center">
          <Package className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">暂无商品</h3>
          <p className="mt-1 text-sm text-gray-500">
            {searchParams.search ? '没有找到匹配的商品' : '开始添加您的第一个商品'}
          </p>
        </div>
      </Card>
    )
  }

  return (
    <div className="space-y-4">
      {/* 商品列表 */}
      <Card>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  商品信息
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  价格信息
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  活动信息
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  SKU数量
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  供应商
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  创建时间
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  操作
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {products.map((product) => (
                <tr key={product.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900">
                        {product.title}
                      </div>
                      <div className="text-sm text-gray-500">
                        ID: {product.productId}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">
                      定价: ¥{product.basePrice}
                    </div>
                    {product.limitPrice && (
                      <div className="text-sm text-gray-500">
                        限价: ¥{product.limitPrice}
                      </div>
                    )}
                    {product.purchaseDiscount && (
                      <div className="text-sm text-gray-500">
                        进货折扣: {(product.purchaseDiscount * 100).toFixed(1)}%
                      </div>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <Badge color={getActivityTypeColor(product.activityType)}>
                      {getActivityTypeLabel(product.activityType)}
                    </Badge>
                    {product.activityStartTime && product.activityEndTime && (
                      <div className="text-xs text-gray-500 mt-1">
                        {new Date(product.activityStartTime).toLocaleDateString()} - 
                        {new Date(product.activityEndTime).toLocaleDateString()}
                      </div>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {product.skus?.length || 0} 个SKU
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {product.supplier?.name || '-'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {new Date(product.createdAt).toLocaleDateString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex justify-end space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => onEdit(product)}
                      >
                        <Edit className="w-4 h-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => onDelete(product.id)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </Card>

      {/* 分页 */}
      {totalPages > 1 && (
        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-700">
              显示 {startIndex} 到 {endIndex} 条，共 {total} 条记录
            </div>
            
            <div className="flex space-x-2">
              <Button
                variant="outline"
                size="sm"
                disabled={currentPage <= 1}
                onClick={() => handlePageChange(currentPage - 1)}
              >
                上一页
              </Button>
              
              {/* 页码 */}
              {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                const page = Math.max(1, currentPage - 2) + i
                if (page > totalPages) return null
                
                return (
                  <Button
                    key={page}
                    variant={page === currentPage ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => handlePageChange(page)}
                  >
                    {page}
                  </Button>
                )
              })}
              
              <Button
                variant="outline"
                size="sm"
                disabled={currentPage >= totalPages}
                onClick={() => handlePageChange(currentPage + 1)}
              >
                下一页
              </Button>
            </div>
          </div>
        </Card>
      )}
    </div>
  )
}
