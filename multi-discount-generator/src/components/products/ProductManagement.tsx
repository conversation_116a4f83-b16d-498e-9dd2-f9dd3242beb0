'use client'

import { useState, useEffect } from 'react'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { Plus, Search, Filter, Download, Upload } from 'lucide-react'
import { ProductForm } from './ProductForm'
import { ProductList } from './ProductList'
import { ProductImport } from './ProductImport'
import type { ProductInfo, ProductFormData, ProductSearchParams } from '@/types/product'

type View = 'list' | 'create' | 'edit' | 'import'

export function ProductManagement() {
  const [currentView, setCurrentView] = useState<View>('list')
  const [products, setProducts] = useState<ProductInfo[]>([])
  const [loading, setLoading] = useState(false)
  const [searchParams, setSearchParams] = useState<ProductSearchParams>({
    page: 1,
    pageSize: 20,
    search: ''
  })
  const [editingProduct, setEditingProduct] = useState<ProductInfo | null>(null)
  const [total, setTotal] = useState(0)

  // 加载商品列表
  const loadProducts = async () => {
    try {
      setLoading(true)
      const queryParams = new URLSearchParams()
      
      Object.entries(searchParams).forEach(([key, value]) => {
        if (value !== undefined && value !== '') {
          queryParams.append(key, value.toString())
        }
      })

      const response = await fetch(`/api/products?${queryParams}`)
      const result = await response.json()

      if (result.success) {
        setProducts(result.data.products)
        setTotal(result.data.total)
      } else {
        console.error('加载商品列表失败:', result.error)
      }
    } catch (error) {
      console.error('加载商品列表失败:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadProducts()
  }, [searchParams])

  // 处理搜索
  const handleSearch = (search: string) => {
    setSearchParams(prev => ({ ...prev, search, page: 1 }))
  }

  // 处理创建商品
  const handleCreateProduct = async (data: ProductFormData) => {
    try {
      const response = await fetch('/api/products/create', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      })

      const result = await response.json()

      if (result.success) {
        setCurrentView('list')
        loadProducts()
      } else {
        throw new Error(result.error)
      }
    } catch (error) {
      console.error('创建商品失败:', error)
      throw error
    }
  }

  // 处理编辑商品
  const handleEditProduct = (product: ProductInfo) => {
    setEditingProduct(product)
    setCurrentView('edit')
  }

  // 处理更新商品
  const handleUpdateProduct = async (data: ProductFormData) => {
    if (!editingProduct) return

    try {
      const response = await fetch(`/api/products/${editingProduct.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      })

      const result = await response.json()

      if (result.success) {
        setCurrentView('list')
        setEditingProduct(null)
        loadProducts()
      } else {
        throw new Error(result.error)
      }
    } catch (error) {
      console.error('更新商品失败:', error)
      throw error
    }
  }

  // 处理删除商品
  const handleDeleteProduct = async (productId: string) => {
    if (!confirm('确定要删除这个商品吗？')) return

    try {
      const response = await fetch(`/api/products/${productId}`, {
        method: 'DELETE'
      })

      const result = await response.json()

      if (result.success) {
        loadProducts()
      } else {
        throw new Error(result.error)
      }
    } catch (error) {
      console.error('删除商品失败:', error)
      alert('删除商品失败: ' + error.message)
    }
  }

  // 处理导入完成
  const handleImportComplete = () => {
    setCurrentView('list')
    loadProducts()
  }

  // 渲染头部操作栏
  const renderHeader = () => (
    <div className="mb-6">
      <div className="flex justify-between items-center mb-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">商品管理</h1>
          <p className="text-gray-600">管理商品信息、SKU和供应商关系</p>
        </div>
        
        <div className="flex space-x-3">
          <Button
            variant="outline"
            onClick={() => setCurrentView('import')}
          >
            <Upload className="w-4 h-4 mr-2" />
            批量导入
          </Button>
          <Button
            onClick={() => setCurrentView('create')}
          >
            <Plus className="w-4 h-4 mr-2" />
            新增商品
          </Button>
        </div>
      </div>

      {currentView === 'list' && (
        <Card className="p-4">
          <div className="flex space-x-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="搜索商品ID、标题或供应商..."
                  value={searchParams.search || ''}
                  onChange={(e) => handleSearch(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Button variant="outline">
              <Filter className="w-4 h-4 mr-2" />
              筛选
            </Button>
            <Button variant="outline">
              <Download className="w-4 h-4 mr-2" />
              导出
            </Button>
          </div>
        </Card>
      )}
    </div>
  )

  return (
    <div className="space-y-6">
      {renderHeader()}

      {currentView === 'list' && (
        <ProductList
          products={products}
          loading={loading}
          total={total}
          searchParams={searchParams}
          onSearchParamsChange={setSearchParams}
          onEdit={handleEditProduct}
          onDelete={handleDeleteProduct}
        />
      )}

      {currentView === 'create' && (
        <Card className="p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-6">新增商品</h2>
          <ProductForm
            onSubmit={handleCreateProduct}
            onCancel={() => setCurrentView('list')}
          />
        </Card>
      )}

      {currentView === 'edit' && editingProduct && (
        <Card className="p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-6">编辑商品</h2>
          <ProductForm
            initialData={{
              productId: editingProduct.productId,
              title: editingProduct.title,
              basePrice: editingProduct.basePrice,
              limitPrice: editingProduct.limitPrice,
              purchaseDiscount: editingProduct.purchaseDiscount,
              activityType: editingProduct.activityType,
              activityStartTime: editingProduct.activityStartTime,
              activityEndTime: editingProduct.activityEndTime,
              supplierId: editingProduct.supplierId,
              skus: editingProduct.skus || []
            }}
            onSubmit={handleUpdateProduct}
            onCancel={() => {
              setCurrentView('list')
              setEditingProduct(null)
            }}
          />
        </Card>
      )}

      {currentView === 'import' && (
        <Card className="p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-6">批量导入商品</h2>
          <ProductImport
            onComplete={handleImportComplete}
            onCancel={() => setCurrentView('list')}
          />
        </Card>
      )}
    </div>
  )
}
