'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { useTaskStatus } from '@/hooks/useSocket'
import type { TaskWithRelations } from '@/types/batch'
import { TaskStatus, ProductStatus, LogLevel } from '@prisma/client'

interface TaskDetailProps {
  taskId: string
}

export function TaskDetail({ taskId }: TaskDetailProps) {
  const router = useRouter()
  const [task, setTask] = useState<TaskWithRelations | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState<'overview' | 'products' | 'logs'>('overview')
  const [actionLoading, setActionLoading] = useState<string | null>(null)

  // 使用WebSocket监听任务状态
  const { taskStatus, progress } = useTaskStatus(taskId)

  // 加载任务详情
  const loadTask = async () => {
    setLoading(true)
    setError(null)

    try {
      const response = await fetch(`/api/tasks/${taskId}`)
      const result = await response.json()

      if (result.success) {
        setTask(result.data.task)
      } else {
        throw new Error(result.error || '加载任务详情失败')
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : '加载任务详情失败')
    } finally {
      setLoading(false)
    }
  }

  // 初始加载
  useEffect(() => {
    loadTask()
  }, [taskId])

  // 更新任务状态
  useEffect(() => {
    if (taskStatus?.task) {
      setTask(prev => prev ? { ...prev, ...taskStatus.task } : null)
    }
  }, [taskStatus])

  // 执行任务操作
  const handleTaskAction = async (action: 'cancel' | 'retry') => {
    if (!task) return

    setActionLoading(action)

    try {
      const response = await fetch(`/api/tasks/${taskId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ action })
      })

      const result = await response.json()

      if (result.success) {
        if (action === 'retry' && result.data.newTaskId) {
          router.push(`/tasks/${result.data.newTaskId}`)
        } else {
          await loadTask()
        }
      } else {
        throw new Error(result.error || `${action === 'cancel' ? '取消' : '重试'}任务失败`)
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : '操作失败')
    } finally {
      setActionLoading(null)
    }
  }

  // 获取状态样式
  const getStatusStyle = (status: TaskStatus) => {
    switch (status) {
      case TaskStatus.PENDING:
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case TaskStatus.RUNNING:
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case TaskStatus.COMPLETED:
        return 'bg-green-100 text-green-800 border-green-200'
      case TaskStatus.FAILED:
        return 'bg-red-100 text-red-800 border-red-200'
      case TaskStatus.CANCELLED:
        return 'bg-gray-100 text-gray-800 border-gray-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  // 获取状态文本
  const getStatusText = (status: TaskStatus) => {
    switch (status) {
      case TaskStatus.PENDING:
        return '等待中'
      case TaskStatus.RUNNING:
        return '执行中'
      case TaskStatus.COMPLETED:
        return '已完成'
      case TaskStatus.FAILED:
        return '失败'
      case TaskStatus.CANCELLED:
        return '已取消'
      default:
        return '未知'
    }
  }

  // 格式化时间
  const formatTime = (date: Date | null) => {
    if (!date) return '-'
    return new Date(date).toLocaleString('zh-CN')
  }

  // 计算执行时长
  const getDuration = () => {
    if (!task?.startedAt) return '-'
    const endTime = task.completedAt || new Date()
    const duration = endTime.getTime() - new Date(task.startedAt).getTime()
    const minutes = Math.floor(duration / 60000)
    const seconds = Math.floor((duration % 60000) / 1000)
    return `${minutes}分${seconds}秒`
  }

  // 渲染概览标签页
  const renderOverview = () => {
    if (!task) return null

    const currentProgress = progress || {
      taskId,
      totalItems: task.totalItems,
      processedItems: task.processedItems,
      failedItems: task.failedItems,
      progress: task.totalItems > 0 ? Math.round((task.processedItems / task.totalItems) * 100) : 0
    }

    return (
      <div className="space-y-6">
        {/* 基本信息 */}
        <Card className="p-6">
          <h3 className="text-lg font-medium mb-4">基本信息</h3>
          <div className="grid md:grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium text-gray-600">任务名称</label>
              <p className="text-gray-900">{task.name}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-600">任务类型</label>
              <p className="text-gray-900">{task.type === 'BATCH' ? '批量处理' : '单个处理'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-600">状态</label>
              <div className={`inline-block px-2 py-1 rounded-full text-xs font-medium border ${getStatusStyle(task.status)}`}>
                {getStatusText(task.status)}
              </div>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-600">优先级</label>
              <p className="text-gray-900">{task.priority}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-600">创建时间</label>
              <p className="text-gray-900">{formatTime(task.createdAt)}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-600">开始时间</label>
              <p className="text-gray-900">{formatTime(task.startedAt)}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-600">完成时间</label>
              <p className="text-gray-900">{formatTime(task.completedAt)}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-600">执行时长</label>
              <p className="text-gray-900">{getDuration()}</p>
            </div>
          </div>
        </Card>

        {/* 进度信息 */}
        <Card className="p-6">
          <h3 className="text-lg font-medium mb-4">执行进度</h3>
          
          {task.status === TaskStatus.RUNNING && (
            <div className="mb-4">
              <div className="flex justify-between text-sm text-gray-600 mb-2">
                <span>整体进度</span>
                <span>{currentProgress.processedItems}/{currentProgress.totalItems} ({currentProgress.progress}%)</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-3">
                <div 
                  className="bg-blue-600 h-3 rounded-full transition-all duration-300"
                  style={{ width: `${currentProgress.progress}%` }}
                />
              </div>
              {progress?.estimatedTimeRemaining && (
                <p className="text-sm text-gray-600 mt-2">
                  预计剩余时间: {Math.round(progress.estimatedTimeRemaining / 60000)}分钟
                </p>
              )}
              {progress?.currentProduct && (
                <p className="text-sm text-gray-600">
                  当前处理: {progress.currentProduct}
                </p>
              )}
            </div>
          )}

          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">{currentProgress.totalItems}</div>
              <div className="text-sm text-gray-600">总数量</div>
            </div>
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">{currentProgress.processedItems}</div>
              <div className="text-sm text-gray-600">已处理</div>
            </div>
            <div className="text-center p-4 bg-red-50 rounded-lg">
              <div className="text-2xl font-bold text-red-600">{currentProgress.failedItems}</div>
              <div className="text-sm text-gray-600">失败</div>
            </div>
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <div className="text-2xl font-bold text-gray-600">{task._count?.logs || 0}</div>
              <div className="text-sm text-gray-600">日志条数</div>
            </div>
          </div>
        </Card>

        {/* 文件信息 */}
        <Card className="p-6">
          <h3 className="text-lg font-medium mb-4">文件信息</h3>
          <div className="grid md:grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium text-gray-600">输入文件</label>
              <p className="text-gray-900">{task.inputFile || '-'}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-600">输出文件</label>
              <div className="flex items-center space-x-2">
                <p className="text-gray-900">{task.outputFile || '-'}</p>
                {task.status === TaskStatus.COMPLETED && task.outputFile && (
                  <Button
                    size="sm"
                    onClick={() => window.open(`/api/download/tasks/${taskId}`, '_blank')}
                  >
                    下载
                  </Button>
                )}
              </div>
            </div>
          </div>
        </Card>
      </div>
    )
  }

  // 渲染商品标签页
  const renderProducts = () => {
    if (!task?.products) return null

    return (
      <Card className="p-6">
        <h3 className="text-lg font-medium mb-4">商品列表</h3>
        <div className="overflow-x-auto">
          <table className="w-full text-sm">
            <thead>
              <tr className="border-b">
                <th className="text-left py-2">商品ID</th>
                <th className="text-left py-2">状态</th>
                <th className="text-left py-2">错误信息</th>
                <th className="text-left py-2">更新时间</th>
              </tr>
            </thead>
            <tbody>
              {task.products.map((product) => (
                <tr key={product.id} className="border-b">
                  <td className="py-2 font-mono">{product.productId}</td>
                  <td className="py-2">
                    <span className={`px-2 py-1 rounded text-xs ${
                      product.status === ProductStatus.COMPLETED ? 'bg-green-100 text-green-800' :
                      product.status === ProductStatus.PROCESSING ? 'bg-blue-100 text-blue-800' :
                      product.status === ProductStatus.FAILED ? 'bg-red-100 text-red-800' :
                      'bg-gray-100 text-gray-800'
                    }`}>
                      {product.status === ProductStatus.COMPLETED ? '已完成' :
                       product.status === ProductStatus.PROCESSING ? '处理中' :
                       product.status === ProductStatus.FAILED ? '失败' : '等待中'}
                    </span>
                  </td>
                  <td className="py-2 text-red-600">{product.errorMessage || '-'}</td>
                  <td className="py-2">{formatTime(product.updatedAt)}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </Card>
    )
  }

  // 渲染日志标签页
  const renderLogs = () => {
    if (!task?.logs) return null

    return (
      <Card className="p-6">
        <h3 className="text-lg font-medium mb-4">执行日志</h3>
        <div className="space-y-2 max-h-96 overflow-y-auto">
          {task.logs.map((log) => (
            <div 
              key={log.id} 
              className={`p-3 rounded text-sm ${
                log.level === LogLevel.ERROR ? 'bg-red-50 border-l-4 border-red-400' :
                log.level === LogLevel.WARN ? 'bg-yellow-50 border-l-4 border-yellow-400' :
                'bg-gray-50 border-l-4 border-gray-400'
              }`}
            >
              <div className="flex justify-between items-start mb-1">
                <span className={`font-medium ${
                  log.level === LogLevel.ERROR ? 'text-red-800' :
                  log.level === LogLevel.WARN ? 'text-yellow-800' :
                  'text-gray-800'
                }`}>
                  {log.message}
                </span>
                <span className="text-xs text-gray-500">
                  {formatTime(log.createdAt)}
                </span>
              </div>
              {log.details && (
                <pre className="text-xs text-gray-600 mt-1 whitespace-pre-wrap">
                  {JSON.stringify(JSON.parse(log.details), null, 2)}
                </pre>
              )}
            </div>
          ))}
        </div>
      </Card>
    )
  }

  if (loading) {
    return (
      <div className="flex justify-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600" />
        <span className="ml-2 text-gray-600">加载中...</span>
      </div>
    )
  }

  if (error) {
    return (
      <Card className="p-8 text-center">
        <div className="text-red-600 mb-4">加载失败</div>
        <p className="text-gray-600 mb-4">{error}</p>
        <Button onClick={loadTask}>重试</Button>
      </Card>
    )
  }

  if (!task) {
    return (
      <Card className="p-8 text-center">
        <div className="text-gray-600">任务不存在</div>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* 头部操作 */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">{task.name}</h1>
          <p className="text-gray-600">任务ID: {task.id}</p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" onClick={() => router.back()}>
            返回
          </Button>
          {task.status === TaskStatus.RUNNING && (
            <Button
              variant="outline"
              onClick={() => handleTaskAction('cancel')}
              disabled={actionLoading === 'cancel'}
            >
              {actionLoading === 'cancel' ? '取消中...' : '取消任务'}
            </Button>
          )}
          {task.status === TaskStatus.FAILED && (
            <Button
              onClick={() => handleTaskAction('retry')}
              disabled={actionLoading === 'retry'}
            >
              {actionLoading === 'retry' ? '重试中...' : '重试任务'}
            </Button>
          )}
          {task.status === TaskStatus.COMPLETED && task.outputFile && (
            <Button
              onClick={() => window.open(`/api/download/tasks/${taskId}`, '_blank')}
            >
              下载结果
            </Button>
          )}
        </div>
      </div>

      {/* 标签页 */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {[
            { key: 'overview', label: '概览' },
            { key: 'products', label: `商品 (${task.products?.length || 0})` },
            { key: 'logs', label: `日志 (${task.logs?.length || 0})` }
          ].map((tab) => (
            <button
              key={tab.key}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === tab.key
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
              onClick={() => setActiveTab(tab.key as any)}
            >
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      {/* 标签页内容 */}
      {activeTab === 'overview' && renderOverview()}
      {activeTab === 'products' && renderProducts()}
      {activeTab === 'logs' && renderLogs()}
    </div>
  )
}