'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import type { TaskWithRelations } from '@/types/batch'
import { TaskStatus } from '@prisma/client'

interface TaskListProps {
  initialTasks?: TaskWithRelations[]
  showFilters?: boolean
}

export function TaskList({ initialTasks = [], showFilters = true }: TaskListProps) {
  const router = useRouter()
  const [tasks, setTasks] = useState<TaskWithRelations[]>(initialTasks)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [filters, setFilters] = useState({
    status: '',
    search: '',
    page: 1,
    limit: 20
  })
  const [pagination, setPagination] = useState({
    total: 0,
    pages: 0,
    page: 1,
    limit: 20
  })

  // 加载任务列表
  const loadTasks = async (newFilters = filters) => {
    setLoading(true)
    setError(null)

    try {
      const params = new URLSearchParams()
      if (newFilters.status) params.append('status', newFilters.status)
      if (newFilters.search) params.append('search', newFilters.search)
      params.append('page', newFilters.page.toString())
      params.append('limit', newFilters.limit.toString())

      const response = await fetch(`/api/tasks/list?${params}`)
      const result = await response.json()

      if (result.success) {
        setTasks(result.data.tasks)
        setPagination(result.data.pagination)
      } else {
        throw new Error(result.error || '加载任务列表失败')
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : '加载任务列表失败')
    } finally {
      setLoading(false)
    }
  }

  // 初始加载
  useEffect(() => {
    if (initialTasks.length === 0) {
      loadTasks()
    }
  }, [])

  // 处理筛选变化
  const handleFilterChange = (newFilters: Partial<typeof filters>) => {
    const updatedFilters = { ...filters, ...newFilters, page: 1 }
    setFilters(updatedFilters)
    loadTasks(updatedFilters)
  }

  // 处理分页
  const handlePageChange = (page: number) => {
    const updatedFilters = { ...filters, page }
    setFilters(updatedFilters)
    loadTasks(updatedFilters)
  }

  // 获取状态样式
  const getStatusStyle = (status: TaskStatus) => {
    switch (status) {
      case TaskStatus.PENDING:
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case TaskStatus.RUNNING:
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case TaskStatus.COMPLETED:
        return 'bg-green-100 text-green-800 border-green-200'
      case TaskStatus.FAILED:
        return 'bg-red-100 text-red-800 border-red-200'
      case TaskStatus.CANCELLED:
        return 'bg-gray-100 text-gray-800 border-gray-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  // 获取状态文本
  const getStatusText = (status: TaskStatus) => {
    switch (status) {
      case TaskStatus.PENDING:
        return '等待中'
      case TaskStatus.RUNNING:
        return '执行中'
      case TaskStatus.COMPLETED:
        return '已完成'
      case TaskStatus.FAILED:
        return '失败'
      case TaskStatus.CANCELLED:
        return '已取消'
      default:
        return '未知'
    }
  }

  // 格式化时间
  const formatTime = (date: Date | null) => {
    if (!date) return '-'
    return new Date(date).toLocaleString('zh-CN')
  }

  // 计算进度
  const getProgress = (task: TaskWithRelations) => {
    if (task.totalItems === 0) return 0
    return Math.round((task.processedItems / task.totalItems) * 100)
  }

  // 渲染任务卡片
  const renderTaskCard = (task: TaskWithRelations) => (
    <Card key={task.id} className="p-4 hover:shadow-md transition-shadow">
      <div className="flex items-start justify-between mb-3">
        <div className="flex-1">
          <h3 className="font-medium text-gray-900 mb-1">{task.name}</h3>
          <div className="flex items-center space-x-4 text-sm text-gray-600">
            <span>类型: {task.type === 'BATCH' ? '批量' : '单个'}</span>
            <span>商品: {task.totalItems}</span>
            <span>创建: {formatTime(task.createdAt)}</span>
          </div>
        </div>
        <div className={`px-2 py-1 rounded-full text-xs font-medium border ${getStatusStyle(task.status)}`}>
          {getStatusText(task.status)}
        </div>
      </div>

      {/* 进度条 */}
      {task.status === TaskStatus.RUNNING && (
        <div className="mb-3">
          <div className="flex justify-between text-sm text-gray-600 mb-1">
            <span>进度</span>
            <span>{task.processedItems}/{task.totalItems} ({getProgress(task)}%)</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${getProgress(task)}%` }}
            />
          </div>
        </div>
      )}

      {/* 统计信息 */}
      <div className="grid grid-cols-3 gap-4 mb-3 text-sm">
        <div className="text-center">
          <div className="font-medium text-gray-900">{task.processedItems}</div>
          <div className="text-gray-600">已处理</div>
        </div>
        <div className="text-center">
          <div className="font-medium text-red-600">{task.failedItems}</div>
          <div className="text-gray-600">失败</div>
        </div>
        <div className="text-center">
          <div className="font-medium text-gray-900">{task._count?.logs || 0}</div>
          <div className="text-gray-600">日志</div>
        </div>
      </div>

      {/* 操作按钮 */}
      <div className="flex justify-between items-center pt-3 border-t">
        <div className="text-xs text-gray-500">
          {task.startedAt && `开始: ${formatTime(task.startedAt)}`}
          {task.completedAt && ` | 完成: ${formatTime(task.completedAt)}`}
        </div>
        <div className="flex space-x-2">
          <Button
            size="sm"
            variant="outline"
            onClick={() => router.push(`/tasks/${task.id}`)}
          >
            查看详情
          </Button>
          {task.status === TaskStatus.COMPLETED && task.outputFile && (
            <Button
              size="sm"
              onClick={() => window.open(`/api/download/tasks/${task.id}`, '_blank')}
            >
              下载
            </Button>
          )}
        </div>
      </div>
    </Card>
  )

  return (
    <div className="space-y-6">
      {/* 筛选器 */}
      {showFilters && (
        <Card className="p-4">
          <div className="flex flex-wrap gap-4">
            <div className="flex-1 min-w-64">
              <Input
                placeholder="搜索任务名称或文件名..."
                value={filters.search}
                onChange={(e) => handleFilterChange({ search: e.target.value })}
              />
            </div>
            <select
              className="px-3 py-2 border border-gray-300 rounded-md text-sm"
              value={filters.status}
              onChange={(e) => handleFilterChange({ status: e.target.value })}
            >
              <option value="">所有状态</option>
              <option value={TaskStatus.PENDING}>等待中</option>
              <option value={TaskStatus.RUNNING}>执行中</option>
              <option value={TaskStatus.COMPLETED}>已完成</option>
              <option value={TaskStatus.FAILED}>失败</option>
              <option value={TaskStatus.CANCELLED}>已取消</option>
            </select>
            <Button
              variant="outline"
              onClick={() => loadTasks()}
              disabled={loading}
            >
              刷新
            </Button>
          </div>
        </Card>
      )}

      {/* 错误提示 */}
      {error && (
        <Card className="p-4 bg-red-50 border-red-200">
          <div className="flex items-center space-x-2">
            <div className="w-4 h-4 bg-red-500 rounded-full flex-shrink-0" />
            <span className="text-red-800 font-medium">加载失败</span>
          </div>
          <p className="text-red-700 mt-1">{error}</p>
          <Button
            variant="outline"
            size="sm"
            className="mt-2"
            onClick={() => loadTasks()}
          >
            重试
          </Button>
        </Card>
      )}

      {/* 加载状态 */}
      {loading && (
        <div className="flex justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600" />
          <span className="ml-2 text-gray-600">加载中...</span>
        </div>
      )}

      {/* 任务列表 */}
      {!loading && tasks.length > 0 && (
        <div className="grid gap-4">
          {tasks.map(renderTaskCard)}
        </div>
      )}

      {/* 空状态 */}
      {!loading && tasks.length === 0 && !error && (
        <Card className="p-8 text-center">
          <div className="text-gray-400 mb-4">
            <svg className="w-16 h-16 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">暂无任务</h3>
          <p className="text-gray-600 mb-4">还没有创建任何任务</p>
          <Button onClick={() => router.push('/batch')}>
            创建批量任务
          </Button>
        </Card>
      )}

      {/* 分页 */}
      {pagination.pages > 1 && (
        <div className="flex justify-center items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            disabled={pagination.page <= 1}
            onClick={() => handlePageChange(pagination.page - 1)}
          >
            上一页
          </Button>
          <span className="text-sm text-gray-600">
            第 {pagination.page} 页，共 {pagination.pages} 页
          </span>
          <Button
            variant="outline"
            size="sm"
            disabled={pagination.page >= pagination.pages}
            onClick={() => handlePageChange(pagination.page + 1)}
          >
            下一页
          </Button>
        </div>
      )}
    </div>
  )
}