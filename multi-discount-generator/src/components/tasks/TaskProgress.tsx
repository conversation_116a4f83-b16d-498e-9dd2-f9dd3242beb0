'use client'

import { useEffect, useState } from 'react'
import { Card } from '@/components/ui/Card'
import { useTaskStatus } from '@/hooks/useSocket'
import type { TaskProgress as TaskProgressType } from '@/types/batch'

interface TaskProgressProps {
  taskId: string
  showDetails?: boolean
  className?: string
}

export function TaskProgress({ 
  taskId, 
  showDetails = true, 
  className = '' 
}: TaskProgressProps) {
  const { progress } = useTaskStatus(taskId)
  const [animatedProgress, setAnimatedProgress] = useState(0)

  // 动画更新进度
  useEffect(() => {
    if (progress) {
      const timer = setTimeout(() => {
        setAnimatedProgress(progress.progress)
      }, 100)
      return () => clearTimeout(timer)
    }
  }, [progress])

  if (!progress) {
    return (
      <Card className={`p-4 ${className}`}>
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded mb-2"></div>
          <div className="h-2 bg-gray-200 rounded"></div>
        </div>
      </Card>
    )
  }

  const formatTime = (ms: number) => {
    const minutes = Math.floor(ms / 60000)
    const seconds = Math.floor((ms % 60000) / 1000)
    return `${minutes}分${seconds}秒`
  }

  return (
    <Card className={`p-4 ${className}`}>
      <div className="space-y-3">
        {/* 进度条 */}
        <div>
          <div className="flex justify-between text-sm text-gray-600 mb-2">
            <span>执行进度</span>
            <span>{progress.processedItems}/{progress.totalItems} ({progress.progress}%)</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-3">
            <div 
              className="bg-blue-600 h-3 rounded-full transition-all duration-500 ease-out relative overflow-hidden"
              style={{ width: `${animatedProgress}%` }}
            >
              {/* 进度条动画效果 */}
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-30 animate-pulse"></div>
            </div>
          </div>
        </div>

        {showDetails && (
          <>
            {/* 统计信息 */}
            <div className="grid grid-cols-3 gap-4 text-sm">
              <div className="text-center">
                <div className="font-semibold text-green-600">{progress.processedItems}</div>
                <div className="text-gray-600">已处理</div>
              </div>
              <div className="text-center">
                <div className="font-semibold text-red-600">{progress.failedItems}</div>
                <div className="text-gray-600">失败</div>
              </div>
              <div className="text-center">
                <div className="font-semibold text-gray-600">
                  {progress.totalItems - progress.processedItems}
                </div>
                <div className="text-gray-600">剩余</div>
              </div>
            </div>

            {/* 详细信息 */}
            <div className="space-y-2 text-sm text-gray-600">
              {progress.currentProduct && (
                <div className="flex justify-between">
                  <span>当前处理:</span>
                  <span className="font-mono text-gray-900">{progress.currentProduct}</span>
                </div>
              )}
              
              {progress.estimatedTimeRemaining && progress.estimatedTimeRemaining > 0 && (
                <div className="flex justify-between">
                  <span>预计剩余:</span>
                  <span className="text-blue-600 font-medium">
                    {formatTime(progress.estimatedTimeRemaining)}
                  </span>
                </div>
              )}
            </div>
          </>
        )}
      </div>
    </Card>
  )
}

// 简化版进度条组件
export function SimpleTaskProgress({ 
  taskId, 
  className = '' 
}: { 
  taskId: string
  className?: string 
}) {
  const { progress } = useTaskStatus(taskId)

  if (!progress) {
    return (
      <div className={`w-full bg-gray-200 rounded-full h-2 ${className}`}>
        <div className="bg-gray-300 h-2 rounded-full animate-pulse w-1/3"></div>
      </div>
    )
  }

  return (
    <div className={`w-full bg-gray-200 rounded-full h-2 ${className}`}>
      <div 
        className="bg-blue-600 h-2 rounded-full transition-all duration-300"
        style={{ width: `${progress.progress}%` }}
      />
    </div>
  )
}