'use client'

import { useState, useEffect } from 'react'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { Badge } from '@/components/ui/Badge'
import { 
  Package, 
  FileText, 
  Download, 
  Upload, 
  Users, 
  Activity,
  TrendingUp,
  Clock,
  AlertCircle,
  CheckCircle
} from 'lucide-react'
import Link from 'next/link'

interface DashboardStats {
  totalProducts: number
  totalTasks: number
  activeTasks: number
  completedTasks: number
  totalSuppliers: number
  todayTasks: number
}

export function AdminDashboard() {
  const [stats, setStats] = useState<DashboardStats>({
    totalProducts: 0,
    totalTasks: 0,
    activeTasks: 0,
    completedTasks: 0,
    totalSuppliers: 0,
    todayTasks: 0
  })
  const [loading, setLoading] = useState(true)

  // 加载统计数据
  useEffect(() => {
    loadDashboardStats()
  }, [])

  const loadDashboardStats = async () => {
    try {
      setLoading(true)
      // 这里应该调用实际的API
      // 暂时使用模拟数据
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      setStats({
        totalProducts: 156,
        totalTasks: 89,
        activeTasks: 12,
        completedTasks: 77,
        totalSuppliers: 23,
        todayTasks: 8
      })
    } catch (error) {
      console.error('加载统计数据失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const quickActions = [
    {
      title: '商品管理',
      description: '管理商品信息、SKU和供应商',
      icon: Package,
      href: '/products',
      color: 'blue'
    },
    {
      title: '模板管理',
      description: '管理Excel模板和批量配置',
      icon: FileText,
      href: '/templates',
      color: 'green'
    },
    {
      title: '批量导出',
      description: '批量导出活动时间表格',
      icon: Download,
      href: '/batch/export',
      color: 'purple'
    },
    {
      title: '批量导入',
      description: '批量导入商品和模板数据',
      icon: Upload,
      href: '/batch/import',
      color: 'orange'
    }
  ]

  const recentTasks = [
    { id: '1', name: '商品批量导入任务', status: 'completed', time: '2分钟前' },
    { id: '2', name: '活动时间生成任务', status: 'running', time: '5分钟前' },
    { id: '3', name: '模板解析任务', status: 'pending', time: '10分钟前' },
    { id: '4', name: '数据导出任务', status: 'completed', time: '15分钟前' }
  ]

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-500" />
      case 'running':
        return <Clock className="w-4 h-4 text-blue-500" />
      case 'pending':
        return <AlertCircle className="w-4 h-4 text-yellow-500" />
      default:
        return <AlertCircle className="w-4 h-4 text-gray-500" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'green'
      case 'running':
        return 'blue'
      case 'pending':
        return 'yellow'
      default:
        return 'gray'
    }
  }

  if (loading) {
    return (
      <div className="max-w-7xl mx-auto p-6">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-gray-200 rounded w-1/4"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-32 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="max-w-7xl mx-auto p-6 space-y-6">
      {/* 页面标题 */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">管理后台</h1>
          <p className="text-gray-600">商品管理、模板管理和批量操作</p>
        </div>
        <Button onClick={loadDashboardStats}>
          <Activity className="w-4 h-4 mr-2" />
          刷新数据
        </Button>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">商品总数</p>
              <p className="text-2xl font-bold text-gray-900">{stats.totalProducts}</p>
            </div>
            <Package className="w-8 h-8 text-blue-500" />
          </div>
          <div className="mt-4 flex items-center text-sm text-green-600">
            <TrendingUp className="w-4 h-4 mr-1" />
            <span>较上月增长 12%</span>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">任务总数</p>
              <p className="text-2xl font-bold text-gray-900">{stats.totalTasks}</p>
            </div>
            <FileText className="w-8 h-8 text-green-500" />
          </div>
          <div className="mt-4 flex items-center text-sm text-gray-600">
            <span>活跃: {stats.activeTasks} | 完成: {stats.completedTasks}</span>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">供应商数量</p>
              <p className="text-2xl font-bold text-gray-900">{stats.totalSuppliers}</p>
            </div>
            <Users className="w-8 h-8 text-purple-500" />
          </div>
          <div className="mt-4 flex items-center text-sm text-blue-600">
            <span>活跃供应商: {Math.floor(stats.totalSuppliers * 0.8)}</span>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">今日任务</p>
              <p className="text-2xl font-bold text-gray-900">{stats.todayTasks}</p>
            </div>
            <Clock className="w-8 h-8 text-orange-500" />
          </div>
          <div className="mt-4 flex items-center text-sm text-green-600">
            <CheckCircle className="w-4 h-4 mr-1" />
            <span>全部完成</span>
          </div>
        </Card>
      </div>

      {/* 快捷操作 */}
      <Card className="p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">快捷操作</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {quickActions.map((action) => {
            const IconComponent = action.icon
            return (
              <Link key={action.title} href={action.href}>
                <div className="p-4 border border-gray-200 rounded-lg hover:border-gray-300 hover:shadow-sm transition-all cursor-pointer">
                  <div className="flex items-center space-x-3">
                    <div className={`p-2 rounded-lg bg-${action.color}-100`}>
                      <IconComponent className={`w-5 h-5 text-${action.color}-600`} />
                    </div>
                    <div>
                      <h3 className="font-medium text-gray-900">{action.title}</h3>
                      <p className="text-sm text-gray-600">{action.description}</p>
                    </div>
                  </div>
                </div>
              </Link>
            )
          })}
        </div>
      </Card>

      {/* 最近任务 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="p-6">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-lg font-semibold text-gray-900">最近任务</h2>
            <Link href="/admin/tasks">
              <Button variant="outline" size="sm">
                查看全部
              </Button>
            </Link>
          </div>
          <div className="space-y-3">
            {recentTasks.map((task) => (
              <div key={task.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  {getStatusIcon(task.status)}
                  <div>
                    <p className="font-medium text-gray-900">{task.name}</p>
                    <p className="text-sm text-gray-600">{task.time}</p>
                  </div>
                </div>
                <Badge color={getStatusColor(task.status)}>
                  {task.status === 'completed' ? '已完成' : 
                   task.status === 'running' ? '运行中' : '等待中'}
                </Badge>
              </div>
            ))}
          </div>
        </Card>

        <Card className="p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">系统状态</h2>
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-gray-600">数据库连接</span>
              <Badge color="green">正常</Badge>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600">文件存储</span>
              <Badge color="green">正常</Badge>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600">任务队列</span>
              <Badge color="blue">运行中</Badge>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-600">内存使用</span>
              <Badge color="yellow">68%</Badge>
            </div>
          </div>
        </Card>
      </div>
    </div>
  )
}
