'use client'

import { useState, useEffect } from 'react'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { LogLevel } from '@prisma/client'

interface LogEntry {
  id: string
  taskId: string
  level: LogLevel
  message: string
  details?: string
  createdAt: Date
  task?: {
    name: string
  }
}

interface LogViewerProps {
  taskId?: string
  maxEntries?: number
}

export function LogViewer({ taskId, maxEntries = 100 }: LogViewerProps) {
  const [logs, setLogs] = useState<LogEntry[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [filters, setFilters] = useState({
    level: '',
    search: '',
    startDate: '',
    endDate: ''
  })
  const [autoRefresh, setAutoRefresh] = useState(false)

  // 日志级别选项
  const logLevels = [
    { value: '', label: '所有级别' },
    { value: LogLevel.INFO, label: '信息' },
    { value: LogLevel.WARN, label: '警告' },
    { value: LogLevel.ERROR, label: '错误' }
  ]

  // 加载日志
  const loadLogs = async () => {
    setLoading(true)
    setError(null)

    try {
      const params = new URLSearchParams()
      if (taskId) params.append('taskId', taskId)
      if (filters.level) params.append('level', filters.level)
      if (filters.search) params.append('search', filters.search)
      if (filters.startDate) params.append('startDate', filters.startDate)
      if (filters.endDate) params.append('endDate', filters.endDate)
      params.append('limit', maxEntries.toString())

      const response = await fetch(`/api/logs?${params}`)
      const result = await response.json()

      if (result.success) {
        setLogs(result.data.logs)
      } else {
        throw new Error(result.error || '加载日志失败')
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : '加载日志失败')
    } finally {
      setLoading(false)
    }
  }

  // 初始加载
  useEffect(() => {
    loadLogs()
  }, [taskId, maxEntries])

  // 自动刷新
  useEffect(() => {
    if (autoRefresh) {
      const interval = setInterval(loadLogs, 5000) // 5秒刷新一次
      return () => clearInterval(interval)
    }
  }, [autoRefresh])

  // 处理筛选变化
  const handleFilterChange = (newFilters: Partial<typeof filters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }))
  }

  // 应用筛选
  const applyFilters = () => {
    loadLogs()
  }

  // 清空筛选
  const clearFilters = () => {
    setFilters({
      level: '',
      search: '',
      startDate: '',
      endDate: ''
    })
    setTimeout(loadLogs, 100)
  }

  // 导出日志
  const exportLogs = () => {
    const csvContent = [
      ['时间', '任务', '级别', '消息', '详情'].join(','),
      ...logs.map(log => [
        new Date(log.createdAt).toISOString(),
        log.task?.name || log.taskId,
        log.level,
        `"${log.message.replace(/"/g, '""')}"`,
        `"${(log.details || '').replace(/"/g, '""')}"`
      ].join(','))
    ].join('\n')

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    link.href = URL.createObjectURL(blob)
    link.download = `logs_${new Date().toISOString().slice(0, 10)}.csv`
    link.click()
  }

  // 获取日志级别样式
  const getLogLevelStyle = (level: LogLevel) => {
    switch (level) {
      case LogLevel.ERROR:
        return 'bg-red-50 border-l-4 border-red-400 text-red-800'
      case LogLevel.WARN:
        return 'bg-yellow-50 border-l-4 border-yellow-400 text-yellow-800'
      case LogLevel.INFO:
        return 'bg-blue-50 border-l-4 border-blue-400 text-blue-800'
      default:
        return 'bg-gray-50 border-l-4 border-gray-400 text-gray-800'
    }
  }

  // 获取日志级别图标
  const getLogLevelIcon = (level: LogLevel) => {
    switch (level) {
      case LogLevel.ERROR:
        return '❌'
      case LogLevel.WARN:
        return '⚠️'
      case LogLevel.INFO:
        return 'ℹ️'
      default:
        return '📝'
    }
  }

  // 格式化时间
  const formatTime = (date: Date) => {
    return new Date(date).toLocaleString('zh-CN')
  }

  // 格式化详情
  const formatDetails = (details: string) => {
    try {
      const parsed = JSON.parse(details)
      return JSON.stringify(parsed, null, 2)
    } catch {
      return details
    }
  }

  return (
    <div className="space-y-6">
      {/* 头部操作 */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">
            {taskId ? '任务日志' : '系统日志'}
          </h2>
          <p className="text-gray-600">
            共 {logs.length} 条日志记录
          </p>
        </div>
        <div className="flex space-x-2">
          <Button
            variant="outline"
            onClick={() => setAutoRefresh(!autoRefresh)}
            className={autoRefresh ? 'bg-green-50 text-green-700' : ''}
          >
            {autoRefresh ? '停止自动刷新' : '自动刷新'}
          </Button>
          <Button variant="outline" onClick={loadLogs} disabled={loading}>
            {loading ? '刷新中...' : '刷新'}
          </Button>
          <Button variant="outline" onClick={exportLogs} disabled={logs.length === 0}>
            导出CSV
          </Button>
        </div>
      </div>

      {/* 筛选器 */}
      <Card className="p-4">
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
          <select
            value={filters.level}
            onChange={(e) => handleFilterChange({ level: e.target.value })}
            className="px-3 py-2 border border-gray-300 rounded-md text-sm"
          >
            {logLevels.map(level => (
              <option key={level.value} value={level.value}>
                {level.label}
              </option>
            ))}
          </select>

          <Input
            placeholder="搜索消息内容..."
            value={filters.search}
            onChange={(e) => handleFilterChange({ search: e.target.value })}
          />

          <Input
            type="datetime-local"
            value={filters.startDate}
            onChange={(e) => handleFilterChange({ startDate: e.target.value })}
            placeholder="开始时间"
          />

          <Input
            type="datetime-local"
            value={filters.endDate}
            onChange={(e) => handleFilterChange({ endDate: e.target.value })}
            placeholder="结束时间"
          />

          <div className="flex space-x-2">
            <Button onClick={applyFilters} size="sm">
              应用筛选
            </Button>
            <Button variant="outline" onClick={clearFilters} size="sm">
              清空
            </Button>
          </div>
        </div>
      </Card>

      {/* 错误提示 */}
      {error && (
        <Card className="p-4 bg-red-50 border-red-200">
          <div className="flex items-center space-x-2">
            <div className="w-4 h-4 bg-red-500 rounded-full flex-shrink-0" />
            <span className="text-red-800 font-medium">加载失败</span>
          </div>
          <p className="text-red-700 mt-1">{error}</p>
        </Card>
      )}

      {/* 加载状态 */}
      {loading && (
        <div className="flex justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600" />
          <span className="ml-2 text-gray-600">加载中...</span>
        </div>
      )}

      {/* 日志列表 */}
      {!loading && logs.length > 0 && (
        <div className="space-y-2 max-h-96 overflow-y-auto">
          {logs.map((log) => (
            <Card key={log.id} className={`p-4 ${getLogLevelStyle(log.level)}`}>
              <div className="flex items-start justify-between mb-2">
                <div className="flex items-center space-x-2">
                  <span className="text-lg">{getLogLevelIcon(log.level)}</span>
                  <span className="font-medium">{log.message}</span>
                </div>
                <div className="text-xs text-gray-500">
                  {formatTime(log.createdAt)}
                </div>
              </div>

              {log.task && (
                <div className="text-sm text-gray-600 mb-2">
                  任务: {log.task.name}
                </div>
              )}

              {log.details && (
                <details className="mt-2">
                  <summary className="cursor-pointer text-sm text-gray-600 hover:text-gray-800">
                    查看详情
                  </summary>
                  <pre className="mt-2 text-xs bg-white p-2 rounded border overflow-x-auto">
                    {formatDetails(log.details)}
                  </pre>
                </details>
              )}
            </Card>
          ))}
        </div>
      )}

      {/* 空状态 */}
      {!loading && logs.length === 0 && !error && (
        <Card className="p-8 text-center">
          <div className="text-gray-400 mb-4">
            <svg className="w-16 h-16 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">暂无日志</h3>
          <p className="text-gray-600">没有找到符合条件的日志记录</p>
        </Card>
      )}

      {/* 实时状态指示器 */}
      {autoRefresh && (
        <div className="fixed bottom-4 right-4 bg-green-500 text-white px-3 py-2 rounded-full text-sm flex items-center space-x-2">
          <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
          <span>实时更新中</span>
        </div>
      )}
    </div>
  )
}