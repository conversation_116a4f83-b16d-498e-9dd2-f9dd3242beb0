'use client'

import { useState, useEffect } from 'react'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { useSystemMetrics } from '@/hooks/useSocket'
import type { SystemMetrics, TaskStats } from '@/types/batch'

export function SystemMonitor() {
  const { metrics, queueStatus, refresh } = useSystemMetrics()
  const [taskStats, setTaskStats] = useState<TaskStats | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // 加载任务统计
  const loadTaskStats = async () => {
    setLoading(true)
    setError(null)

    try {
      const response = await fetch('/api/system/stats')
      const result = await response.json()

      if (result.success) {
        setTaskStats(result.data.tasks)
      } else {
        throw new Error(result.error || '加载统计数据失败')
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : '加载统计数据失败')
    } finally {
      setLoading(false)
    }
  }

  // 初始加载
  useEffect(() => {
    loadTaskStats()
    const interval = setInterval(loadTaskStats, 30000) // 30秒更新一次
    return () => clearInterval(interval)
  }, [])

  // 格式化内存大小
  const formatMemory = (bytes: number) => {
    const mb = bytes / 1024 / 1024
    return `${mb.toFixed(1)} MB`
  }

  // 格式化百分比
  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`
  }

  // 获取健康状态颜色
  const getHealthColor = (percentage: number) => {
    if (percentage < 50) return 'text-green-600 bg-green-100'
    if (percentage < 80) return 'text-yellow-600 bg-yellow-100'
    return 'text-red-600 bg-red-100'
  }

  return (
    <div className="space-y-6">
      {/* 头部操作 */}
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold text-gray-900">系统监控</h2>
        <div className="flex space-x-2">
          <Button variant="outline" onClick={refresh}>
            刷新指标
          </Button>
          <Button variant="outline" onClick={loadTaskStats} disabled={loading}>
            {loading ? '加载中...' : '刷新统计'}
          </Button>
        </div>
      </div>

      {error && (
        <Card className="p-4 bg-red-50 border-red-200">
          <div className="flex items-center space-x-2">
            <div className="w-4 h-4 bg-red-500 rounded-full flex-shrink-0" />
            <span className="text-red-800 font-medium">加载失败</span>
          </div>
          <p className="text-red-700 mt-1">{error}</p>
        </Card>
      )}

      {/* 系统指标 */}
      {metrics && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {/* 内存使用 */}
          <Card className="p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-sm font-medium text-gray-600">内存使用</h3>
              <div className={`px-2 py-1 rounded-full text-xs font-medium ${getHealthColor(metrics.memoryUsage.percentage)}`}>
                {formatPercentage(metrics.memoryUsage.percentage)}
              </div>
            </div>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>已使用</span>
                <span>{formatMemory(metrics.memoryUsage.used)}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span>总计</span>
                <span>{formatMemory(metrics.memoryUsage.total)}</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${metrics.memoryUsage.percentage}%` }}
                />
              </div>
            </div>
          </Card>

          {/* CPU使用 */}
          <Card className="p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-sm font-medium text-gray-600">CPU使用</h3>
              <div className="px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-600">
                {formatPercentage(metrics.cpuUsage)}
              </div>
            </div>
            <div className="text-2xl font-bold text-gray-900 mb-2">
              {formatPercentage(metrics.cpuUsage)}
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-green-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${Math.min(metrics.cpuUsage, 100)}%` }}
              />
            </div>
          </Card>

          {/* 磁盘使用 */}
          <Card className="p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-sm font-medium text-gray-600">磁盘使用</h3>
              <div className={`px-2 py-1 rounded-full text-xs font-medium ${getHealthColor(metrics.diskUsage.percentage)}`}>
                {formatPercentage(metrics.diskUsage.percentage)}
              </div>
            </div>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>已使用</span>
                <span>{formatMemory(metrics.diskUsage.used)}</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-purple-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${metrics.diskUsage.percentage}%` }}
                />
              </div>
            </div>
          </Card>

          {/* 活跃任务 */}
          <Card className="p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-sm font-medium text-gray-600">活跃任务</h3>
              <div className="px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-600">
                运行中
              </div>
            </div>
            <div className="text-2xl font-bold text-gray-900 mb-2">
              {metrics.activeTasks}
            </div>
            <div className="text-sm text-gray-600">
              队列长度: {metrics.queueLength}
            </div>
          </Card>
        </div>
      )}

      {/* 任务统计 */}
      {taskStats && (
        <Card className="p-6">
          <h3 className="text-lg font-medium mb-4">任务统计</h3>
          <div className="grid grid-cols-2 md:grid-cols-6 gap-4">
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <div className="text-2xl font-bold text-gray-900">{taskStats.total}</div>
              <div className="text-sm text-gray-600">总任务</div>
            </div>
            <div className="text-center p-4 bg-yellow-50 rounded-lg">
              <div className="text-2xl font-bold text-yellow-600">{taskStats.pending}</div>
              <div className="text-sm text-gray-600">等待中</div>
            </div>
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">{taskStats.running}</div>
              <div className="text-sm text-gray-600">执行中</div>
            </div>
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">{taskStats.completed}</div>
              <div className="text-sm text-gray-600">已完成</div>
            </div>
            <div className="text-center p-4 bg-red-50 rounded-lg">
              <div className="text-2xl font-bold text-red-600">{taskStats.failed}</div>
              <div className="text-sm text-gray-600">失败</div>
            </div>
            <div className="text-center p-4 bg-gray-50 rounded-lg">
              <div className="text-2xl font-bold text-gray-600">{taskStats.cancelled}</div>
              <div className="text-sm text-gray-600">已取消</div>
            </div>
          </div>
        </Card>
      )}

      {/* 队列状态 */}
      {queueStatus && (
        <Card className="p-6">
          <h3 className="text-lg font-medium mb-4">队列状态</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <div className="text-xl font-bold text-blue-600">{queueStatus.activeWorkers}</div>
              <div className="text-sm text-gray-600">活跃工作器</div>
            </div>
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <div className="text-xl font-bold text-green-600">{queueStatus.maxConcurrency}</div>
              <div className="text-sm text-gray-600">最大并发</div>
            </div>
            <div className="text-center p-4 bg-yellow-50 rounded-lg">
              <div className="text-xl font-bold text-yellow-600">{queueStatus.queueLength}</div>
              <div className="text-sm text-gray-600">队列长度</div>
            </div>
            <div className="text-center p-4 bg-purple-50 rounded-lg">
              <div className="text-xl font-bold text-purple-600">{queueStatus.running}</div>
              <div className="text-sm text-gray-600">运行中</div>
            </div>
          </div>
        </Card>
      )}

      {/* 系统信息 */}
      {metrics && (
        <Card className="p-6">
          <h3 className="text-lg font-medium mb-4">系统信息</h3>
          <div className="grid md:grid-cols-2 gap-4 text-sm">
            <div>
              <label className="font-medium text-gray-600">更新时间</label>
              <p className="text-gray-900">{new Date(metrics.timestamp).toLocaleString('zh-CN')}</p>
            </div>
            <div>
              <label className="font-medium text-gray-600">运行环境</label>
              <p className="text-gray-900">{process.env.NODE_ENV || 'development'}</p>
            </div>
          </div>
        </Card>
      )}
    </div>
  )
}