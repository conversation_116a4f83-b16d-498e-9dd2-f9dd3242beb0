'use client'

import { useState, useEffect } from 'react'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { TaskList } from '@/components/tasks/TaskList'
import type { TaskWithRelations } from '@/types/batch'
import { TaskStatus } from '@prisma/client'

interface TaskManagerProps {
  showAdvancedControls?: boolean
}

export function TaskManager({ showAdvancedControls = true }: TaskManagerProps) {
  const [tasks, setTasks] = useState<TaskWithRelations[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [selectedTasks, setSelectedTasks] = useState<Set<string>>(new Set())
  const [batchAction, setBatchAction] = useState<string>('')
  const [actionLoading, setActionLoading] = useState(false)

  // 批量操作选项
  const batchActions = [
    { value: '', label: '选择批量操作' },
    { value: 'cancel', label: '取消选中任务' },
    { value: 'retry', label: '重试失败任务' },
    { value: 'delete', label: '删除已完成任务' }
  ]

  // 加载任务列表
  const loadTasks = async () => {
    setLoading(true)
    setError(null)

    try {
      const response = await fetch('/api/tasks/list?limit=100')
      const result = await response.json()

      if (result.success) {
        setTasks(result.data.tasks)
      } else {
        throw new Error(result.error || '加载任务列表失败')
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : '加载任务列表失败')
    } finally {
      setLoading(false)
    }
  }

  // 初始加载
  useEffect(() => {
    loadTasks()
    const interval = setInterval(loadTasks, 30000) // 30秒刷新一次
    return () => clearInterval(interval)
  }, [])

  // 处理任务选择
  const handleTaskSelect = (taskId: string, selected: boolean) => {
    const newSelected = new Set(selectedTasks)
    if (selected) {
      newSelected.add(taskId)
    } else {
      newSelected.delete(taskId)
    }
    setSelectedTasks(newSelected)
  }

  // 全选/取消全选
  const handleSelectAll = (selectAll: boolean) => {
    if (selectAll) {
      setSelectedTasks(new Set(tasks.map(task => task.id)))
    } else {
      setSelectedTasks(new Set())
    }
  }

  // 执行批量操作
  const handleBatchAction = async () => {
    if (!batchAction || selectedTasks.size === 0) return

    setActionLoading(true)

    try {
      const promises = Array.from(selectedTasks).map(async (taskId) => {
        const response = await fetch(`/api/tasks/${taskId}`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ action: batchAction })
        })
        return response.json()
      })

      const results = await Promise.all(promises)
      const failures = results.filter(result => !result.success)

      if (failures.length > 0) {
        throw new Error(`${failures.length} 个任务操作失败`)
      }

      // 重新加载任务列表
      await loadTasks()
      setSelectedTasks(new Set())
      setBatchAction('')

    } catch (error) {
      setError(error instanceof Error ? error.message : '批量操作失败')
    } finally {
      setActionLoading(false)
    }
  }

  // 清理过期任务
  const handleCleanupTasks = async () => {
    setActionLoading(true)

    try {
      const response = await fetch('/api/tasks/cleanup', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          olderThan: 7, // 清理7天前的已完成任务
          status: [TaskStatus.COMPLETED, TaskStatus.CANCELLED]
        })
      })

      const result = await response.json()
      if (result.success) {
        await loadTasks()
      } else {
        throw new Error(result.error || '清理任务失败')
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : '清理任务失败')
    } finally {
      setActionLoading(false)
    }
  }

  // 获取任务统计
  const getTaskStats = () => {
    const stats = {
      total: tasks.length,
      pending: tasks.filter(t => t.status === TaskStatus.PENDING).length,
      running: tasks.filter(t => t.status === TaskStatus.RUNNING).length,
      completed: tasks.filter(t => t.status === TaskStatus.COMPLETED).length,
      failed: tasks.filter(t => t.status === TaskStatus.FAILED).length,
      cancelled: tasks.filter(t => t.status === TaskStatus.CANCELLED).length
    }
    return stats
  }

  const stats = getTaskStats()

  return (
    <div className="space-y-6">
      {/* 头部操作 */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">任务管理</h2>
          <p className="text-gray-600">
            共 {stats.total} 个任务，{stats.running} 个运行中，{stats.pending} 个等待中
          </p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" onClick={loadTasks} disabled={loading}>
            {loading ? '刷新中...' : '刷新'}
          </Button>
          {showAdvancedControls && (
            <Button 
              variant="outline" 
              onClick={handleCleanupTasks}
              disabled={actionLoading}
            >
              清理过期任务
            </Button>
          )}
        </div>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-2 md:grid-cols-6 gap-4">
        <Card className="p-4 text-center">
          <div className="text-2xl font-bold text-gray-900">{stats.total}</div>
          <div className="text-sm text-gray-600">总任务</div>
        </Card>
        <Card className="p-4 text-center">
          <div className="text-2xl font-bold text-yellow-600">{stats.pending}</div>
          <div className="text-sm text-gray-600">等待中</div>
        </Card>
        <Card className="p-4 text-center">
          <div className="text-2xl font-bold text-blue-600">{stats.running}</div>
          <div className="text-sm text-gray-600">执行中</div>
        </Card>
        <Card className="p-4 text-center">
          <div className="text-2xl font-bold text-green-600">{stats.completed}</div>
          <div className="text-sm text-gray-600">已完成</div>
        </Card>
        <Card className="p-4 text-center">
          <div className="text-2xl font-bold text-red-600">{stats.failed}</div>
          <div className="text-sm text-gray-600">失败</div>
        </Card>
        <Card className="p-4 text-center">
          <div className="text-2xl font-bold text-gray-600">{stats.cancelled}</div>
          <div className="text-sm text-gray-600">已取消</div>
        </Card>
      </div>

      {/* 批量操作 */}
      {showAdvancedControls && (
        <Card className="p-4">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={selectedTasks.size === tasks.length && tasks.length > 0}
                onChange={(e) => handleSelectAll(e.target.checked)}
                className="rounded border-gray-300"
              />
              <span className="text-sm text-gray-600">
                已选择 {selectedTasks.size} 个任务
              </span>
            </div>
            
            <select
              value={batchAction}
              onChange={(e) => setBatchAction(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md text-sm"
              disabled={selectedTasks.size === 0}
            >
              {batchActions.map(action => (
                <option key={action.value} value={action.value}>
                  {action.label}
                </option>
              ))}
            </select>

            <Button
              onClick={handleBatchAction}
              disabled={!batchAction || selectedTasks.size === 0 || actionLoading}
              size="sm"
            >
              {actionLoading ? '执行中...' : '执行'}
            </Button>
          </div>
        </Card>
      )}

      {/* 错误提示 */}
      {error && (
        <Card className="p-4 bg-red-50 border-red-200">
          <div className="flex items-center space-x-2">
            <div className="w-4 h-4 bg-red-500 rounded-full flex-shrink-0" />
            <span className="text-red-800 font-medium">操作失败</span>
          </div>
          <p className="text-red-700 mt-1">{error}</p>
          <Button
            variant="outline"
            size="sm"
            className="mt-2"
            onClick={() => setError(null)}
          >
            关闭
          </Button>
        </Card>
      )}

      {/* 任务列表 */}
      <div className="space-y-4">
        {tasks.map(task => (
          <Card key={task.id} className="p-4">
            <div className="flex items-start space-x-4">
              {showAdvancedControls && (
                <input
                  type="checkbox"
                  checked={selectedTasks.has(task.id)}
                  onChange={(e) => handleTaskSelect(task.id, e.target.checked)}
                  className="mt-1 rounded border-gray-300"
                />
              )}
              
              <div className="flex-1">
                <div className="flex items-center justify-between mb-2">
                  <h3 className="font-medium text-gray-900">{task.name}</h3>
                  <div className={`px-2 py-1 rounded-full text-xs font-medium border ${
                    task.status === TaskStatus.PENDING ? 'bg-yellow-100 text-yellow-800 border-yellow-200' :
                    task.status === TaskStatus.RUNNING ? 'bg-blue-100 text-blue-800 border-blue-200' :
                    task.status === TaskStatus.COMPLETED ? 'bg-green-100 text-green-800 border-green-200' :
                    task.status === TaskStatus.FAILED ? 'bg-red-100 text-red-800 border-red-200' :
                    'bg-gray-100 text-gray-800 border-gray-200'
                  }`}>
                    {task.status === TaskStatus.PENDING ? '等待中' :
                     task.status === TaskStatus.RUNNING ? '执行中' :
                     task.status === TaskStatus.COMPLETED ? '已完成' :
                     task.status === TaskStatus.FAILED ? '失败' : '已取消'}
                  </div>
                </div>

                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-600 mb-3">
                  <div>类型: {task.type === 'BATCH' ? '批量' : '单个'}</div>
                  <div>商品: {task.totalItems}</div>
                  <div>已处理: {task.processedItems}</div>
                  <div>失败: {task.failedItems}</div>
                </div>

                {task.status === TaskStatus.RUNNING && (
                  <div className="mb-3">
                    <div className="flex justify-between text-sm text-gray-600 mb-1">
                      <span>进度</span>
                      <span>{task.processedItems}/{task.totalItems} ({Math.round((task.processedItems / task.totalItems) * 100)}%)</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${Math.round((task.processedItems / task.totalItems) * 100)}%` }}
                      />
                    </div>
                  </div>
                )}

                <div className="flex justify-between items-center text-sm text-gray-500">
                  <div>
                    创建: {new Date(task.createdAt).toLocaleString('zh-CN')}
                    {task.completedAt && ` | 完成: ${new Date(task.completedAt).toLocaleString('zh-CN')}`}
                  </div>
                  <div className="flex space-x-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => window.open(`/tasks/${task.id}`, '_blank')}
                    >
                      查看详情
                    </Button>
                    {task.status === TaskStatus.COMPLETED && task.outputFile && (
                      <Button
                        size="sm"
                        onClick={() => window.open(`/api/download/tasks/${task.id}`, '_blank')}
                      >
                        下载
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </Card>
        ))}
      </div>

      {/* 空状态 */}
      {!loading && tasks.length === 0 && (
        <Card className="p-8 text-center">
          <div className="text-gray-400 mb-4">
            <svg className="w-16 h-16 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">暂无任务</h3>
          <p className="text-gray-600">还没有创建任何任务</p>
        </Card>
      )}
    </div>
  )
}