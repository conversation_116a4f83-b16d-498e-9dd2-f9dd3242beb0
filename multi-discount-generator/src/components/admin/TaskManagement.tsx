'use client'

import { useState, useEffect } from 'react'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { Badge } from '@/components/ui/Badge'
import { 
  Download, 
  RefreshCw, 
  Clock, 
  CheckCircle, 
  XCircle, 
  AlertCircle,
  FileText,
  Calendar
} from 'lucide-react'

interface Task {
  id: string
  productId: string
  title: string
  status: 'pending' | 'processing' | 'completed' | 'failed'
  progress: number
  timeSlots: any[]
  excelData: any[]
  createdAt: string
  completedAt?: string
  errorMessage?: string
}

export function TaskManagement() {
  const [tasks, setTasks] = useState<Task[]>([])
  const [loading, setLoading] = useState(false)
  const [refreshing, setRefreshing] = useState(false)

  // 加载任务列表
  const loadTasks = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/batch/tasks')
      const result = await response.json()
      
      if (result.success) {
        setTasks(result.data)
      } else {
        console.error('加载任务失败:', result.error)
      }
    } catch (error) {
      console.error('加载任务失败:', error)
    } finally {
      setLoading(false)
    }
  }

  // 刷新任务状态
  const refreshTasks = async () => {
    try {
      setRefreshing(true)
      await loadTasks()
    } finally {
      setRefreshing(false)
    }
  }

  // 导出任务结果
  const exportTask = async (taskId: string) => {
    try {
      const response = await fetch(`/api/batch/tasks?action=export&taskId=${taskId}`)
      
      if (response.ok) {
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `活动时间表_${taskId}_${new Date().toISOString().slice(0, 10)}.xlsx`
        document.body.appendChild(a)
        a.click()
        window.URL.revokeObjectURL(url)
        document.body.removeChild(a)
      } else {
        alert('导出失败')
      }
    } catch (error) {
      console.error('导出失败:', error)
      alert('导出失败')
    }
  }

  // 获取状态图标
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="w-4 h-4 text-gray-500" />
      case 'processing':
        return <RefreshCw className="w-4 h-4 text-blue-500 animate-spin" />
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-500" />
      case 'failed':
        return <XCircle className="w-4 h-4 text-red-500" />
      default:
        return <AlertCircle className="w-4 h-4 text-gray-500" />
    }
  }

  // 获取状态徽章
  const getStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { label: '等待中', variant: 'secondary' as const },
      processing: { label: '处理中', variant: 'primary' as const },
      completed: { label: '已完成', variant: 'success' as const },
      failed: { label: '失败', variant: 'destructive' as const }
    }
    
    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending
    return <Badge variant={config.variant}>{config.label}</Badge>
  }

  // 格式化时间
  const formatTime = (timeStr: string) => {
    return new Date(timeStr).toLocaleString('zh-CN')
  }

  useEffect(() => {
    loadTasks()
    
    // 设置定时刷新（每30秒）
    const interval = setInterval(() => {
      loadTasks()
    }, 30000)
    
    return () => clearInterval(interval)
  }, [])

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">任务管理</h1>
          <p className="text-gray-600">查看和管理批量生成任务</p>
        </div>
        <Button
          onClick={refreshTasks}
          disabled={refreshing}
          variant="outline"
        >
          <RefreshCw className={`w-4 h-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
          刷新
        </Button>
      </div>

      {/* 任务统计 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="p-4">
          <div className="flex items-center">
            <FileText className="w-8 h-8 text-blue-500 mr-3" />
            <div>
              <p className="text-sm font-medium text-gray-600">总任务数</p>
              <p className="text-2xl font-bold text-gray-900">{tasks.length}</p>
            </div>
          </div>
        </Card>
        
        <Card className="p-4">
          <div className="flex items-center">
            <RefreshCw className="w-8 h-8 text-yellow-500 mr-3" />
            <div>
              <p className="text-sm font-medium text-gray-600">处理中</p>
              <p className="text-2xl font-bold text-gray-900">
                {tasks.filter(t => t.status === 'processing' || t.status === 'pending').length}
              </p>
            </div>
          </div>
        </Card>
        
        <Card className="p-4">
          <div className="flex items-center">
            <CheckCircle className="w-8 h-8 text-green-500 mr-3" />
            <div>
              <p className="text-sm font-medium text-gray-600">已完成</p>
              <p className="text-2xl font-bold text-gray-900">
                {tasks.filter(t => t.status === 'completed').length}
              </p>
            </div>
          </div>
        </Card>
        
        <Card className="p-4">
          <div className="flex items-center">
            <XCircle className="w-8 h-8 text-red-500 mr-3" />
            <div>
              <p className="text-sm font-medium text-gray-600">失败</p>
              <p className="text-2xl font-bold text-gray-900">
                {tasks.filter(t => t.status === 'failed').length}
              </p>
            </div>
          </div>
        </Card>
      </div>

      {/* 任务列表 */}
      <Card className="p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">任务列表</h2>
        
        {loading ? (
          <div className="text-center py-8">
            <RefreshCw className="w-8 h-8 animate-spin mx-auto text-gray-400 mb-2" />
            <p className="text-gray-500">加载中...</p>
          </div>
        ) : tasks.length === 0 ? (
          <div className="text-center py-8">
            <FileText className="w-12 h-12 mx-auto text-gray-400 mb-2" />
            <p className="text-gray-500">暂无任务</p>
            <p className="text-sm text-gray-400">请到批量生成页面创建任务</p>
          </div>
        ) : (
          <div className="space-y-4">
            {tasks.map((task) => (
              <div
                key={task.id}
                className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
              >
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      {getStatusIcon(task.status)}
                      <h3 className="font-medium text-gray-900">{task.title}</h3>
                      {getStatusBadge(task.status)}
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600">
                      <div>
                        <span className="font-medium">商品ID:</span> {task.productId}
                      </div>
                      <div>
                        <span className="font-medium">创建时间:</span> {formatTime(task.createdAt)}
                      </div>
                      {task.completedAt && (
                        <div>
                          <span className="font-medium">完成时间:</span> {formatTime(task.completedAt)}
                        </div>
                      )}
                    </div>
                    
                    {task.status === 'processing' && (
                      <div className="mt-3">
                        <div className="flex justify-between text-sm text-gray-600 mb-1">
                          <span>进度</span>
                          <span>{task.progress}%</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                            style={{ width: `${task.progress}%` }}
                          />
                        </div>
                      </div>
                    )}
                    
                    {task.errorMessage && (
                      <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded text-sm text-red-600">
                        错误: {task.errorMessage}
                      </div>
                    )}
                  </div>
                  
                  <div className="ml-4">
                    {task.status === 'completed' && (
                      <Button
                        onClick={() => exportTask(task.id)}
                        size="sm"
                        variant="outline"
                      >
                        <Download className="w-4 h-4 mr-2" />
                        导出
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </Card>
    </div>
  )
}
