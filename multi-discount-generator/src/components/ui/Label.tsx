import React from 'react'

interface LabelProps {
  htmlFor?: string
  children: React.ReactNode
  required?: boolean
  className?: string
}

export function Label({ 
  htmlFor, 
  children, 
  required = false, 
  className = '' 
}: LabelProps) {
  return (
    <label 
      htmlFor={htmlFor}
      className={`
        block text-sm font-medium text-gray-700 mb-1
        ${className}
      `}
    >
      {children}
      {required && <span className="text-red-500 ml-1">*</span>}
    </label>
  )
}
