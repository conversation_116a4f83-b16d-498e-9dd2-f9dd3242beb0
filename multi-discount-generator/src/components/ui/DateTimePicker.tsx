'use client'

import { forwardRef, useId } from 'react'
import { format } from 'date-fns'
import { clsx } from 'clsx'
import { TIME_FORMAT } from '@/constants'

interface DateTimePickerProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'value' | 'onChange'> {
  label?: string
  error?: string
  helperText?: string
  value?: Date
  onChange?: (date: Date | null) => void
  showTime?: boolean
  timeOnly?: boolean
}

const DateTimePicker = forwardRef<HTMLInputElement, DateTimePickerProps>(
  ({ 
    className, 
    label, 
    error, 
    helperText, 
    value, 
    onChange, 
    showTime = true,
    timeOnly = false,
    id, 
    ...props 
  }, ref) => {
    const generatedId = useId()
    const inputId = id || generatedId

    // 格式化日期时间值
    const formatValue = (date: Date | undefined) => {
      if (!date) return ''
      
      if (timeOnly) {
        return format(date, 'HH:mm:ss')
      } else if (showTime) {
        return format(date, "yyyy-MM-dd'T'HH:mm")
      } else {
        return format(date, 'yyyy-MM-dd')
      }
    }

    // 处理输入变化
    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const inputValue = e.target.value
      
      if (!inputValue) {
        onChange?.(null)
        return
      }

      try {
        let newDate: Date
        
        if (timeOnly) {
          // 时间模式：使用今天的日期 + 输入的时间
          const today = new Date()
          const [hours, minutes, seconds = '00'] = inputValue.split(':')
          newDate = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 
                           parseInt(hours), parseInt(minutes), parseInt(seconds))
        } else {
          // 日期时间模式
          newDate = new Date(inputValue)
        }

        if (!isNaN(newDate.getTime())) {
          onChange?.(newDate)
        }
      } catch (error) {
        console.warn('日期时间解析失败:', error)
      }
    }

    // 确定输入类型
    const getInputType = () => {
      if (timeOnly) return 'time'
      if (showTime) return 'datetime-local'
      return 'date'
    }

    return (
      <div className="w-full">
        {label && (
          <label 
            htmlFor={inputId}
            className="mb-2 block text-sm font-medium text-gray-700"
          >
            {label}
          </label>
        )}
        <input
          id={inputId}
          type={getInputType()}
          step={timeOnly ? "1" : undefined} // 支持秒级精度
          value={formatValue(value)}
          onChange={handleChange}
          className={clsx(
            'block w-full rounded-md border border-gray-300 px-3 py-2 text-sm shadow-sm transition-colors',
            'focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500',
            'disabled:cursor-not-allowed disabled:bg-gray-50 disabled:text-gray-500',
            error && 'border-red-500 focus:border-red-500 focus:ring-red-500',
            className
          )}
          ref={ref}
          {...props}
        />
        {error && (
          <p className="mt-1 text-sm text-red-600">{error}</p>
        )}
        {helperText && !error && (
          <p className="mt-1 text-sm text-gray-500">{helperText}</p>
        )}
        {value && (
          <p className="mt-1 text-xs text-gray-400">
            格式化结果: {format(value, TIME_FORMAT)}
          </p>
        )}
      </div>
    )
  }
)

DateTimePicker.displayName = 'DateTimePicker'

export { DateTimePicker }