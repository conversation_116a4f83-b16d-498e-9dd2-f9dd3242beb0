'use client'

import React from 'react'

interface SimpleSelectProps {
  value?: string
  onValueChange?: (value: string) => void
  placeholder?: string
  disabled?: boolean
  className?: string
  children?: React.ReactNode
}

export function SimpleSelect({ 
  value, 
  onValueChange, 
  placeholder, 
  disabled = false, 
  className = '', 
  children 
}: SimpleSelectProps) {
  const handleChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    onValueChange?.(e.target.value)
  }

  return (
    <select
      value={value}
      onChange={handleChange}
      disabled={disabled}
      className={`
        block w-full rounded-md border border-gray-300 px-3 py-2 text-sm shadow-sm
        focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500
        disabled:cursor-not-allowed disabled:bg-gray-50 disabled:text-gray-500
        ${className}
      `}
    >
      {placeholder && (
        <option value="" disabled>
          {placeholder}
        </option>
      )}
      {children}
    </select>
  )
}
