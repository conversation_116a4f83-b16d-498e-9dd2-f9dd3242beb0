'use client'

import React, { Component, ReactNode } from 'react'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'

interface Props {
  children: ReactNode
  fallback?: ReactNode
}

interface State {
  hasError: boolean
  error?: Error
  errorInfo?: React.ErrorInfo
}

export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo)
    this.setState({ error, errorInfo })
  }

  handleReset = () => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined })
  }

  handleReload = () => {
    window.location.reload()
  }

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback
      }

      return (
        <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
          <Card className="max-w-lg w-full p-6">
            <div className="text-center">
              <div className="text-red-500 text-6xl mb-4">⚠️</div>
              <h1 className="text-2xl font-bold text-gray-900 mb-2">
                出现了一些问题
              </h1>
              <p className="text-gray-600 mb-6">
                应用程序遇到了意外错误。这可能是由于网络问题或浏览器兼容性问题导致的。
              </p>
              
              {process.env.NODE_ENV === 'development' && this.state.error && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6 text-left">
                  <h3 className="font-semibold text-red-800 mb-2">错误详情：</h3>
                  <pre className="text-sm text-red-700 whitespace-pre-wrap overflow-auto max-h-40">
                    {this.state.error.message}
                    {this.state.errorInfo?.componentStack}
                  </pre>
                </div>
              )}

              <div className="space-y-3">
                <Button 
                  onClick={this.handleReset}
                  className="w-full"
                >
                  重试
                </Button>
                <Button 
                  onClick={this.handleReload}
                  variant="outline"
                  className="w-full"
                >
                  刷新页面
                </Button>
              </div>

              <div className="mt-6 text-sm text-gray-500">
                <p>如果问题持续存在，请尝试：</p>
                <ul className="mt-2 space-y-1 text-left">
                  <li>• 清除浏览器缓存</li>
                  <li>• 检查网络连接</li>
                  <li>• 使用最新版本的浏览器</li>
                </ul>
              </div>
            </div>
          </Card>
        </div>
      )
    }

    return this.props.children
  }
}

// 简化版错误边界，用于小组件
export function SimpleErrorBoundary({ 
  children, 
  fallback 
}: { 
  children: ReactNode
  fallback?: ReactNode 
}) {
  return (
    <ErrorBoundary 
      fallback={fallback || (
        <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
          <p className="text-red-700">组件加载失败，请刷新页面重试</p>
        </div>
      )}
    >
      {children}
    </ErrorBoundary>
  )
}
