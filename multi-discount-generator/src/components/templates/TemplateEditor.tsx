'use client'

import { useState } from 'react'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { Label } from '@/components/ui/Label'
import { SimpleSelect as Select } from '@/components/ui/SimpleSelect'
import { Save, X } from 'lucide-react'

interface Template {
  id: string
  name: string
  description: string
  type: 'standard' | 'batch' | 'custom'
  isDefault: boolean
}

interface TemplateEditorProps {
  template?: Template
  onSave: (template: Partial<Template>) => Promise<void>
  onCancel: () => void
  loading?: boolean
}

export function TemplateEditor({ 
  template, 
  onSave, 
  onCancel, 
  loading = false 
}: TemplateEditorProps) {
  const [formData, setFormData] = useState({
    name: template?.name || '',
    description: template?.description || '',
    type: template?.type || 'standard',
    isDefault: template?.isDefault || false
  })
  const [errors, setErrors] = useState<Record<string, string>>({})

  // 表单验证
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {}

    if (!formData.name.trim()) {
      newErrors.name = '模板名称不能为空'
    }

    if (!formData.description.trim()) {
      newErrors.description = '模板描述不能为空'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  // 处理提交
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    try {
      await onSave(formData)
    } catch (error) {
      console.error('保存模板失败:', error)
    }
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <Card className="p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">
          {template ? '编辑模板' : '创建模板'}
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label htmlFor="name">模板名称 *</Label>
            <Input
              id="name"
              value={formData.name}
              onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
              placeholder="请输入模板名称"
              error={errors.name}
            />
          </div>

          <div>
            <Label htmlFor="type">模板类型</Label>
            <Select
              value={formData.type}
              onValueChange={(value) => setFormData(prev => ({ 
                ...prev, 
                type: value as 'standard' | 'batch' | 'custom'
              }))}
            >
              <option value="standard">标准模板</option>
              <option value="batch">批量模板</option>
              <option value="custom">自定义模板</option>
            </Select>
          </div>

          <div className="md:col-span-2">
            <Label htmlFor="description">模板描述 *</Label>
            <textarea
              id="description"
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              placeholder="请输入模板描述"
              rows={3}
              className={`
                block w-full rounded-md border px-3 py-2 text-sm shadow-sm
                focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500
                ${errors.description ? 'border-red-300' : 'border-gray-300'}
              `}
            />
            {errors.description && (
              <p className="mt-1 text-sm text-red-600">{errors.description}</p>
            )}
          </div>

          <div className="md:col-span-2">
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={formData.isDefault}
                onChange={(e) => setFormData(prev => ({ ...prev, isDefault: e.target.checked }))}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span className="ml-2 text-sm text-gray-700">设为默认模板</span>
            </label>
          </div>
        </div>
      </Card>

      {/* 模板配置预览 */}
      <Card className="p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">模板配置</h3>
        
        <div className="bg-gray-50 rounded-lg p-4">
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="font-medium text-gray-700">类型:</span>
              <span className="ml-2 text-gray-600">
                {formData.type === 'standard' ? '标准模板' : 
                 formData.type === 'batch' ? '批量模板' : '自定义模板'}
              </span>
            </div>
            <div>
              <span className="font-medium text-gray-700">默认:</span>
              <span className="ml-2 text-gray-600">
                {formData.isDefault ? '是' : '否'}
              </span>
            </div>
          </div>
          
          <div className="mt-3">
            <span className="font-medium text-gray-700">支持字段:</span>
            <div className="mt-2 flex flex-wrap gap-2">
              {formData.type === 'standard' && (
                <>
                  <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded">商品ID</span>
                  <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded">活动类型</span>
                  <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded">开始时间</span>
                  <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded">结束时间</span>
                  <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded">活动区域</span>
                  <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded">满几件</span>
                  <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded">折扣</span>
                </>
              )}
              {formData.type === 'batch' && (
                <>
                  <span className="px-2 py-1 bg-green-100 text-green-800 text-xs rounded">商品ID</span>
                  <span className="px-2 py-1 bg-green-100 text-green-800 text-xs rounded">时间模式</span>
                  <span className="px-2 py-1 bg-green-100 text-green-800 text-xs rounded">开始日期</span>
                  <span className="px-2 py-1 bg-green-100 text-green-800 text-xs rounded">结束日期</span>
                  <span className="px-2 py-1 bg-green-100 text-green-800 text-xs rounded">每日时间</span>
                  <span className="px-2 py-1 bg-green-100 text-green-800 text-xs rounded">周末全天</span>
                </>
              )}
              {formData.type === 'custom' && (
                <span className="px-2 py-1 bg-purple-100 text-purple-800 text-xs rounded">自定义字段</span>
              )}
            </div>
          </div>
        </div>
      </Card>

      <div className="flex justify-end space-x-3">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={loading}
        >
          <X className="w-4 h-4 mr-2" />
          取消
        </Button>
        <Button
          type="submit"
          disabled={loading}
        >
          <Save className="w-4 h-4 mr-2" />
          {loading ? '保存中...' : '保存'}
        </Button>
      </div>
    </form>
  )
}
