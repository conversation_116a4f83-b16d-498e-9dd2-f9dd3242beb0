'use client'

import { useState, useMemo } from 'react'
import { format } from 'date-fns'
import { Button } from '@/components/ui'
import { EXCEL_HEADERS, TIME_FORMAT } from '@/constants'
import type { ExcelRowData } from '@/types'
import { clsx } from 'clsx'

interface PreviewTableProps {
  data: ExcelRowData[]
  onExport: () => void
  loading?: boolean
}

type SortField = keyof ExcelRowData
type SortDirection = 'asc' | 'desc'

export function PreviewTable({ data, onExport, loading = false }: PreviewTableProps) {
  const [sortField, setSortField] = useState<SortField>('商品ID')
  const [sortDirection, setSortDirection] = useState<SortDirection>('asc')
  const [filterText, setFilterText] = useState('')
  const [currentPage, setCurrentPage] = useState(1)
  const itemsPerPage = 10

  // 排序和筛选数据
  const filteredAndSortedData = useMemo(() => {
    let filtered = data

    // 筛选
    if (filterText) {
      filtered = data.filter(row => 
        Object.values(row).some(value => 
          String(value).toLowerCase().includes(filterText.toLowerCase())
        )
      )
    }

    // 排序
    filtered.sort((a, b) => {
      const aValue = a[sortField]
      const bValue = b[sortField]
      
      let comparison = 0
      if (typeof aValue === 'string' && typeof bValue === 'string') {
        comparison = aValue.localeCompare(bValue)
      } else if (typeof aValue === 'number' && typeof bValue === 'number') {
        comparison = aValue - bValue
      } else {
        comparison = String(aValue).localeCompare(String(bValue))
      }
      
      return sortDirection === 'asc' ? comparison : -comparison
    })

    return filtered
  }, [data, filterText, sortField, sortDirection])

  // 分页数据
  const paginatedData = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage
    return filteredAndSortedData.slice(startIndex, startIndex + itemsPerPage)
  }, [filteredAndSortedData, currentPage])

  const totalPages = Math.ceil(filteredAndSortedData.length / itemsPerPage)

  // 处理排序
  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc')
    } else {
      setSortField(field)
      setSortDirection('asc')
    }
  }

  // 检查是否跨天
  const isCrossDaySlot = (row: ExcelRowData) => {
    const startTimeKey = '开始时间\n示例：\n2020-03-27 00:00:00'
    const endTimeKey = '结束时间\n示例：\n2020-03-27 23:59:59'
    
    try {
      const startTime = new Date(row[startTimeKey])
      const endTime = new Date(row[endTimeKey])
      return startTime.getTime() > endTime.getTime()
    } catch {
      return false
    }
  }

  // 简化的列标题（用于显示）
  const getSimpleHeader = (header: string) => {
    if (header.includes('开始时间')) return '开始时间'
    if (header.includes('结束时间')) return '结束时间'
    if (header.includes('活动区域')) return '活动区域'
    if (header.includes('满几件')) return '满几件'
    if (header.includes('折扣')) return '折扣'
    if (header.includes('立减金额')) return '立减金额'
    return header
  }

  if (data.length === 0) {
    return (
      <div className="rounded-lg border border-gray-200 bg-white p-8 text-center">
        <div className="mx-auto h-12 w-12 text-gray-400">
          📊
        </div>
        <h3 className="mt-2 text-sm font-medium text-gray-900">暂无数据</h3>
        <p className="mt-1 text-sm text-gray-500">
          请先配置商品信息和时间参数，然后生成预览数据
        </p>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {/* 工具栏 */}
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <span className="text-sm font-medium text-gray-700">
              共 {filteredAndSortedData.length} 条记录
            </span>
            {filterText && (
              <span className="text-sm text-gray-500">
                (已筛选)
              </span>
            )}
          </div>
        </div>

        <div className="flex items-center space-x-3">
          {/* 搜索框 */}
          <input
            type="text"
            placeholder="搜索..."
            value={filterText}
            onChange={(e) => {
              setFilterText(e.target.value)
              setCurrentPage(1)
            }}
            className="block w-48 rounded-md border border-gray-300 px-3 py-2 text-sm placeholder-gray-400 shadow-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
          />

          {/* 导出按钮 */}
          <Button
            onClick={onExport}
            loading={loading}
            className="whitespace-nowrap"
          >
            导出Excel
          </Button>
        </div>
      </div>

      {/* 表格 */}
      <div className="overflow-hidden rounded-lg border border-gray-200 bg-white">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                {EXCEL_HEADERS.map((header) => (
                  <th
                    key={header}
                    className="cursor-pointer px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 hover:bg-gray-100"
                    onClick={() => handleSort(header as SortField)}
                  >
                    <div className="flex items-center space-x-1">
                      <span>{getSimpleHeader(header)}</span>
                      {sortField === header && (
                        <span className="text-blue-500">
                          {sortDirection === 'asc' ? '↑' : '↓'}
                        </span>
                      )}
                    </div>
                  </th>
                ))}
                <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                  状态
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200 bg-white">
              {paginatedData.map((row, index) => {
                const isCrossDay = isCrossDaySlot(row)
                return (
                  <tr key={index} className="hover:bg-gray-50">
                    {EXCEL_HEADERS.map((header) => (
                      <td
                        key={header}
                        className="whitespace-nowrap px-6 py-4 text-sm text-gray-900"
                      >
                        {header.includes('时间') ? (
                          <code className="rounded bg-gray-100 px-2 py-1 text-xs">
                            {row[header as keyof ExcelRowData]}
                          </code>
                        ) : (
                          row[header as keyof ExcelRowData]
                        )}
                      </td>
                    ))}
                    <td className="whitespace-nowrap px-6 py-4 text-sm">
                      {isCrossDay && (
                        <span className="inline-flex items-center rounded-full bg-yellow-100 px-2.5 py-0.5 text-xs font-medium text-yellow-800">
                          跨天
                        </span>
                      )}
                    </td>
                  </tr>
                )
              })}
            </tbody>
          </table>
        </div>
      </div>

      {/* 分页 */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-700">
              第 {currentPage} 页，共 {totalPages} 页
            </span>
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
              disabled={currentPage === 1}
            >
              上一页
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
              disabled={currentPage === totalPages}
            >
              下一页
            </Button>
          </div>
        </div>
      )}

      {/* 统计信息 */}
      <div className="rounded-md bg-blue-50 p-3">
        <div className="grid grid-cols-2 gap-4 text-sm sm:grid-cols-4">
          <div>
            <span className="font-medium text-blue-900">总记录数：</span>
            <span className="text-blue-700">{data.length}</span>
          </div>
          <div>
            <span className="font-medium text-blue-900">跨天时段：</span>
            <span className="text-blue-700">
              {data.filter(row => isCrossDaySlot(row)).length}
            </span>
          </div>
          <div>
            <span className="font-medium text-blue-900">商品ID：</span>
            <span className="text-blue-700">
              {data.length > 0 ? data[0].商品ID : '-'}
            </span>
          </div>
          <div>
            <span className="font-medium text-blue-900">活动类型：</span>
            <span className="text-blue-700">
              {data.length > 0 ? data[0].活动类型 : '-'}
            </span>
          </div>
        </div>
      </div>
    </div>
  )
}