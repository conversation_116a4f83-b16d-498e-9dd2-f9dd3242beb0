'use client'

import { useState, useEffect } from 'react'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { Badge } from '@/components/ui/Badge'
import { TemplateUpload } from './TemplateUpload'
import { TemplateEditor } from './TemplateEditor'
import { TemplateService } from '@/lib/template/TemplateService'
import { 
  Plus, 
  Search, 
  Download, 
  Upload, 
  Edit, 
  Trash2, 
  FileText,
  Calendar,
  Users
} from 'lucide-react'

interface Template {
  id: string
  name: string
  description: string
  type: 'standard' | 'batch' | 'custom'
  createdAt: Date
  updatedAt: Date
  usageCount: number
  isDefault: boolean
}

type View = 'list' | 'upload' | 'create' | 'edit'

export function TemplateManagement() {
  const [currentView, setCurrentView] = useState<View>('list')
  const [templates, setTemplates] = useState<Template[]>([])
  const [loading, setLoading] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [editingTemplate, setEditingTemplate] = useState<Template | null>(null)

  // 加载模板列表
  useEffect(() => {
    loadTemplates()
  }, [])

  const loadTemplates = async () => {
    try {
      setLoading(true)
      // 模拟数据
      const mockTemplates: Template[] = [
        {
          id: '1',
          name: '标准活动模板',
          description: '包含商品ID、活动时间、区域等基础字段',
          type: 'standard',
          createdAt: new Date('2024-01-01'),
          updatedAt: new Date('2024-01-15'),
          usageCount: 45,
          isDefault: true
        },
        {
          id: '2',
          name: '批量时间生成模板',
          description: '支持日期范围和时间模式的批量生成',
          type: 'batch',
          createdAt: new Date('2024-01-10'),
          updatedAt: new Date('2024-01-20'),
          usageCount: 23,
          isDefault: false
        },
        {
          id: '3',
          name: '自定义商品模板',
          description: '包含商品详细信息和供应商数据',
          type: 'custom',
          createdAt: new Date('2024-01-15'),
          updatedAt: new Date('2024-01-25'),
          usageCount: 12,
          isDefault: false
        }
      ]
      setTemplates(mockTemplates)
    } catch (error) {
      console.error('加载模板失败:', error)
    } finally {
      setLoading(false)
    }
  }

  // 筛选模板
  const filteredTemplates = templates.filter(template =>
    template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    template.description.toLowerCase().includes(searchTerm.toLowerCase())
  )

  // 下载模板
  const handleDownloadTemplate = (template: Template) => {
    try {
      if (template.type === 'standard') {
        TemplateService.exportTemplate('standard', `${template.name}.xlsx`)
      } else if (template.type === 'batch') {
        TemplateService.exportTemplate('batch', `${template.name}.xlsx`)
      } else {
        TemplateService.exportTemplate('standard', `${template.name}.xlsx`)
      }
    } catch (error) {
      console.error('下载模板失败:', error)
      alert('下载模板失败: ' + error.message)
    }
  }

  // 编辑模板
  const handleEditTemplate = (template: Template) => {
    setEditingTemplate(template)
    setCurrentView('edit')
  }

  // 保存模板
  const handleSaveTemplate = async (templateData: Partial<Template>) => {
    try {
      if (editingTemplate) {
        // 更新模板
        setTemplates(prev => prev.map(t =>
          t.id === editingTemplate.id
            ? { ...t, ...templateData, updatedAt: new Date() }
            : t
        ))
      } else {
        // 创建新模板
        const newTemplate: Template = {
          id: Date.now().toString(),
          name: templateData.name || '',
          description: templateData.description || '',
          type: templateData.type || 'standard',
          createdAt: new Date(),
          updatedAt: new Date(),
          usageCount: 0,
          isDefault: templateData.isDefault || false
        }
        setTemplates(prev => [...prev, newTemplate])
      }

      setCurrentView('list')
      setEditingTemplate(null)
    } catch (error) {
      console.error('保存模板失败:', error)
      throw error
    }
  }

  // 删除模板
  const handleDeleteTemplate = async (templateId: string) => {
    if (!confirm('确定要删除这个模板吗？')) return

    try {
      // 这里应该调用删除API
      setTemplates(prev => prev.filter(t => t.id !== templateId))
    } catch (error) {
      console.error('删除模板失败:', error)
    }
  }

  // 处理模板上传完成
  const handleTemplateProcessed = async (data: any) => {
    try {
      // 这里应该调用创建任务API
      console.log('模板处理完成:', data)
      setCurrentView('list')
      // 可以跳转到任务页面查看结果
    } catch (error) {
      console.error('处理模板失败:', error)
    }
  }

  const getTypeLabel = (type: string) => {
    const labels = {
      standard: '标准模板',
      batch: '批量模板',
      custom: '自定义模板'
    }
    return labels[type] || type
  }

  const getTypeColor = (type: string) => {
    const colors = {
      standard: 'blue',
      batch: 'green',
      custom: 'purple'
    }
    return colors[type] || 'gray'
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="mb-6">
        <div className="flex justify-between items-center mb-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">模板管理</h1>
            <p className="text-gray-600">管理Excel模板和批量配置</p>
          </div>
          
          <div className="flex space-x-3">
            <Button
              variant="outline"
              onClick={() => setCurrentView('upload')}
            >
              <Upload className="w-4 h-4 mr-2" />
              上传模板
            </Button>
            <Button
              onClick={() => setCurrentView('create')}
            >
              <Plus className="w-4 h-4 mr-2" />
              新建模板
            </Button>
          </div>
        </div>

        {currentView === 'list' && (
          <Card className="p-4">
            <div className="flex space-x-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <Input
                    placeholder="搜索模板名称或描述..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <Button variant="outline">
                <Download className="w-4 h-4 mr-2" />
                批量下载
              </Button>
            </div>
          </Card>
        )}
      </div>

      {/* 内容区域 */}
      {currentView === 'list' && (
        <div className="space-y-4">
          {loading ? (
            <Card className="p-8">
              <div className="flex justify-center items-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                <span className="ml-3 text-gray-600">加载中...</span>
              </div>
            </Card>
          ) : filteredTemplates.length === 0 ? (
            <Card className="p-8">
              <div className="text-center">
                <FileText className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">暂无模板</h3>
                <p className="mt-1 text-sm text-gray-500">
                  {searchTerm ? '没有找到匹配的模板' : '开始创建您的第一个模板'}
                </p>
              </div>
            </Card>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredTemplates.map((template) => (
                <Card key={template.id} className="p-6">
                  <div className="flex justify-between items-start mb-4">
                    <div className="flex items-center space-x-2">
                      <Badge color={getTypeColor(template.type)}>
                        {getTypeLabel(template.type)}
                      </Badge>
                      {template.isDefault && (
                        <Badge color="yellow">默认</Badge>
                      )}
                    </div>
                    <div className="flex space-x-1">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleEditTemplate(template)}
                      >
                        <Edit className="w-4 h-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDeleteTemplate(template.id)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>

                  <div className="mb-4">
                    <h3 className="font-semibold text-gray-900 mb-2">{template.name}</h3>
                    <p className="text-sm text-gray-600">{template.description}</p>
                  </div>

                  <div className="space-y-2 mb-4">
                    <div className="flex items-center text-sm text-gray-500">
                      <Calendar className="w-4 h-4 mr-2" />
                      <span>更新于 {template.updatedAt.toLocaleDateString()}</span>
                    </div>
                    <div className="flex items-center text-sm text-gray-500">
                      <Users className="w-4 h-4 mr-2" />
                      <span>使用次数: {template.usageCount}</span>
                    </div>
                  </div>

                  <div className="flex space-x-2">
                    <Button 
                      variant="outline" 
                      size="sm" 
                      className="flex-1"
                      onClick={() => handleDownloadTemplate(template)}
                    >
                      <Download className="w-4 h-4 mr-2" />
                      下载
                    </Button>
                    <Button size="sm" className="flex-1">
                      使用模板
                    </Button>
                  </div>
                </Card>
              ))}
            </div>
          )}
        </div>
      )}

      {currentView === 'upload' && (
        <Card className="p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-6">上传模板</h2>
          <TemplateUpload
            onTemplateProcessed={handleTemplateProcessed}
            onCancel={() => setCurrentView('list')}
          />
        </Card>
      )}

      {currentView === 'create' && (
        <Card className="p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-6">创建新模板</h2>
          <TemplateEditor
            onSave={handleSaveTemplate}
            onCancel={() => setCurrentView('list')}
          />
        </Card>
      )}

      {currentView === 'edit' && editingTemplate && (
        <Card className="p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-6">编辑模板</h2>
          <TemplateEditor
            template={editingTemplate}
            onSave={handleSaveTemplate}
            onCancel={() => {
              setCurrentView('list')
              setEditingTemplate(null)
            }}
          />
        </Card>
      )}
    </div>
  )
}
