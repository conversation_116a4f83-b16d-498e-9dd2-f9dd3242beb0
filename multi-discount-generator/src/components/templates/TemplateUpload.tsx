'use client'

import { useState, useCallback } from 'react'
import { Card } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import { FileUpload } from '@/components/batch/FileUpload'
import { TemplateService } from '@/lib/template/TemplateService'
import { Download, Upload, CheckCircle, XCircle, AlertCircle } from 'lucide-react'

interface TemplateUploadProps {
  onTemplateProcessed: (data: {
    products: string[]
    configs: any[]
    templateData: any[]
  }) => void
  onCancel: () => void
}

export function TemplateUpload({ onTemplateProcessed, onCancel }: TemplateUploadProps) {
  const [step, setStep] = useState<'upload' | 'preview' | 'processing'>('upload')
  const [templateData, setTemplateData] = useState<any>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // 下载模板
  const downloadTemplate = (type: 'standard' | 'batch' = 'standard') => {
    try {
      if (type === 'standard') {
        TemplateService.exportTemplate()
      } else {
        // 创建批量模板
        const headers = [
          '商品ID',
          '活动类型',
          '时间模式',
          '开始日期',
          '结束日期',
          '每日开始时间',
          '每日结束时间',
          '活动区域',
          '满几件',
          '折扣',
          '周末全天',
          '节假日全天'
        ]
        
        const exampleData = [
          'PROD001',
          '多件多折',
          '连续',
          '2024-01-01',
          '2024-01-07',
          '09:00:00',
          '21:00:00',
          '全国',
          '2',
          '8.0',
          '是',
          '是'
        ]

        const csvContent = [headers, exampleData].map(row => row.join(',')).join('\n')
        const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' })
        const link = document.createElement('a')
        const url = URL.createObjectURL(blob)
        
        link.setAttribute('href', url)
        link.setAttribute('download', `批量活动模板_${new Date().toISOString().slice(0, 10)}.csv`)
        link.style.visibility = 'hidden'
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
      }
    } catch (error) {
      setError('下载模板失败')
    }
  }

  // 处理文件上传成功
  const handleUploadSuccess = useCallback(async (result: any) => {
    try {
      setLoading(true)
      setError(null)

      // 文件已经通过FileUpload组件上传到服务器并解析
      if (result.success) {
        setTemplateData(result)
        setStep('preview')
      } else {
        throw new Error(result.error || '解析模板失败')
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : '处理文件失败')
    } finally {
      setLoading(false)
    }
  }, [])

  // 处理上传错误
  const handleUploadError = useCallback((error: string) => {
    setError(error)
    setStep('upload')
  }, [])

  // 确认处理模板
  const handleConfirmProcess = async () => {
    if (!templateData) return

    try {
      setLoading(true)
      setStep('processing')

      // 模拟处理延迟
      await new Promise(resolve => setTimeout(resolve, 2000))

      // 生成配置数据
      const products = templateData.data.map((row: any) => row['商品ID']).filter(Boolean)
      const configs = templateData.data.map((row: any) => ({
        productId: row['商品ID'],
        activityType: row['活动类型'] || '多件多折',
        startTime: row['开始时间'],
        endTime: row['结束时间'],
        region: row['活动区域'] || '全国',
        minQuantity: parseInt(row['满几件']) || 1,
        discount: parseFloat(row['折扣']) || 8.0
      }))

      onTemplateProcessed({
        products,
        configs,
        templateData: templateData.data
      })

    } catch (error) {
      setError(error instanceof Error ? error.message : '处理模板失败')
      setStep('preview')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      {/* 步骤指示器 */}
      <div className="flex items-center justify-center space-x-4">
        {['upload', 'preview', 'processing'].map((stepName, index) => {
          const stepLabels = ['上传模板', '预览数据', '处理中']
          const isActive = step === stepName
          const isCompleted = ['upload', 'preview', 'processing'].indexOf(step) > index
          
          return (
            <div key={stepName} className="flex items-center">
              <div className={`
                w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium
                ${isActive ? 'bg-blue-600 text-white' : 
                  isCompleted ? 'bg-green-600 text-white' : 'bg-gray-200 text-gray-600'}
              `}>
                {isCompleted ? <CheckCircle className="w-4 h-4" /> : index + 1}
              </div>
              <span className={`ml-2 text-sm ${isActive ? 'text-blue-600 font-medium' : 'text-gray-500'}`}>
                {stepLabels[index]}
              </span>
              {index < stepLabels.length - 1 && (
                <div className={`w-8 h-0.5 mx-4 ${isCompleted ? 'bg-green-600' : 'bg-gray-200'}`} />
              )}
            </div>
          )
        })}
      </div>

      {error && (
        <Card className="p-4 bg-red-50 border-red-200">
          <div className="flex items-center space-x-2">
            <XCircle className="w-5 h-5 text-red-500" />
            <span className="text-red-800 font-medium">错误</span>
          </div>
          <p className="text-red-700 mt-1">{error}</p>
          <Button
            variant="outline"
            size="sm"
            className="mt-2"
            onClick={() => setError(null)}
          >
            关闭
          </Button>
        </Card>
      )}

      {/* 上传步骤 */}
      {step === 'upload' && (
        <div className="space-y-4">
          <Card className="p-6">
            <div className="text-center mb-6">
              <h3 className="text-lg font-medium text-gray-900 mb-2">上传活动模板文件</h3>
              <p className="text-gray-600">支持 Excel (.xlsx, .xls) 和 CSV 格式</p>
            </div>

            <FileUpload
              onSuccess={handleUploadSuccess}
              onError={handleUploadError}
              loading={loading}
              accept=".xlsx,.xls,.csv"
              uploadUrl="/api/templates/upload"
            />
          </Card>

          <Card className="p-4 bg-blue-50 border-blue-200">
            <div className="flex items-start space-x-3">
              <AlertCircle className="w-5 h-5 text-blue-500 mt-0.5" />
              <div>
                <h4 className="font-medium text-blue-900">模板说明</h4>
                <ul className="text-sm text-blue-800 mt-1 space-y-1">
                  <li>• 标准模板：包含具体的开始时间和结束时间</li>
                  <li>• 批量模板：包含日期范围和时间模式，系统自动生成时间段</li>
                  <li>• 商品ID为必填字段</li>
                  <li>• 时间格式：YYYY-MM-DD HH:mm:ss</li>
                </ul>
                <div className="flex space-x-2 mt-3">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => downloadTemplate('standard')}
                  >
                    <Download className="w-4 h-4 mr-2" />
                    标准模板
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => downloadTemplate('batch')}
                  >
                    <Download className="w-4 h-4 mr-2" />
                    批量模板
                  </Button>
                </div>
              </div>
            </div>
          </Card>
        </div>
      )}

      {/* 预览步骤 */}
      {step === 'preview' && templateData && (
        <Card className="p-6">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-medium text-gray-900">预览模板数据</h3>
            <div className="flex space-x-3">
              <Button
                variant="outline"
                onClick={() => setStep('upload')}
              >
                重新上传
              </Button>
              <Button
                onClick={handleConfirmProcess}
                disabled={loading}
              >
                <Upload className="w-4 h-4 mr-2" />
                确认处理
              </Button>
            </div>
          </div>
          
          {templateData.errors.length > 0 && (
            <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
              <h4 className="font-medium text-yellow-800">警告</h4>
              <ul className="text-sm text-yellow-700 mt-1">
                {templateData.errors.map((error: string, index: number) => (
                  <li key={index}>• {error}</li>
                ))}
              </ul>
            </div>
          )}

          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  {templateData.columns.map((column: string) => (
                    <th key={column} className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      {column}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {templateData.data.slice(0, 10).map((row: any, index: number) => (
                  <tr key={index}>
                    {templateData.columns.map((column: string) => (
                      <td key={column} className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {row[column] || '-'}
                      </td>
                    ))}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {templateData.data.length > 10 && (
            <div className="mt-4 text-center text-sm text-gray-500">
              显示前10条记录，共 {templateData.data.length} 条
            </div>
          )}
        </Card>
      )}

      {/* 处理中步骤 */}
      {step === 'processing' && (
        <Card className="p-8">
          <div className="text-center space-y-4">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto" />
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">正在处理模板数据</h3>
              <p className="text-gray-600">正在生成活动配置，请稍候...</p>
            </div>
          </div>
        </Card>
      )}

      {/* 底部操作 */}
      {step === 'upload' && (
        <div className="flex justify-end">
          <Button variant="outline" onClick={onCancel}>
            取消
          </Button>
        </div>
      )}
    </div>
  )
}
