import { NextRequest, NextResponse } from 'next/server'

export async function middleware(request: NextRequest) {
  // 简化的中间件，避免Edge Runtime兼容性问题
  // 只处理基本的请求路由，不进行复杂的系统初始化
  
  // 添加CORS头
  const response = NextResponse.next()
  
  // 允许跨域请求
  response.headers.set('Access-Control-Allow-Origin', '*')
  response.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
  response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization')
  
  // 处理预检请求
  if (request.method === 'OPTIONS') {
    return new Response(null, { status: 200, headers: response.headers })
  }
  
  return response
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!_next/static|_next/image|favicon.ico).*)',
  ],
}