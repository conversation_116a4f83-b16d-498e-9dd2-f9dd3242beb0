import { TestDataGenerator } from './TestDataGenerator'
import { TaskQueue } from '@/lib/batch/TaskQueue'
import { performanceMonitor } from '@/lib/performance/PerformanceMonitor'
import { healthCheckService } from '@/lib/monitoring/HealthCheckService'

interface StressTestConfig {
  initialLoad: number
  maxLoad: number
  stepSize: number
  stepDuration: number
  cooldownTime: number
  failureThreshold: number
}

interface StressTestResult {
  load: number
  duration: number
  successRate: number
  averageResponseTime: number
  throughput: number
  memoryUsage: number
  cpuUsage: number
  errors: string[]
  systemHealth: string
}

/**
 * 压力测试套件
 * 测试系统在极限负载下的表现
 */
export class StressTestSuite {
  private isRunning = false
  private currentLoad = 0
  private activeWorkers: Set<Promise<void>> = new Set()

  /**
   * 运行渐进式压力测试
   */
  async runProgressiveStressTest(config: StressTestConfig): Promise<{
    breakingPoint: number
    maxStableLoad: number
    results: StressTestResult[]
    recommendations: string[]
  }> {
    if (this.isRunning) {
      throw new Error('压力测试已在运行中')
    }

    this.isRunning = true
    const results: StressTestResult[] = []
    let breakingPoint = config.maxLoad
    let maxStableLoad = 0

    console.log('开始渐进式压力测试...', config)

    try {
      for (let load = config.initialLoad; load <= config.maxLoad; load += config.stepSize) {
        console.log(`测试负载级别: ${load}`)
        
        const result = await this.runStressTestStep(load, config.stepDuration)
        results.push(result)

        // 检查是否达到失败阈值
        if (result.successRate < (1 - config.failureThreshold)) {
          console.log(`负载 ${load} 时成功率降至 ${(result.successRate * 100).toFixed(1)}%，达到失败阈值`)
          breakingPoint = load
          break
        }

        // 更新最大稳定负载
        if (result.successRate >= 0.95 && result.averageResponseTime < 2000) {
          maxStableLoad = load
        }

        // 系统健康检查
        if (result.systemHealth === 'critical') {
          console.log(`负载 ${load} 时系统健康状态为严重，停止测试`)
          breakingPoint = load
          break
        }

        // 冷却时间
        if (config.cooldownTime > 0) {
          console.log(`冷却 ${config.cooldownTime}ms...`)
          await new Promise(resolve => setTimeout(resolve, config.cooldownTime))
        }
      }

      const recommendations = this.generateRecommendations(results, maxStableLoad, breakingPoint)

      return {
        breakingPoint,
        maxStableLoad,
        results,
        recommendations
      }

    } finally {
      this.isRunning = false
      await this.cleanup()
    }
  }

  /**
   * 运行峰值负载测试
   */
  async runPeakLoadTest(targetLoad: number, duration: number): Promise<{
    sustained: boolean
    peakThroughput: number
    averageResponseTime: number
    errorRate: number
    resourceUtilization: {
      memory: number
      cpu: number
    }
    degradationPoints: Array<{
      timestamp: number
      metric: string
      value: number
    }>
  }> {
    console.log(`开始峰值负载测试: ${targetLoad} 并发，持续 ${duration}ms`)

    const startTime = Date.now()
    const endTime = startTime + duration
    const metrics: Array<{
      timestamp: number
      throughput: number
      responseTime: number
      errorRate: number
      memory: number
      cpu: number
    }> = []

    let totalRequests = 0
    let successfulRequests = 0
    let responseTimes: number[] = []
    const degradationPoints: Array<{
      timestamp: number
      metric: string
      value: number
    }> = []

    // 启动负载生成器
    const workers: Promise<void>[] = []
    for (let i = 0; i < targetLoad; i++) {
      const worker = this.createPeakLoadWorker(
        endTime,
        (success, responseTime) => {
          totalRequests++
          if (success) {
            successfulRequests++
            responseTimes.push(responseTime)
          }
        }
      )
      workers.push(worker)
      this.activeWorkers.add(worker)
    }

    // 监控系统指标
    const monitorInterval = setInterval(() => {
      const now = Date.now()
      const memoryUsage = process.memoryUsage().heapUsed / 1024 / 1024
      const currentThroughput = responseTimes.length > 0 ? responseTimes.length / ((now - startTime) / 1000) : 0
      const currentResponseTime = responseTimes.length > 0 
        ? responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length 
        : 0
      const currentErrorRate = totalRequests > 0 ? (totalRequests - successfulRequests) / totalRequests : 0

      metrics.push({
        timestamp: now,
        throughput: currentThroughput,
        responseTime: currentResponseTime,
        errorRate: currentErrorRate,
        memory: memoryUsage,
        cpu: 0 // 简化实现
      })

      // 检测性能降级
      if (currentResponseTime > 5000) {
        degradationPoints.push({
          timestamp: now,
          metric: 'response_time',
          value: currentResponseTime
        })
      }

      if (currentErrorRate > 0.1) {
        degradationPoints.push({
          timestamp: now,
          metric: 'error_rate',
          value: currentErrorRate
        })
      }

      if (memoryUsage > 1000) { // 1GB
        degradationPoints.push({
          timestamp: now,
          metric: 'memory_usage',
          value: memoryUsage
        })
      }
    }, 1000)

    // 等待测试完成
    await Promise.all(workers)
    clearInterval(monitorInterval)

    const actualDuration = Date.now() - startTime
    const peakThroughput = Math.max(...metrics.map(m => m.throughput))
    const averageResponseTime = responseTimes.length > 0 
      ? responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length 
      : 0
    const errorRate = totalRequests > 0 ? (totalRequests - successfulRequests) / totalRequests : 0
    const sustained = errorRate < 0.05 && averageResponseTime < 3000

    return {
      sustained,
      peakThroughput,
      averageResponseTime,
      errorRate,
      resourceUtilization: {
        memory: Math.max(...metrics.map(m => m.memory)),
        cpu: Math.max(...metrics.map(m => m.cpu))
      },
      degradationPoints
    }
  }

  /**
   * 运行内存压力测试
   */
  async runMemoryStressTest(targetMemoryMB: number): Promise<{
    success: boolean
    peakMemoryUsage: number
    memoryLeakDetected: boolean
    gcEfficiency: number
    timeToTarget: number
  }> {
    console.log(`开始内存压力测试，目标: ${targetMemoryMB}MB`)

    const startTime = Date.now()
    const initialMemory = process.memoryUsage().heapUsed / 1024 / 1024
    const targetMemoryBytes = targetMemoryMB * 1024 * 1024
    
    const memoryConsumers: any[] = []
    let peakMemoryUsage = initialMemory
    let timeToTarget = 0

    try {
      // 逐步增加内存使用
      while (process.memoryUsage().heapUsed < targetMemoryBytes) {
        // 创建大量对象消耗内存
        const chunk = new Array(10000).fill(0).map(() => ({
          id: Math.random().toString(36),
          data: new Array(100).fill(Math.random()),
          timestamp: new Date(),
          metadata: {
            created: Date.now(),
            random: Math.random()
          }
        }))
        
        memoryConsumers.push(chunk)
        
        const currentMemory = process.memoryUsage().heapUsed / 1024 / 1024
        if (currentMemory > peakMemoryUsage) {
          peakMemoryUsage = currentMemory
        }

        if (timeToTarget === 0 && currentMemory >= targetMemoryMB) {
          timeToTarget = Date.now() - startTime
        }

        // 防止无限循环
        if (Date.now() - startTime > 30000) { // 30秒超时
          break
        }

        await new Promise(resolve => setTimeout(resolve, 100))
      }

      // 测试垃圾回收效率
      const beforeGC = process.memoryUsage().heapUsed / 1024 / 1024
      
      // 清理一半内存
      memoryConsumers.splice(0, Math.floor(memoryConsumers.length / 2))
      
      // 强制垃圾回收
      if (global.gc) {
        global.gc()
      }
      
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      const afterGC = process.memoryUsage().heapUsed / 1024 / 1024
      const gcEfficiency = (beforeGC - afterGC) / beforeGC

      // 检测内存泄漏
      const memoryLeakDetected = afterGC > initialMemory * 2

      return {
        success: peakMemoryUsage >= targetMemoryMB * 0.9,
        peakMemoryUsage,
        memoryLeakDetected,
        gcEfficiency,
        timeToTarget
      }

    } finally {
      // 清理内存
      memoryConsumers.length = 0
      if (global.gc) {
        global.gc()
      }
    }
  }

  /**
   * 运行并发连接压力测试
   */
  async runConcurrencyStressTest(maxConnections: number): Promise<{
    maxSustainedConnections: number
    connectionFailurePoint: number
    averageConnectionTime: number
    resourceExhaustion: boolean
  }> {
    console.log(`开始并发连接压力测试，最大连接数: ${maxConnections}`)

    let maxSustainedConnections = 0
    let connectionFailurePoint = maxConnections
    const connectionTimes: number[] = []
    let resourceExhaustion = false

    for (let connections = 10; connections <= maxConnections; connections += 10) {
      console.log(`测试 ${connections} 个并发连接...`)

      const startTime = Date.now()
      const promises: Promise<void>[] = []
      let successfulConnections = 0

      for (let i = 0; i < connections; i++) {
        const promise = this.simulateConnection()
          .then(() => {
            successfulConnections++
            connectionTimes.push(Date.now() - startTime)
          })
          .catch(() => {
            // 连接失败
          })
        
        promises.push(promise)
      }

      await Promise.all(promises)

      const successRate = successfulConnections / connections
      
      if (successRate >= 0.95) {
        maxSustainedConnections = connections
      } else {
        connectionFailurePoint = connections
        break
      }

      // 检查资源耗尽
      const memoryUsage = process.memoryUsage().heapUsed / 1024 / 1024
      if (memoryUsage > 500) { // 500MB
        resourceExhaustion = true
        break
      }

      // 短暂休息
      await new Promise(resolve => setTimeout(resolve, 1000))
    }

    const averageConnectionTime = connectionTimes.length > 0
      ? connectionTimes.reduce((sum, time) => sum + time, 0) / connectionTimes.length
      : 0

    return {
      maxSustainedConnections,
      connectionFailurePoint,
      averageConnectionTime,
      resourceExhaustion
    }
  }

  // 私有方法

  private async runStressTestStep(load: number, duration: number): Promise<StressTestResult> {
    const startTime = Date.now()
    const endTime = startTime + duration
    
    let totalRequests = 0
    let successfulRequests = 0
    const responseTimes: number[] = []
    const errors: string[] = []

    // 启动工作线程
    const workers: Promise<void>[] = []
    for (let i = 0; i < load; i++) {
      const worker = this.createStressTestWorker(
        endTime,
        (success, responseTime, error) => {
          totalRequests++
          if (success) {
            successfulRequests++
            responseTimes.push(responseTime)
          } else if (error) {
            errors.push(error)
          }
        }
      )
      workers.push(worker)
      this.activeWorkers.add(worker)
    }

    // 监控系统资源
    const initialMemory = process.memoryUsage().heapUsed
    let peakMemory = initialMemory
    
    const monitorInterval = setInterval(() => {
      const currentMemory = process.memoryUsage().heapUsed
      if (currentMemory > peakMemory) {
        peakMemory = currentMemory
      }
    }, 500)

    // 等待所有工作线程完成
    await Promise.all(workers)
    clearInterval(monitorInterval)

    // 清理工作线程引用
    workers.forEach(worker => this.activeWorkers.delete(worker))

    const actualDuration = Date.now() - startTime
    const successRate = totalRequests > 0 ? successfulRequests / totalRequests : 0
    const averageResponseTime = responseTimes.length > 0 
      ? responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length 
      : 0
    const throughput = (successfulRequests / actualDuration) * 1000

    // 获取系统健康状态
    const healthCheck = await healthCheckService.performHealthCheck()

    return {
      load,
      duration: actualDuration,
      successRate,
      averageResponseTime,
      throughput,
      memoryUsage: Math.round(peakMemory / 1024 / 1024),
      cpuUsage: 0, // 简化实现
      errors: [...new Set(errors)].slice(0, 5), // 去重并限制数量
      systemHealth: healthCheck.overall
    }
  }

  private async createStressTestWorker(
    endTime: number,
    callback: (success: boolean, responseTime: number, error?: string) => void
  ): Promise<void> {
    while (Date.now() < endTime) {
      const requestStart = Date.now()
      
      try {
        await this.simulateStressfulOperation()
        const responseTime = Date.now() - requestStart
        callback(true, responseTime)
      } catch (error) {
        const responseTime = Date.now() - requestStart
        callback(false, responseTime, error.message)
      }

      // 短暂间隔
      await new Promise(resolve => setTimeout(resolve, Math.random() * 100))
    }
  }

  private async createPeakLoadWorker(
    endTime: number,
    callback: (success: boolean, responseTime: number) => void
  ): Promise<void> {
    while (Date.now() < endTime) {
      const requestStart = Date.now()
      
      try {
        await this.simulateStressfulOperation()
        const responseTime = Date.now() - requestStart
        callback(true, responseTime)
      } catch (error) {
        const responseTime = Date.now() - requestStart
        callback(false, responseTime)
      }

      // 高频率请求
      await new Promise(resolve => setTimeout(resolve, 10))
    }
  }

  private async simulateStressfulOperation(): Promise<void> {
    // 模拟CPU密集型操作
    const iterations = Math.floor(Math.random() * 1000) + 500
    for (let i = 0; i < iterations; i++) {
      Math.sqrt(Math.random() * 1000)
    }

    // 模拟异步操作
    const delay = Math.random() * 50 + 10 // 10-60ms
    await new Promise(resolve => setTimeout(resolve, delay))

    // 模拟内存分配
    const tempData = new Array(Math.floor(Math.random() * 100) + 50)
      .fill(0)
      .map(() => Math.random())

    // 10%概率模拟错误
    if (Math.random() < 0.1) {
      throw new Error('模拟压力测试错误')
    }

    // 清理临时数据
    tempData.length = 0
  }

  private async simulateConnection(): Promise<void> {
    // 模拟连接建立时间
    const connectionTime = Math.random() * 100 + 50
    await new Promise(resolve => setTimeout(resolve, connectionTime))

    // 模拟连接保持
    const holdTime = Math.random() * 1000 + 500
    await new Promise(resolve => setTimeout(resolve, holdTime))

    // 5%概率连接失败
    if (Math.random() < 0.05) {
      throw new Error('连接失败')
    }
  }

  private generateRecommendations(
    results: StressTestResult[],
    maxStableLoad: number,
    breakingPoint: number
  ): string[] {
    const recommendations: string[] = []

    // 基于最大稳定负载的建议
    if (maxStableLoad < 50) {
      recommendations.push('系统并发处理能力较低，建议优化任务处理逻辑和数据库查询')
    } else if (maxStableLoad < 100) {
      recommendations.push('系统并发处理能力中等，可考虑增加缓存和优化资源使用')
    } else {
      recommendations.push('系统并发处理能力良好')
    }

    // 基于内存使用的建议
    const maxMemoryUsage = Math.max(...results.map(r => r.memoryUsage))
    if (maxMemoryUsage > 500) {
      recommendations.push('内存使用量较高，建议优化内存管理和实现对象池')
    }

    // 基于响应时间的建议
    const avgResponseTime = results.reduce((sum, r) => sum + r.averageResponseTime, 0) / results.length
    if (avgResponseTime > 2000) {
      recommendations.push('平均响应时间较长，建议优化算法和减少I/O操作')
    }

    // 基于错误率的建议
    const hasHighErrorRate = results.some(r => r.successRate < 0.9)
    if (hasHighErrorRate) {
      recommendations.push('在高负载下错误率较高，建议增强错误处理和重试机制')
    }

    // 基于系统健康的建议
    const hasCriticalHealth = results.some(r => r.systemHealth === 'critical')
    if (hasCriticalHealth) {
      recommendations.push('系统在高负载下健康状态不佳，建议增加监控和告警')
    }

    return recommendations
  }

  private async cleanup(): Promise<void> {
    // 等待所有活跃的工作线程完成
    if (this.activeWorkers.size > 0) {
      console.log(`等待 ${this.activeWorkers.size} 个工作线程完成...`)
      await Promise.all(Array.from(this.activeWorkers))
    }

    this.activeWorkers.clear()
    this.currentLoad = 0

    // 强制垃圾回收
    if (global.gc) {
      global.gc()
    }

    console.log('压力测试清理完成')
  }
}

// 导出压力测试运行器
export async function runStressTests(): Promise<void> {
  const testSuite = new StressTestSuite()
  
  console.log('开始压力测试...')
  
  try {
    // 运行渐进式压力测试
    const progressiveResult = await testSuite.runProgressiveStressTest({
      initialLoad: 10,
      maxLoad: 200,
      stepSize: 20,
      stepDuration: 30000, // 30秒
      cooldownTime: 5000,  // 5秒
      failureThreshold: 0.1 // 10%失败率
    })

    console.log('\n渐进式压力测试结果:')
    console.log(`最大稳定负载: ${progressiveResult.maxStableLoad}`)
    console.log(`系统极限: ${progressiveResult.breakingPoint}`)
    console.log('建议:')
    progressiveResult.recommendations.forEach(rec => console.log(`- ${rec}`))

    // 运行内存压力测试
    const memoryResult = await testSuite.runMemoryStressTest(512) // 512MB

    console.log('\n内存压力测试结果:')
    console.log(`峰值内存使用: ${memoryResult.peakMemoryUsage.toFixed(1)}MB`)
    console.log(`GC效率: ${(memoryResult.gcEfficiency * 100).toFixed(1)}%`)
    console.log(`内存泄漏检测: ${memoryResult.memoryLeakDetected ? '是' : '否'}`)

  } catch (error) {
    console.error('压力测试失败:', error)
  }
}