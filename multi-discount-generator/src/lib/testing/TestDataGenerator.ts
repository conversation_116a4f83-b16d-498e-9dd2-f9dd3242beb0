import { faker } from '@faker-js/faker'
import type { FormData } from '@/types'
import type { CreateTaskInput } from '@/types/batch'
import { TaskType } from '@prisma/client'

/**
 * 测试数据生成器
 * 用于生成各种测试场景的数据
 */
export class TestDataGenerator {
  /**
   * 生成随机商品ID列表
   */
  static generateProductIds(count: number = 10): string[] {
    const productIds: string[] = []
    for (let i = 0; i < count; i++) {
      productIds.push(`PROD${faker.string.alphanumeric(8).toUpperCase()}`)
    }
    return productIds
  }

  /**
   * 生成测试表单数据
   */
  static generateFormData(overrides: Partial<FormData> = {}): FormData {
    const baseData: FormData = {
      productId: faker.string.alphanumeric(10).toUpperCase(),
      region: {
        type: faker.helpers.arrayElement(['national', 'regional']) as 'national' | 'regional',
        regions: faker.helpers.arrayElements(['华东', '华南', '华中', '华北', '西南'], { min: 1, max: 3 })
      },
      minQuantity: faker.helpers.arrayElement([1, 2, 3, 5]),
      discount: faker.helpers.arrayElement([6.0, 7.0, 7.5, 8.0, 8.5, 9.0]),
      timeMode: faker.helpers.arrayElement(['continuous', 'random']) as 'continuous' | 'random',
      startDate: faker.date.future(),
      startTime: faker.date.future(),
      endTime: faker.date.future(),
      days: faker.number.int({ min: 1, max: 30 }),
      weekendFullDay: faker.datatype.boolean(),
      randomStartTime: faker.date.future(),
      timeSlotCount: faker.number.int({ min: 1, max: 10 }),
      slotDuration: faker.number.int({ min: 1, max: 24 })
    }

    return { ...baseData, ...overrides }
  }

  /**
   * 生成测试任务输入
   */
  static generateTaskInput(overrides: Partial<CreateTaskInput> = {}): CreateTaskInput {
    const baseInput: CreateTaskInput = {
      name: `测试任务_${faker.string.alphanumeric(6)}`,
      type: faker.helpers.arrayElement([TaskType.SINGLE, TaskType.BATCH]),
      config: this.generateFormData(),
      products: this.generateProductIds(faker.number.int({ min: 5, max: 50 })),
      priority: faker.number.int({ min: 0, max: 5 })
    }

    return { ...baseInput, ...overrides }
  }

  /**
   * 生成CSV格式的商品数据
   */
  static generateCSVData(productIds: string[]): string {
    const headers = ['商品ID', '商品名称', '分类']
    const rows = productIds.map(id => [
      id,
      faker.commerce.productName(),
      faker.commerce.department()
    ])

    return [headers, ...rows]
      .map(row => row.join(','))
      .join('\n')
  }

  /**
   * 生成Excel测试数据
   */
  static generateExcelTestData(productCount: number = 20): {
    productIds: string[]
    csvContent: string
    expectedResults: {
      validCount: number
      duplicateCount: number
      invalidCount: number
    }
  } {
    const validIds = this.generateProductIds(productCount)
    const duplicateIds = validIds.slice(0, 3) // 添加一些重复ID
    const invalidIds = ['', 'INVALID@ID', '123-456-789-TOOLONG-ID-THAT-EXCEEDS-LIMIT']
    
    const allIds = [...validIds, ...duplicateIds, ...invalidIds]
    const shuffledIds = faker.helpers.shuffle(allIds)
    
    const csvContent = this.generateCSVData(shuffledIds)
    
    return {
      productIds: shuffledIds,
      csvContent,
      expectedResults: {
        validCount: validIds.length,
        duplicateCount: duplicateIds.length,
        invalidCount: invalidIds.filter(id => id.length > 0).length
      }
    }
  }

  /**
   * 生成性能测试数据
   */
  static generatePerformanceTestData(size: 'small' | 'medium' | 'large' | 'xlarge'): {
    productIds: string[]
    expectedProcessingTime: number
    memoryEstimate: number
  } {
    const sizeConfig = {
      small: { count: 10, time: 5000, memory: 50 },
      medium: { count: 100, time: 30000, memory: 200 },
      large: { count: 500, time: 120000, memory: 800 },
      xlarge: { count: 1000, time: 300000, memory: 1500 }
    }

    const config = sizeConfig[size]
    const productIds = this.generateProductIds(config.count)

    return {
      productIds,
      expectedProcessingTime: config.time,
      memoryEstimate: config.memory * 1024 * 1024 // 转换为字节
    }
  }

  /**
   * 生成错误场景测试数据
   */
  static generateErrorScenarios(): Array<{
    name: string
    description: string
    data: any
    expectedError: string
  }> {
    return [
      {
        name: 'empty_product_list',
        description: '空商品列表',
        data: {
          ...this.generateTaskInput(),
          products: []
        },
        expectedError: '商品列表不能为空'
      },
      {
        name: 'invalid_product_ids',
        description: '无效商品ID格式',
        data: {
          ...this.generateTaskInput(),
          products: ['', 'invalid@id', '123-456-789-TOOLONG']
        },
        expectedError: '发现格式不正确的商品ID'
      },
      {
        name: 'too_many_products',
        description: '商品数量超限',
        data: {
          ...this.generateTaskInput(),
          products: this.generateProductIds(1001)
        },
        expectedError: '商品数量不能超过1000个'
      },
      {
        name: 'invalid_config',
        description: '无效配置参数',
        data: {
          ...this.generateTaskInput(),
          config: {
            ...this.generateFormData(),
            discount: 15.0, // 超出有效范围
            minQuantity: 0 // 无效数量
          }
        },
        expectedError: '配置参数无效'
      }
    ]
  }

  /**
   * 生成并发测试场景
   */
  static generateConcurrencyTestData(concurrentTasks: number = 5): Array<{
    taskInput: CreateTaskInput
    delay: number
  }> {
    const tasks: Array<{ taskInput: CreateTaskInput; delay: number }> = []
    
    for (let i = 0; i < concurrentTasks; i++) {
      tasks.push({
        taskInput: {
          ...this.generateTaskInput(),
          name: `并发测试任务_${i + 1}`,
          priority: faker.number.int({ min: 0, max: 2 })
        },
        delay: faker.number.int({ min: 0, max: 1000 }) // 随机延迟0-1秒
      })
    }
    
    return tasks
  }

  /**
   * 生成负载测试数据
   */
  static generateLoadTestData(duration: number = 60000): {
    tasks: CreateTaskInput[]
    schedule: Array<{
      timestamp: number
      taskIndex: number
    }>
  } {
    const taskCount = Math.floor(duration / 5000) // 每5秒一个任务
    const tasks = Array.from({ length: taskCount }, (_, i) => ({
      ...this.generateTaskInput(),
      name: `负载测试任务_${i + 1}`,
      priority: faker.number.int({ min: 0, max: 3 })
    }))

    const schedule = tasks.map((_, index) => ({
      timestamp: index * 5000, // 每5秒执行一个任务
      taskIndex: index
    }))

    return { tasks, schedule }
  }

  /**
   * 生成系统监控测试数据
   */
  static generateMonitoringTestData(): {
    metrics: Array<{
      name: string
      value: number
      unit: string
      timestamp: Date
      tags?: Record<string, any>
    }>
    alerts: Array<{
      level: 'info' | 'warning' | 'error'
      message: string
      timestamp: Date
      source: string
    }>
  } {
    const metrics = [
      {
        name: 'cpu_usage',
        value: faker.number.float({ min: 10, max: 90 }),
        unit: 'percent',
        timestamp: faker.date.recent(),
        tags: { host: 'server-01' }
      },
      {
        name: 'memory_usage',
        value: faker.number.float({ min: 200, max: 800 }),
        unit: 'MB',
        timestamp: faker.date.recent(),
        tags: { host: 'server-01' }
      },
      {
        name: 'task_queue_size',
        value: faker.number.int({ min: 0, max: 50 }),
        unit: 'count',
        timestamp: faker.date.recent()
      },
      {
        name: 'active_tasks',
        value: faker.number.int({ min: 0, max: 10 }),
        unit: 'count',
        timestamp: faker.date.recent()
      },
      {
        name: 'db_query_duration',
        value: faker.number.float({ min: 10, max: 500 }),
        unit: 'ms',
        timestamp: faker.date.recent(),
        tags: { query: 'SELECT' }
      }
    ]

    const alerts = [
      {
        level: 'warning' as const,
        message: 'CPU使用率超过80%',
        timestamp: faker.date.recent(),
        source: 'system_monitor'
      },
      {
        level: 'error' as const,
        message: '任务执行失败',
        timestamp: faker.date.recent(),
        source: 'task_executor'
      },
      {
        level: 'info' as const,
        message: '系统启动完成',
        timestamp: faker.date.recent(),
        source: 'application'
      }
    ]

    return { metrics, alerts }
  }

  /**
   * 生成数据库测试数据
   */
  static generateDatabaseTestData(): {
    tasks: any[]
    logs: any[]
    files: any[]
  } {
    const tasks = Array.from({ length: 10 }, (_, i) => ({
      id: `task_${i + 1}`,
      name: `测试任务_${i + 1}`,
      type: faker.helpers.arrayElement(['SINGLE', 'BATCH']),
      status: faker.helpers.arrayElement(['PENDING', 'RUNNING', 'COMPLETED', 'FAILED']),
      priority: faker.number.int({ min: 0, max: 5 }),
      config: this.generateFormData(),
      createdAt: faker.date.recent(),
      updatedAt: faker.date.recent()
    }))

    const logs = Array.from({ length: 50 }, (_, i) => ({
      id: `log_${i + 1}`,
      taskId: faker.helpers.arrayElement(tasks).id,
      level: faker.helpers.arrayElement(['INFO', 'WARN', 'ERROR']),
      message: faker.lorem.sentence(),
      timestamp: faker.date.recent(),
      metadata: {
        source: faker.helpers.arrayElement(['task_executor', 'file_processor', 'excel_generator']),
        duration: faker.number.int({ min: 100, max: 5000 })
      }
    }))

    const files = Array.from({ length: 20 }, (_, i) => ({
      id: `file_${i + 1}`,
      filename: `${faker.system.fileName()}.xlsx`,
      originalName: `${faker.commerce.productName()}.xlsx`,
      size: faker.number.int({ min: 1024, max: 10485760 }), // 1KB - 10MB
      mimeType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      path: `/uploads/${faker.string.uuid()}.xlsx`,
      createdAt: faker.date.recent(),
      taskId: faker.helpers.arrayElement(tasks).id
    }))

    return { tasks, logs, files }
  }

  /**
   * 清理测试数据
   */
  static cleanup(): void {
    // 重置faker种子以确保测试的可重复性
    faker.seed(12345)
  }
}

/**
 * 测试断言工具
 */
export class TestAssertions {
  /**
   * 断言任务状态
   */
  static assertTaskStatus(task: any, expectedStatus: string): void {
    if (task.status !== expectedStatus) {
      throw new Error(`期望任务状态为 ${expectedStatus}，实际为 ${task.status}`)
    }
  }

  /**
   * 断言任务进度
   */
  static assertTaskProgress(task: any, minProgress: number, maxProgress: number = 100): void {
    if (task.progress < minProgress || task.progress > maxProgress) {
      throw new Error(`任务进度 ${task.progress} 不在期望范围 [${minProgress}, ${maxProgress}] 内`)
    }
  }

  /**
   * 断言文件存在
   */
  static assertFileExists(filePath: string): void {
    const fs = require('fs')
    if (!fs.existsSync(filePath)) {
      throw new Error(`文件不存在: ${filePath}`)
    }
  }

  /**
   * 断言响应时间
   */
  static assertResponseTime(actualTime: number, maxTime: number): void {
    if (actualTime > maxTime) {
      throw new Error(`响应时间 ${actualTime}ms 超过最大允许时间 ${maxTime}ms`)
    }
  }

  /**
   * 断言内存使用
   */
  static assertMemoryUsage(actualMemory: number, maxMemory: number): void {
    if (actualMemory > maxMemory) {
      throw new Error(`内存使用 ${actualMemory}MB 超过最大允许值 ${maxMemory}MB`)
    }
  }

  /**
   * 断言数组长度
   */
  static assertArrayLength(array: any[], expectedLength: number): void {
    if (array.length !== expectedLength) {
      throw new Error(`数组长度期望为 ${expectedLength}，实际为 ${array.length}`)
    }
  }

  /**
   * 断言对象包含属性
   */
  static assertObjectHasProperty(obj: any, property: string): void {
    if (!(property in obj)) {
      throw new Error(`对象缺少属性: ${property}`)
    }
  }

  /**
   * 断言错误类型
   */
  static assertErrorType(error: any, expectedType: string): void {
    if (error.constructor.name !== expectedType) {
      throw new Error(`期望错误类型为 ${expectedType}，实际为 ${error.constructor.name}`)
    }
  }
}