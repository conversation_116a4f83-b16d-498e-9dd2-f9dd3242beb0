import { TestDataGenerator } from './TestDataGenerator'
import { TaskQueue } from '@/lib/batch/TaskQueue'
import { FileUploadService } from '@/lib/upload/FileUploadService'
import { BatchExcelService } from '@/lib/batch/BatchExcelService'
import { performanceMonitor } from '@/lib/performance/PerformanceMonitor'
import { resourceManager } from '@/lib/performance/ResourceManager'

interface PerformanceTestResult {
  testName: string
  duration: number
  throughput: number
  memoryUsage: {
    before: number
    after: number
    peak: number
  }
  cpuUsage: {
    average: number
    peak: number
  }
  success: boolean
  errors: string[]
  metrics: Record<string, number>
}

interface LoadTestConfig {
  concurrency: number
  duration: number
  rampUpTime: number
  targetThroughput: number
}

/**
 * 性能测试套件
 * 测试系统在各种负载下的性能表现
 */
export class PerformanceTestSuite {
  private testResults: PerformanceTestResult[] = []
  private isRunning = false

  /**
   * 运行所有性能测试
   */
  async runAllPerformanceTests(): Promise<{
    totalTests: number
    passed: number
    failed: number
    results: PerformanceTestResult[]
    summary: {
      averageDuration: number
      totalThroughput: number
      memoryEfficiency: number
      cpuEfficiency: number
    }
  }> {
    if (this.isRunning) {
      throw new Error('性能测试已在运行中')
    }

    this.isRunning = true
    this.testResults = []

    console.log('开始运行性能测试套件...')

    try {
      // 预热系统
      await this.warmupSystem()

      // 运行各种性能测试
      const tests = [
        { name: '单任务处理性能测试', fn: this.testSingleTaskPerformance },
        { name: '批量任务处理性能测试', fn: this.testBatchTaskPerformance },
        { name: '文件上传性能测试', fn: this.testFileUploadPerformance },
        { name: 'Excel生成性能测试', fn: this.testExcelGenerationPerformance },
        { name: '并发任务处理测试', fn: this.testConcurrentTaskProcessing },
        { name: '内存使用效率测试', fn: this.testMemoryEfficiency },
        { name: '数据库查询性能测试', fn: this.testDatabaseQueryPerformance },
        { name: '系统负载测试', fn: this.testSystemLoad }
      ]

      for (const test of tests) {
        try {
          console.log(`运行测试: ${test.name}`)
          const result = await this.runPerformanceTest(test.name, test.fn.bind(this))
          this.testResults.push(result)
          
          // 测试间隔，让系统恢复
          await this.waitForSystemRecovery()
        } catch (error) {
          console.error(`测试失败: ${test.name}`, error)
          this.testResults.push({
            testName: test.name,
            duration: 0,
            throughput: 0,
            memoryUsage: { before: 0, after: 0, peak: 0 },
            cpuUsage: { average: 0, peak: 0 },
            success: false,
            errors: [error.message],
            metrics: {}
          })
        }
      }

      const summary = this.generatePerformanceSummary()
      console.log('性能测试完成:', summary)

      return {
        totalTests: this.testResults.length,
        passed: this.testResults.filter(r => r.success).length,
        failed: this.testResults.filter(r => !r.success).length,
        results: this.testResults,
        summary
      }

    } finally {
      this.isRunning = false
    }
  }

  /**
   * 运行负载测试
   */
  async runLoadTest(config: LoadTestConfig): Promise<{
    success: boolean
    duration: number
    totalRequests: number
    successfulRequests: number
    failedRequests: number
    averageResponseTime: number
    throughput: number
    errors: string[]
  }> {
    console.log('开始负载测试...', config)

    const startTime = Date.now()
    let totalRequests = 0
    let successfulRequests = 0
    let failedRequests = 0
    const responseTimes: number[] = []
    const errors: string[] = []

    // 创建并发任务
    const workers: Promise<void>[] = []
    const endTime = startTime + config.duration

    for (let i = 0; i < config.concurrency; i++) {
      const worker = this.createLoadTestWorker(
        i,
        endTime,
        config.targetThroughput / config.concurrency,
        (success, responseTime, error) => {
          totalRequests++
          if (success) {
            successfulRequests++
            responseTimes.push(responseTime)
          } else {
            failedRequests++
            if (error) errors.push(error)
          }
        }
      )
      workers.push(worker)

      // 渐进式启动
      if (config.rampUpTime > 0) {
        await new Promise(resolve => 
          setTimeout(resolve, config.rampUpTime / config.concurrency)
        )
      }
    }

    // 等待所有工作线程完成
    await Promise.all(workers)

    const actualDuration = Date.now() - startTime
    const averageResponseTime = responseTimes.length > 0 
      ? responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length 
      : 0
    const throughput = (successfulRequests / actualDuration) * 1000 // 每秒请求数

    return {
      success: failedRequests / totalRequests < 0.05, // 错误率小于5%认为成功
      duration: actualDuration,
      totalRequests,
      successfulRequests,
      failedRequests,
      averageResponseTime,
      throughput,
      errors: [...new Set(errors)].slice(0, 10) // 去重并限制错误数量
    }
  }

  /**
   * 运行压力测试
   */
  async runStressTest(maxConcurrency: number = 100): Promise<{
    breakingPoint: number
    maxThroughput: number
    results: Array<{
      concurrency: number
      throughput: number
      errorRate: number
      averageResponseTime: number
    }>
  }> {
    console.log('开始压力测试，寻找系统极限...')

    const results: Array<{
      concurrency: number
      throughput: number
      errorRate: number
      averageResponseTime: number
    }> = []

    let breakingPoint = maxConcurrency
    let maxThroughput = 0

    // 逐步增加并发数
    for (let concurrency = 1; concurrency <= maxConcurrency; concurrency *= 2) {
      console.log(`测试并发数: ${concurrency}`)

      const loadTestResult = await this.runLoadTest({
        concurrency,
        duration: 30000, // 30秒
        rampUpTime: 5000, // 5秒渐进
        targetThroughput: concurrency * 10 // 目标吞吐量
      })

      const errorRate = loadTestResult.failedRequests / loadTestResult.totalRequests
      
      results.push({
        concurrency,
        throughput: loadTestResult.throughput,
        errorRate,
        averageResponseTime: loadTestResult.averageResponseTime
      })

      if (loadTestResult.throughput > maxThroughput) {
        maxThroughput = loadTestResult.throughput
      }

      // 如果错误率超过10%或响应时间超过5秒，认为达到极限
      if (errorRate > 0.1 || loadTestResult.averageResponseTime > 5000) {
        breakingPoint = concurrency
        break
      }

      // 让系统恢复
      await new Promise(resolve => setTimeout(resolve, 10000))
    }

    return {
      breakingPoint,
      maxThroughput,
      results
    }
  }

  // 私有方法

  private async runPerformanceTest(
    testName: string,
    testFn: () => Promise<void>
  ): Promise<PerformanceTestResult> {
    const startTime = Date.now()
    const beforeMemory = process.memoryUsage().heapUsed
    const beforeCpu = process.cpuUsage()
    
    let peakMemory = beforeMemory
    let cpuSamples: number[] = []
    
    // 监控资源使用
    const monitorInterval = setInterval(() => {
      const currentMemory = process.memoryUsage().heapUsed
      if (currentMemory > peakMemory) {
        peakMemory = currentMemory
      }
      
      const currentCpu = process.cpuUsage(beforeCpu)
      cpuSamples.push(currentCpu.user + currentCpu.system)
    }, 100)

    const errors: string[] = []
    let success = true

    try {
      await testFn()
    } catch (error) {
      success = false
      errors.push(error.message)
    } finally {
      clearInterval(monitorInterval)
    }

    const endTime = Date.now()
    const afterMemory = process.memoryUsage().heapUsed
    const duration = endTime - startTime

    const averageCpu = cpuSamples.length > 0 
      ? cpuSamples.reduce((sum, sample) => sum + sample, 0) / cpuSamples.length 
      : 0
    const peakCpu = cpuSamples.length > 0 ? Math.max(...cpuSamples) : 0

    return {
      testName,
      duration,
      throughput: duration > 0 ? 1000 / duration : 0,
      memoryUsage: {
        before: Math.round(beforeMemory / 1024 / 1024),
        after: Math.round(afterMemory / 1024 / 1024),
        peak: Math.round(peakMemory / 1024 / 1024)
      },
      cpuUsage: {
        average: Math.round(averageCpu / 1000), // 转换为毫秒
        peak: Math.round(peakCpu / 1000)
      },
      success,
      errors,
      metrics: {
        memoryDelta: Math.round((afterMemory - beforeMemory) / 1024 / 1024),
        memoryEfficiency: beforeMemory > 0 ? (afterMemory - beforeMemory) / beforeMemory : 0
      }
    }
  }

  private async warmupSystem(): Promise<void> {
    console.log('预热系统...')
    
    // 创建一些简单任务来预热系统
    const taskQueue = TaskQueue.getInstance()
    await taskQueue.start()

    const warmupTasks = []
    for (let i = 0; i < 5; i++) {
      const taskInput = TestDataGenerator.generateTaskInput({
        name: `预热任务_${i}`,
        products: TestDataGenerator.generateProductIds(10)
      })
      warmupTasks.push(taskQueue.createTask(taskInput))
    }

    await Promise.all(warmupTasks)
    
    // 等待任务完成
    await new Promise(resolve => setTimeout(resolve, 5000))
    
    console.log('系统预热完成')
  }

  private async testSingleTaskPerformance(): Promise<void> {
    const taskQueue = TaskQueue.getInstance()
    const taskInput = TestDataGenerator.generateTaskInput({
      name: '单任务性能测试',
      products: TestDataGenerator.generateProductIds(50)
    })

    const task = await taskQueue.createTask(taskInput)
    
    // 等待任务完成
    const maxWaitTime = 30000
    const startTime = Date.now()
    
    while (Date.now() - startTime < maxWaitTime) {
      const updatedTask = await taskQueue.getTask(task.id)
      if (updatedTask?.status === 'COMPLETED' || updatedTask?.status === 'FAILED') {
        break
      }
      await new Promise(resolve => setTimeout(resolve, 500))
    }
  }

  private async testBatchTaskPerformance(): Promise<void> {
    const testData = TestDataGenerator.generatePerformanceTestData('large')
    const batchExcelService = new BatchExcelService()
    
    const taskQueue = TaskQueue.getInstance()
    const taskInput = TestDataGenerator.generateTaskInput({
      name: '批量性能测试',
      products: testData.productIds
    })

    const task = await taskQueue.createTask(taskInput)
    
    // 生成Excel
    await batchExcelService.generateBatchExcel(task.id, {
      products: testData.productIds,
      config: taskInput.config
    })
  }

  private async testFileUploadPerformance(): Promise<void> {
    const testData = TestDataGenerator.generateExcelTestData(1000)
    const fileBuffer = Buffer.from(testData.csvContent, 'utf-8')
    
    const mockFile = {
      buffer: fileBuffer,
      originalname: 'performance-test.csv',
      mimetype: 'text/csv',
      size: fileBuffer.length
    }

    const uploadResult = await FileUploadService.uploadFile(mockFile)
    await FileUploadService.parseProductFile(uploadResult.fileId)
  }

  private async testExcelGenerationPerformance(): Promise<void> {
    const batchExcelService = new BatchExcelService()
    const config = TestDataGenerator.generateFormData({
      timeMode: 'continuous',
      days: 30
    })

    await batchExcelService.generateSingleExcel(config)
  }

  private async testConcurrentTaskProcessing(): Promise<void> {
    const taskQueue = TaskQueue.getInstance()
    const concurrentTasks = []

    // 创建10个并发任务
    for (let i = 0; i < 10; i++) {
      const taskInput = TestDataGenerator.generateTaskInput({
        name: `并发测试任务_${i}`,
        products: TestDataGenerator.generateProductIds(20)
      })
      concurrentTasks.push(taskQueue.createTask(taskInput))
    }

    await Promise.all(concurrentTasks)
  }

  private async testMemoryEfficiency(): Promise<void> {
    const initialMemory = process.memoryUsage().heapUsed
    
    // 创建大量数据
    const largeData = []
    for (let i = 0; i < 10000; i++) {
      largeData.push(TestDataGenerator.generateFormData())
    }

    // 处理数据
    const processedData = largeData.map(data => ({
      ...data,
      processed: true,
      timestamp: new Date()
    }))

    // 清理数据
    largeData.length = 0
    processedData.length = 0

    // 强制垃圾回收
    if (global.gc) {
      global.gc()
    }

    const finalMemory = process.memoryUsage().heapUsed
    const memoryIncrease = finalMemory - initialMemory

    if (memoryIncrease > 100 * 1024 * 1024) { // 100MB
      throw new Error(`内存使用增长过多: ${Math.round(memoryIncrease / 1024 / 1024)}MB`)
    }
  }

  private async testDatabaseQueryPerformance(): Promise<void> {
    const taskQueue = TaskQueue.getInstance()
    
    // 测试各种数据库查询
    const queries = [
      () => taskQueue.listTasks({ limit: 100 }),
      () => taskQueue.getStats(),
      () => taskQueue.listTasks({ status: 'COMPLETED', limit: 50 }),
      () => taskQueue.listTasks({ page: 1, limit: 20 })
    ]

    for (const query of queries) {
      const startTime = Date.now()
      await query()
      const duration = Date.now() - startTime
      
      if (duration > 1000) { // 1秒
        throw new Error(`数据库查询耗时过长: ${duration}ms`)
      }
    }
  }

  private async testSystemLoad(): Promise<void> {
    // 模拟系统高负载
    const tasks = []
    
    for (let i = 0; i < 20; i++) {
      tasks.push(this.simulateWork(1000)) // 1秒工作
    }

    await Promise.all(tasks)
  }

  private async simulateWork(duration: number): Promise<void> {
    const endTime = Date.now() + duration
    while (Date.now() < endTime) {
      // 模拟CPU密集型工作
      Math.random() * Math.random()
    }
  }

  private async createLoadTestWorker(
    workerId: number,
    endTime: number,
    targetRate: number,
    callback: (success: boolean, responseTime: number, error?: string) => void
  ): Promise<void> {
    const interval = 1000 / targetRate // 请求间隔

    while (Date.now() < endTime) {
      const requestStart = Date.now()
      
      try {
        // 模拟API请求
        await this.simulateApiRequest()
        const responseTime = Date.now() - requestStart
        callback(true, responseTime)
      } catch (error) {
        const responseTime = Date.now() - requestStart
        callback(false, responseTime, error.message)
      }

      // 控制请求频率
      const elapsed = Date.now() - requestStart
      const waitTime = Math.max(0, interval - elapsed)
      if (waitTime > 0) {
        await new Promise(resolve => setTimeout(resolve, waitTime))
      }
    }
  }

  private async simulateApiRequest(): Promise<void> {
    // 模拟API请求的处理时间和可能的错误
    const processingTime = Math.random() * 100 + 50 // 50-150ms
    await new Promise(resolve => setTimeout(resolve, processingTime))

    // 5%的概率模拟错误
    if (Math.random() < 0.05) {
      throw new Error('模拟API错误')
    }
  }

  private async waitForSystemRecovery(): Promise<void> {
    // 等待系统资源恢复
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // 强制垃圾回收
    if (global.gc) {
      global.gc()
    }
  }

  private generatePerformanceSummary(): {
    averageDuration: number
    totalThroughput: number
    memoryEfficiency: number
    cpuEfficiency: number
  } {
    const successfulTests = this.testResults.filter(r => r.success)
    
    const averageDuration = successfulTests.length > 0
      ? successfulTests.reduce((sum, r) => sum + r.duration, 0) / successfulTests.length
      : 0

    const totalThroughput = successfulTests.reduce((sum, r) => sum + r.throughput, 0)

    const memoryEfficiency = successfulTests.length > 0
      ? successfulTests.reduce((sum, r) => sum + (r.metrics.memoryEfficiency || 0), 0) / successfulTests.length
      : 0

    const cpuEfficiency = successfulTests.length > 0
      ? successfulTests.reduce((sum, r) => sum + r.cpuUsage.average, 0) / successfulTests.length
      : 0

    return {
      averageDuration: Math.round(averageDuration),
      totalThroughput: Math.round(totalThroughput),
      memoryEfficiency: Math.round(memoryEfficiency * 100) / 100,
      cpuEfficiency: Math.round(cpuEfficiency)
    }
  }
}

// 导出性能测试运行器
export async function runPerformanceTests(): Promise<void> {
  const testSuite = new PerformanceTestSuite()
  const results = await testSuite.runAllPerformanceTests()
  
  console.log('\n性能测试结果:')
  console.log(`总测试数: ${results.totalTests}`)
  console.log(`通过: ${results.passed}`)
  console.log(`失败: ${results.failed}`)
  console.log(`平均耗时: ${results.summary.averageDuration}ms`)
  console.log(`总吞吐量: ${results.summary.totalThroughput}`)
  console.log(`内存效率: ${results.summary.memoryEfficiency}`)
  console.log(`CPU效率: ${results.summary.cpuEfficiency}ms`)

  if (results.failed > 0) {
    console.error('\n失败的测试:')
    results.results
      .filter(r => !r.success)
      .forEach(r => {
        console.error(`- ${r.testName}: ${r.errors.join(', ')}`)
      })
  }
}