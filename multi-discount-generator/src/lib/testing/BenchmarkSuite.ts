import { TestDataGenerator } from './TestDataGenerator'
import { TaskQueue } from '@/lib/batch/TaskQueue'
import { BatchExcelService } from '@/lib/batch/BatchExcelService'
import { FileUploadService } from '@/lib/upload/FileUploadService'

interface BenchmarkResult {
  name: string
  operations: number
  duration: number
  opsPerSecond: number
  averageTime: number
  minTime: number
  maxTime: number
  memoryUsed: number
  success: boolean
  error?: string
}

interface BenchmarkSuite {
  name: string
  benchmarks: BenchmarkResult[]
  summary: {
    totalOperations: number
    totalDuration: number
    averageOpsPerSecond: number
    memoryEfficiency: number
  }
}

/**
 * 性能基准测试套件
 * 测试各个组件的基准性能
 */
export class BenchmarkSuite {
  private results: BenchmarkResult[] = []

  /**
   * 运行所有基准测试
   */
  async runAllBenchmarks(): Promise<BenchmarkSuite> {
    console.log('开始运行性能基准测试...')

    this.results = []

    const benchmarks = [
      { name: '任务创建基准测试', fn: this.benchmarkTaskCreation, operations: 100 },
      { name: '文件解析基准测试', fn: this.benchmarkFileProcessing, operations: 50 },
      { name: 'Excel生成基准测试', fn: this.benchmarkExcelGeneration, operations: 20 },
      { name: '数据库查询基准测试', fn: this.benchmarkDatabaseQueries, operations: 200 },
      { name: '内存分配基准测试', fn: this.benchmarkMemoryAllocation, operations: 1000 },
      { name: '并发处理基准测试', fn: this.benchmarkConcurrentProcessing, operations: 50 },
      { name: '序列化基准测试', fn: this.benchmarkSerialization, operations: 500 },
      { name: '算法性能基准测试', fn: this.benchmarkAlgorithmPerformance, operations: 1000 }
    ]

    for (const benchmark of benchmarks) {
      try {
        console.log(`运行 ${benchmark.name}...`)
        const result = await this.runBenchmark(
          benchmark.name,
          benchmark.fn.bind(this),
          benchmark.operations
        )
        this.results.push(result)
        
        // 短暂休息让系统恢复
        await new Promise(resolve => setTimeout(resolve, 1000))
      } catch (error) {
        console.error(`基准测试失败: ${benchmark.name}`, error)
        this.results.push({
          name: benchmark.name,
          operations: benchmark.operations,
          duration: 0,
          opsPerSecond: 0,
          averageTime: 0,
          minTime: 0,
          maxTime: 0,
          memoryUsed: 0,
          success: false,
          error: error.message
        })
      }
    }

    const summary = this.generateSummary()
    
    return {
      name: '性能基准测试套件',
      benchmarks: this.results,
      summary
    }
  }

  /**
   * 运行单个基准测试
   */
  async runSingleBenchmark(
    name: string,
    testFn: () => Promise<void>,
    operations: number
  ): Promise<BenchmarkResult> {
    return this.runBenchmark(name, testFn, operations)
  }

  /**
   * 比较两个基准测试结果
   */
  compareBenchmarks(
    baseline: BenchmarkResult,
    current: BenchmarkResult
  ): {
    performanceChange: number
    memoryChange: number
    recommendation: string
  } {
    const performanceChange = baseline.opsPerSecond > 0 
      ? ((current.opsPerSecond - baseline.opsPerSecond) / baseline.opsPerSecond) * 100
      : 0

    const memoryChange = baseline.memoryUsed > 0
      ? ((current.memoryUsed - baseline.memoryUsed) / baseline.memoryUsed) * 100
      : 0

    let recommendation = ''
    if (performanceChange < -10) {
      recommendation = '性能显著下降，需要优化'
    } else if (performanceChange > 10) {
      recommendation = '性能显著提升'
    } else {
      recommendation = '性能变化在正常范围内'
    }

    if (memoryChange > 20) {
      recommendation += '，内存使用增长较多'
    }

    return {
      performanceChange,
      memoryChange,
      recommendation
    }
  }

  // 私有方法

  private async runBenchmark(
    name: string,
    testFn: () => Promise<void>,
    operations: number
  ): Promise<BenchmarkResult> {
    const times: number[] = []
    const initialMemory = process.memoryUsage().heapUsed
    let peakMemory = initialMemory

    const startTime = Date.now()

    try {
      for (let i = 0; i < operations; i++) {
        const operationStart = Date.now()
        
        await testFn()
        
        const operationTime = Date.now() - operationStart
        times.push(operationTime)

        // 监控内存使用
        const currentMemory = process.memoryUsage().heapUsed
        if (currentMemory > peakMemory) {
          peakMemory = currentMemory
        }
      }

      const totalDuration = Date.now() - startTime
      const opsPerSecond = (operations / totalDuration) * 1000
      const averageTime = times.reduce((sum, time) => sum + time, 0) / times.length
      const minTime = Math.min(...times)
      const maxTime = Math.max(...times)
      const memoryUsed = Math.round((peakMemory - initialMemory) / 1024 / 1024)

      return {
        name,
        operations,
        duration: totalDuration,
        opsPerSecond: Math.round(opsPerSecond * 100) / 100,
        averageTime: Math.round(averageTime * 100) / 100,
        minTime,
        maxTime,
        memoryUsed,
        success: true
      }

    } catch (error) {
      return {
        name,
        operations,
        duration: Date.now() - startTime,
        opsPerSecond: 0,
        averageTime: 0,
        minTime: 0,
        maxTime: 0,
        memoryUsed: 0,
        success: false,
        error: error.message
      }
    }
  }

  private async benchmarkTaskCreation(): Promise<void> {
    const taskQueue = TaskQueue.getInstance()
    const taskInput = TestDataGenerator.generateTaskInput({
      products: TestDataGenerator.generateProductIds(5)
    })

    await taskQueue.createTask(taskInput)
  }

  private async benchmarkFileProcessing(): Promise<void> {
    const testData = TestDataGenerator.generateExcelTestData(100)
    const fileBuffer = Buffer.from(testData.csvContent, 'utf-8')
    
    const mockFile = {
      buffer: fileBuffer,
      originalname: 'benchmark.csv',
      mimetype: 'text/csv',
      size: fileBuffer.length
    }

    const uploadResult = await FileUploadService.uploadFile(mockFile)
    await FileUploadService.parseProductFile(uploadResult.fileId)
  }

  private async benchmarkExcelGeneration(): Promise<void> {
    const batchExcelService = new BatchExcelService()
    const config = TestDataGenerator.generateFormData({
      timeMode: 'continuous',
      days: 7
    })

    await batchExcelService.generateSingleExcel(config)
  }

  private async benchmarkDatabaseQueries(): Promise<void> {
    const taskQueue = TaskQueue.getInstance()
    
    // 执行各种数据库查询
    await taskQueue.getStats()
    await taskQueue.listTasks({ limit: 10 })
  }

  private async benchmarkMemoryAllocation(): Promise<void> {
    // 测试内存分配和释放性能
    const data = new Array(1000).fill(0).map(() => ({
      id: Math.random().toString(36),
      timestamp: new Date(),
      data: new Array(100).fill(Math.random())
    }))

    // 处理数据
    const processed = data.map(item => ({
      ...item,
      processed: true,
      hash: item.id.split('').reduce((hash, char) => hash + char.charCodeAt(0), 0)
    }))

    // 清理
    data.length = 0
    processed.length = 0
  }

  private async benchmarkConcurrentProcessing(): Promise<void> {
    // 测试并发处理性能
    const tasks = []
    for (let i = 0; i < 10; i++) {
      tasks.push(this.simulateAsyncWork(50))
    }

    await Promise.all(tasks)
  }

  private async benchmarkSerialization(): Promise<void> {
    // 测试序列化性能
    const data = TestDataGenerator.generateFormData()
    const serialized = JSON.stringify(data)
    const deserialized = JSON.parse(serialized)
    
    // 验证数据完整性
    if (!deserialized.productId) {
      throw new Error('序列化数据不完整')
    }
  }

  private async benchmarkAlgorithmPerformance(): Promise<void> {
    // 测试算法性能
    const numbers = new Array(1000).fill(0).map(() => Math.random() * 1000)
    
    // 排序算法
    const sorted = [...numbers].sort((a, b) => a - b)
    
    // 搜索算法
    const target = numbers[Math.floor(Math.random() * numbers.length)]
    const index = sorted.findIndex(num => num === target)
    
    // 过滤算法
    const filtered = numbers.filter(num => num > 500)
    
    // 映射算法
    const mapped = numbers.map(num => Math.sqrt(num))
    
    // 归约算法
    const sum = numbers.reduce((acc, num) => acc + num, 0)
  }

  private async simulateAsyncWork(duration: number): Promise<void> {
    return new Promise(resolve => {
      setTimeout(() => {
        // 模拟一些CPU工作
        let result = 0
        for (let i = 0; i < 1000; i++) {
          result += Math.sqrt(i)
        }
        resolve()
      }, duration)
    })
  }

  private generateSummary(): {
    totalOperations: number
    totalDuration: number
    averageOpsPerSecond: number
    memoryEfficiency: number
  } {
    const successfulBenchmarks = this.results.filter(r => r.success)
    
    const totalOperations = successfulBenchmarks.reduce((sum, r) => sum + r.operations, 0)
    const totalDuration = successfulBenchmarks.reduce((sum, r) => sum + r.duration, 0)
    const averageOpsPerSecond = successfulBenchmarks.length > 0
      ? successfulBenchmarks.reduce((sum, r) => sum + r.opsPerSecond, 0) / successfulBenchmarks.length
      : 0
    const memoryEfficiency = successfulBenchmarks.length > 0
      ? successfulBenchmarks.reduce((sum, r) => sum + (r.operations / Math.max(r.memoryUsed, 1)), 0) / successfulBenchmarks.length
      : 0

    return {
      totalOperations,
      totalDuration,
      averageOpsPerSecond: Math.round(averageOpsPerSecond * 100) / 100,
      memoryEfficiency: Math.round(memoryEfficiency * 100) / 100
    }
  }
}

// 导出基准测试运行器
export async function runBenchmarks(): Promise<void> {
  const benchmarkSuite = new BenchmarkSuite()
  const results = await benchmarkSuite.runAllBenchmarks()
  
  console.log('\n性能基准测试结果:')
  console.log(`总操作数: ${results.summary.totalOperations}`)
  console.log(`总耗时: ${results.summary.totalDuration}ms`)
  console.log(`平均操作/秒: ${results.summary.averageOpsPerSecond}`)
  console.log(`内存效率: ${results.summary.memoryEfficiency}`)

  console.log('\n详细结果:')
  results.benchmarks.forEach(benchmark => {
    if (benchmark.success) {
      console.log(`${benchmark.name}:`)
      console.log(`  操作数: ${benchmark.operations}`)
      console.log(`  耗时: ${benchmark.duration}ms`)
      console.log(`  操作/秒: ${benchmark.opsPerSecond}`)
      console.log(`  平均时间: ${benchmark.averageTime}ms`)
      console.log(`  内存使用: ${benchmark.memoryUsed}MB`)
    } else {
      console.log(`${benchmark.name}: 失败 - ${benchmark.error}`)
    }
  })
}