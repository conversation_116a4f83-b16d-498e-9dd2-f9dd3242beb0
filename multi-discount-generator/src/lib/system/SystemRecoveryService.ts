import { prisma } from '@/lib/prisma'
import { TaskStatus } from '@prisma/client'
import { TaskQueue } from '@/lib/queue/TaskQueue'
import { performanceMonitor } from '@/lib/performance/PerformanceMonitor'
import { healthCheckService } from '@/lib/monitoring/HealthCheckService'
import { globalErrorHandler } from '@/lib/error/GlobalErrorHandler'

interface RecoveryCheckpoint {
  id: string
  timestamp: Date
  systemState: {
    runningTasks: string[]
    queueSize: number
    systemHealth: string
    memoryUsage: number
    cpuUsage: number
  }
  metadata: Record<string, any>
}

interface RecoveryPlan {
  priority: number
  actions: RecoveryAction[]
  estimatedDuration: number
  dependencies: string[]
}

interface RecoveryAction {
  type: 'restart_task' | 'cleanup_resources' | 'reset_queue' | 'restore_data' | 'notify_admin'
  description: string
  execute: () => Promise<boolean>
  rollback?: () => Promise<void>
  timeout: number
}

/**
 * 系统故障恢复服务
 * 处理系统崩溃后的自动恢复
 */
export class SystemRecoveryService {
  private static instance: SystemRecoveryService
  private checkpointInterval: NodeJS.Timeout | null = null
  private recoveryInProgress = false
  private lastCheckpoint: RecoveryCheckpoint | null = null

  private constructor() {}

  static getInstance(): SystemRecoveryService {
    if (!SystemRecoveryService.instance) {
      SystemRecoveryService.instance = new SystemRecoveryService()
    }
    return SystemRecoveryService.instance
  }

  /**
   * 启动系统恢复服务
   */
  async start(): Promise<void> {
    console.log('启动系统恢复服务...')

    // 检查是否需要恢复
    const needsRecovery = await this.checkIfRecoveryNeeded()
    if (needsRecovery) {
      await this.performSystemRecovery()
    }

    // 开始定期创建检查点
    this.startCheckpointing()

    console.log('系统恢复服务已启动')
  }

  /**
   * 停止系统恢复服务
   */
  async stop(): Promise<void> {
    console.log('停止系统恢复服务...')

    if (this.checkpointInterval) {
      clearInterval(this.checkpointInterval)
      this.checkpointInterval = null
    }

    // 创建最终检查点
    await this.createCheckpoint('system_shutdown')

    console.log('系统恢复服务已停止')
  }

  /**
   * 手动触发系统恢复
   */
  async triggerRecovery(reason: string = 'manual'): Promise<{
    success: boolean
    recoveredTasks: number
    errors: string[]
    duration: number
  }> {
    if (this.recoveryInProgress) {
      throw new Error('系统恢复已在进行中')
    }

    console.log(`开始手动系统恢复，原因: ${reason}`)
    
    const startTime = Date.now()
    const result = await this.performSystemRecovery()
    const duration = Date.now() - startTime

    return {
      ...result,
      duration
    }
  }

  /**
   * 创建系统检查点
   */
  async createCheckpoint(reason: string = 'scheduled'): Promise<void> {
    try {
      const systemState = await this.captureSystemState()
      
      const checkpoint: RecoveryCheckpoint = {
        id: `checkpoint_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        timestamp: new Date(),
        systemState,
        metadata: {
          reason,
          nodeVersion: process.version,
          platform: process.platform,
          uptime: process.uptime()
        }
      }

      // 保存检查点到数据库
      await prisma.taskLog.create({
        data: {
          level: 'INFO',
          message: `系统检查点已创建: ${checkpoint.id}`,
          timestamp: checkpoint.timestamp,
          metadata: {
            source: 'system_recovery_service',
            action: 'checkpoint_created',
            checkpoint: checkpoint
          }
        }
      })

      this.lastCheckpoint = checkpoint

      performanceMonitor.recordMetric('system_checkpoint_created', 1, 'count', {
        reason,
        runningTasks: systemState.runningTasks.length,
        queueSize: systemState.queueSize
      })

    } catch (error) {
      console.error('创建系统检查点失败:', error)
      
      await globalErrorHandler.handleError(error, {
        timestamp: new Date(),
        additionalData: {
          source: 'system_recovery_service',
          action: 'checkpoint_creation_failed'
        }
      })
    }
  }

  /**
   * 获取恢复统计
   */
  async getRecoveryStats(timeWindow: number = 86400000): Promise<{
    totalRecoveries: number
    successfulRecoveries: number
    failedRecoveries: number
    averageRecoveryTime: number
    recoveredTasks: number
    lastRecovery?: Date
    checkpointsCreated: number
  }> {
    const startTime = new Date(Date.now() - timeWindow)
    
    const recoveryLogs = await prisma.taskLog.findMany({
      where: {
        timestamp: { gte: startTime },
        metadata: {
          path: ['source'],
          equals: 'system_recovery_service'
        }
      }
    })

    const recoveryAttempts = recoveryLogs.filter(log => 
      (log.metadata as any)?.action === 'recovery_started'
    )

    const successfulRecoveries = recoveryLogs.filter(log => 
      (log.metadata as any)?.action === 'recovery_completed'
    )

    const failedRecoveries = recoveryLogs.filter(log => 
      (log.metadata as any)?.action === 'recovery_failed'
    )

    const checkpointsCreated = recoveryLogs.filter(log => 
      (log.metadata as any)?.action === 'checkpoint_created'
    ).length

    // 计算平均恢复时间
    const recoveryTimes = successfulRecoveries
      .map(log => (log.metadata as any)?.duration)
      .filter(duration => duration !== undefined)

    const averageRecoveryTime = recoveryTimes.length > 0
      ? recoveryTimes.reduce((sum, time) => sum + time, 0) / recoveryTimes.length
      : 0

    // 计算恢复的任务总数
    const recoveredTasks = successfulRecoveries
      .reduce((sum, log) => sum + ((log.metadata as any)?.recoveredTasks || 0), 0)

    const lastRecovery = successfulRecoveries.length > 0
      ? successfulRecoveries[successfulRecoveries.length - 1].timestamp
      : undefined

    return {
      totalRecoveries: recoveryAttempts.length,
      successfulRecoveries: successfulRecoveries.length,
      failedRecoveries: failedRecoveries.length,
      averageRecoveryTime,
      recoveredTasks,
      lastRecovery,
      checkpointsCreated
    }
  }

  // 私有方法

  private async checkIfRecoveryNeeded(): Promise<boolean> {
    try {
      // 检查是否有未完成的任务
      const incompleteTasks = await prisma.task.findMany({
        where: {
          status: { in: [TaskStatus.RUNNING, TaskStatus.PENDING] },
          updatedAt: {
            lt: new Date(Date.now() - 5 * 60 * 1000) // 5分钟前
          }
        }
      })

      if (incompleteTasks.length > 0) {
        console.log(`发现 ${incompleteTasks.length} 个可能需要恢复的任务`)
        return true
      }

      // 检查系统健康状态
      const healthCheck = await healthCheckService.performHealthCheck()
      if (healthCheck.overall === 'critical') {
        console.log('系统健康状态为严重，需要恢复')
        return true
      }

      return false

    } catch (error) {
      console.error('检查恢复需求时发生错误:', error)
      return true // 出错时假设需要恢复
    }
  }

  private async performSystemRecovery(): Promise<{
    success: boolean
    recoveredTasks: number
    errors: string[]
  }> {
    this.recoveryInProgress = true
    const errors: string[] = []
    let recoveredTasks = 0

    try {
      console.log('开始系统恢复...')
      
      await prisma.taskLog.create({
        data: {
          level: 'INFO',
          message: '系统恢复开始',
          timestamp: new Date(),
          metadata: {
            source: 'system_recovery_service',
            action: 'recovery_started'
          }
        }
      })

      // 创建恢复计划
      const recoveryPlan = await this.createRecoveryPlan()

      // 按优先级执行恢复操作
      for (const plan of recoveryPlan.sort((a, b) => b.priority - a.priority)) {
        try {
          const result = await this.executeRecoveryPlan(plan)
          recoveredTasks += result.recoveredTasks
          errors.push(...result.errors)
        } catch (error) {
          console.error('执行恢复计划失败:', error)
          errors.push(`恢复计划执行失败: ${error.message}`)
        }
      }

      // 验证恢复结果
      const verificationResult = await this.verifyRecovery()
      if (!verificationResult.success) {
        errors.push(...verificationResult.errors)
      }

      const success = errors.length === 0

      await prisma.taskLog.create({
        data: {
          level: success ? 'INFO' : 'WARN',
          message: `系统恢复${success ? '成功' : '部分成功'}完成`,
          timestamp: new Date(),
          metadata: {
            source: 'system_recovery_service',
            action: success ? 'recovery_completed' : 'recovery_partial',
            recoveredTasks,
            errors: errors.length,
            errorDetails: errors
          }
        }
      })

      performanceMonitor.recordMetric('system_recovery_completed', 1, 'count', {
        success,
        recoveredTasks,
        errorCount: errors.length
      })

      console.log(`系统恢复完成，恢复了 ${recoveredTasks} 个任务，${errors.length} 个错误`)

      return { success, recoveredTasks, errors }

    } catch (error) {
      console.error('系统恢复失败:', error)
      errors.push(`系统恢复失败: ${error.message}`)

      await prisma.taskLog.create({
        data: {
          level: 'ERROR',
          message: `系统恢复失败: ${error.message}`,
          timestamp: new Date(),
          metadata: {
            source: 'system_recovery_service',
            action: 'recovery_failed',
            error: error.stack
          }
        }
      })

      return { success: false, recoveredTasks, errors }

    } finally {
      this.recoveryInProgress = false
    }
  }

  private async createRecoveryPlan(): Promise<RecoveryPlan[]> {
    const plans: RecoveryPlan[] = []

    // 1. 清理资源计划
    plans.push({
      priority: 100,
      actions: [{
        type: 'cleanup_resources',
        description: '清理系统资源和临时文件',
        execute: async () => {
          await this.cleanupResources()
          return true
        },
        timeout: 30000
      }],
      estimatedDuration: 30000,
      dependencies: []
    })

    // 2. 重启失败任务计划
    const failedTasks = await prisma.task.findMany({
      where: {
        status: TaskStatus.RUNNING,
        updatedAt: {
          lt: new Date(Date.now() - 5 * 60 * 1000)
        }
      }
    })

    if (failedTasks.length > 0) {
      plans.push({
        priority: 90,
        actions: failedTasks.map(task => ({
          type: 'restart_task' as const,
          description: `重启任务 ${task.name}`,
          execute: async () => {
            await this.restartTask(task.id)
            return true
          },
          timeout: 60000
        })),
        estimatedDuration: failedTasks.length * 10000,
        dependencies: ['cleanup_resources']
      })
    }

    // 3. 重置任务队列计划
    plans.push({
      priority: 80,
      actions: [{
        type: 'reset_queue',
        description: '重置任务队列状态',
        execute: async () => {
          await this.resetTaskQueue()
          return true
        },
        timeout: 15000
      }],
      estimatedDuration: 15000,
      dependencies: ['restart_task']
    })

    return plans
  }

  private async executeRecoveryPlan(plan: RecoveryPlan): Promise<{
    recoveredTasks: number
    errors: string[]
  }> {
    const errors: string[] = []
    let recoveredTasks = 0

    for (const action of plan.actions) {
      try {
        console.log(`执行恢复操作: ${action.description}`)
        
        const success = await Promise.race([
          action.execute(),
          new Promise<boolean>((_, reject) => 
            setTimeout(() => reject(new Error('操作超时')), action.timeout)
          )
        ])

        if (success) {
          if (action.type === 'restart_task') {
            recoveredTasks++
          }
        } else {
          errors.push(`操作失败: ${action.description}`)
        }

      } catch (error) {
        console.error(`恢复操作失败: ${action.description}`, error)
        errors.push(`${action.description}: ${error.message}`)

        // 如果有回滚操作，执行回滚
        if (action.rollback) {
          try {
            await action.rollback()
          } catch (rollbackError) {
            console.error('回滚操作失败:', rollbackError)
            errors.push(`回滚失败: ${rollbackError.message}`)
          }
        }
      }
    }

    return { recoveredTasks, errors }
  }

  private async verifyRecovery(): Promise<{
    success: boolean
    errors: string[]
  }> {
    const errors: string[] = []

    try {
      // 检查任务队列状态
      const taskQueue = TaskQueue.getInstance()
      const queueStats = await taskQueue.getStats()
      
      if (queueStats.failed > queueStats.completed) {
        errors.push('任务队列中失败任务过多')
      }

      // 检查系统健康状态
      const healthCheck = await healthCheckService.performHealthCheck()
      if (healthCheck.overall === 'critical') {
        errors.push('系统健康状态仍为严重')
      }

      // 检查数据库连接
      try {
        await prisma.$queryRaw`SELECT 1`
      } catch (dbError) {
        errors.push('数据库连接验证失败')
      }

    } catch (error) {
      errors.push(`恢复验证失败: ${error.message}`)
    }

    return {
      success: errors.length === 0,
      errors
    }
  }

  private async captureSystemState(): Promise<RecoveryCheckpoint['systemState']> {
    const taskQueue = TaskQueue.getInstance()
    const queueStats = await taskQueue.getStats()
    
    const runningTasks = await prisma.task.findMany({
      where: { status: TaskStatus.RUNNING },
      select: { id: true }
    })

    const healthCheck = await healthCheckService.performHealthCheck()
    const memoryUsage = process.memoryUsage()

    return {
      runningTasks: runningTasks.map(t => t.id),
      queueSize: queueStats.pending,
      systemHealth: healthCheck.overall,
      memoryUsage: memoryUsage.heapUsed,
      cpuUsage: process.cpuUsage().user
    }
  }

  private startCheckpointing(): void {
    // 每5分钟创建一个检查点
    this.checkpointInterval = setInterval(async () => {
      await this.createCheckpoint('scheduled')
    }, 5 * 60 * 1000)
  }

  private async cleanupResources(): Promise<void> {
    // 清理临时文件
    const fs = require('fs').promises
    const path = require('path')
    
    try {
      const tempDir = path.join(process.cwd(), 'temp')
      const files = await fs.readdir(tempDir)
      
      for (const file of files) {
        const filePath = path.join(tempDir, file)
        const stats = await fs.stat(filePath)
        
        // 删除超过1小时的临时文件
        if (Date.now() - stats.mtime.getTime() > 60 * 60 * 1000) {
          await fs.unlink(filePath)
        }
      }
    } catch (error) {
      console.warn('清理临时文件失败:', error)
    }

    // 强制垃圾回收
    if (global.gc) {
      global.gc()
    }
  }

  private async restartTask(taskId: string): Promise<void> {
    await prisma.task.update({
      where: { id: taskId },
      data: {
        status: TaskStatus.PENDING,
        error: null,
        progress: 0,
        updatedAt: new Date()
      }
    })

    await prisma.taskLog.create({
      data: {
        taskId,
        level: 'INFO',
        message: '任务已重启，等待重新执行',
        timestamp: new Date(),
        metadata: {
          source: 'system_recovery_service',
          action: 'task_restarted'
        }
      }
    })
  }

  private async resetTaskQueue(): Promise<void> {
    const taskQueue = TaskQueue.getInstance()
    await taskQueue.stop()
    await taskQueue.start()
  }
}

// 导出单例实例
export const systemRecoveryService = SystemRecoveryService.getInstance()