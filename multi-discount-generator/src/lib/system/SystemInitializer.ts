import { healthCheckService } from '@/lib/monitoring/HealthCheckService'
import { performanceMonitor } from '@/lib/performance/PerformanceMonitor'
import { resourceManager } from '@/lib/performance/ResourceManager'
import { TaskQueue } from '@/lib/queue/TaskQueue'
import { logAggregationService } from '@/lib/monitoring/LogAggregationService'
import { systemRecoveryService } from './SystemRecoveryService'
import { globalErrorHandler } from '@/lib/error/GlobalErrorHandler'
import { dataBackupService } from '@/lib/backup/DataBackupService'

/**
 * 系统初始化服务
 * 负责启动和配置系统各个组件
 */
export class SystemInitializer {
  private static instance: SystemInitializer
  private initialized = false

  private constructor() {}

  static getInstance(): SystemInitializer {
    if (!SystemInitializer.instance) {
      SystemInitializer.instance = new SystemInitializer()
    }
    return SystemInitializer.instance
  }

  /**
   * 初始化系统
   */
  async initialize(): Promise<void> {
    if (this.initialized) {
      console.log('系统已经初始化')
      return
    }

    console.log('开始初始化系统...')

    try {
      // 1. 启动性能监控
      console.log('启动性能监控...')
      performanceMonitor.startCollection(30000) // 30秒间隔

      // 2. 启动资源管理器
      console.log('启动资源管理器...')
      resourceManager.startMonitoring(60000) // 60秒间隔

      // 3. 启动健康检查
      console.log('启动健康检查...')
      healthCheckService.startPeriodicChecks(60000) // 60秒间隔

      // 4. 启动系统恢复服务
      console.log('启动系统恢复服务...')
      await systemRecoveryService.start()

      // 5. 配置全局错误处理器
      console.log('配置全局错误处理器...')
      globalErrorHandler.configureCircuitBreaker('database_connection', 5, 60000)
      globalErrorHandler.configureCircuitBreaker('file_operation', 3, 30000)
      globalErrorHandler.configureCircuitBreaker('task_execution', 3, 120000)

      // 6. 启动任务队列
      console.log('启动任务队列...')
      const taskQueue = TaskQueue.getInstance()
      await taskQueue.start()

      // 7. 清理旧日志和备份（可选）
      if (process.env.NODE_ENV === 'production') {
        console.log('清理旧日志...')
        const cleanupResult = await logAggregationService.cleanupOldLogs(30)
        console.log(`清理了 ${cleanupResult.deletedCount} 条旧日志`)

        console.log('清理过期备份...')
        const backupCleanup = await dataBackupService.cleanupExpiredBackups()
        console.log(`清理了 ${backupCleanup.deletedCount} 个过期备份，释放空间 ${Math.round(backupCleanup.freedSpace / 1024 / 1024)}MB`)
      }

      // 8. 记录系统启动指标
      performanceMonitor.recordMetric('system_startup', 1, 'count', {
        timestamp: new Date().toISOString(),
        nodeVersion: process.version,
        platform: process.platform
      })

      this.initialized = true
      console.log('系统初始化完成')

      // 执行初始健康检查
      const healthCheck = await healthCheckService.performHealthCheck()
      console.log(`系统健康状态: ${healthCheck.overall}`)

    } catch (error) {
      console.error('系统初始化失败:', error)
      throw error
    }
  }

  /**
   * 关闭系统
   */
  async shutdown(): Promise<void> {
    if (!this.initialized) {
      console.log('系统未初始化，无需关闭')
      return
    }

    console.log('开始关闭系统...')

    try {
      // 1. 停止任务队列
      console.log('停止任务队列...')
      const taskQueue = TaskQueue.getInstance()
      await taskQueue.stop()

      // 2. 停止系统恢复服务
      console.log('停止系统恢复服务...')
      await systemRecoveryService.stop()

      // 3. 停止健康检查
      console.log('停止健康检查...')
      healthCheckService.stopPeriodicChecks()

      // 4. 停止资源监控
      console.log('停止资源监控...')
      resourceManager.stopMonitoring()

      // 5. 停止性能监控
      console.log('停止性能监控...')
      performanceMonitor.stopCollection()

      // 6. 记录系统关闭指标
      performanceMonitor.recordMetric('system_shutdown', 1, 'count', {
        timestamp: new Date().toISOString(),
        uptime: process.uptime()
      })

      this.initialized = false
      console.log('系统关闭完成')

    } catch (error) {
      console.error('系统关闭失败:', error)
      throw error
    }
  }

  /**
   * 获取系统状态
   */
  getSystemStatus(): {
    initialized: boolean
    uptime: number
    memoryUsage: NodeJS.MemoryUsage
    cpuUsage: NodeJS.CpuUsage
  } {
    return {
      initialized: this.initialized,
      uptime: process.uptime(),
      memoryUsage: process.memoryUsage(),
      cpuUsage: process.cpuUsage()
    }
  }

  /**
   * 重启系统组件
   */
  async restart(): Promise<void> {
    console.log('重启系统...')
    await this.shutdown()
    await this.initialize()
    console.log('系统重启完成')
  }

  /**
   * 检查系统是否已初始化
   */
  isInitialized(): boolean {
    return this.initialized
  }
}

// 导出单例实例
export const systemInitializer = SystemInitializer.getInstance()

// 处理进程退出信号
if (typeof process !== 'undefined') {
  process.on('SIGTERM', async () => {
    console.log('收到SIGTERM信号，正在关闭系统...')
    try {
      await systemInitializer.shutdown()
      process.exit(0)
    } catch (error) {
      console.error('系统关闭失败:', error)
      process.exit(1)
    }
  })

  process.on('SIGINT', async () => {
    console.log('收到SIGINT信号，正在关闭系统...')
    try {
      await systemInitializer.shutdown()
      process.exit(0)
    } catch (error) {
      console.error('系统关闭失败:', error)
      process.exit(1)
    }
  })

  process.on('uncaughtException', async (error) => {
    console.error('未捕获的异常:', error)
    try {
      await systemInitializer.shutdown()
    } catch (shutdownError) {
      console.error('关闭系统时发生错误:', shutdownError)
    }
    process.exit(1)
  })

  process.on('unhandledRejection', async (reason, promise) => {
    console.error('未处理的Promise拒绝:', reason)
    try {
      await systemInitializer.shutdown()
    } catch (shutdownError) {
      console.error('关闭系统时发生错误:', shutdownError)
    }
    process.exit(1)
  })
}