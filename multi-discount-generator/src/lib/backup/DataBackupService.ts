import { prisma } from '@/lib/prisma'
import { performanceMonitor } from '@/lib/performance/PerformanceMonitor'
import { globalErrorHandler } from '@/lib/error/GlobalErrorHandler'
import * as fs from 'fs/promises'
import * as path from 'path'

interface BackupConfig {
  includeFiles: boolean
  includeLogs: boolean
  includeMetrics: boolean
  compression: boolean
  encryption: boolean
  retentionDays: number
  maxBackupSize: number // bytes
}

interface BackupMetadata {
  id: string
  timestamp: Date
  size: number
  checksum: string
  config: BackupConfig
  tables: string[]
  fileCount: number
  status: 'creating' | 'completed' | 'failed'
  error?: string
}

interface RestoreOptions {
  backupId: string
  restoreTables: string[]
  restoreFiles: boolean
  overwriteExisting: boolean
  validateBeforeRestore: boolean
}

/**
 * 数据备份和恢复服务
 * 提供完整的数据备份和恢复功能
 */
export class DataBackupService {
  private static instance: DataBackupService
  private backupDir: string
  private defaultConfig: BackupConfig = {
    includeFiles: true,
    includeLogs: true,
    includeMetrics: false,
    compression: true,
    encryption: false,
    retentionDays: 30,
    maxBackupSize: 1024 * 1024 * 1024 // 1GB
  }

  private constructor() {
    // 在Edge Runtime中使用相对路径
    this.backupDir = typeof process !== 'undefined' && process.cwd 
      ? path.join(process.cwd(), 'backups')
      : './backups'
    this.ensureBackupDirectory()
  }

  static getInstance(): DataBackupService {
    if (!DataBackupService.instance) {
      DataBackupService.instance = new DataBackupService()
    }
    return DataBackupService.instance
  }

  /**
   * 创建完整备份
   */
  async createBackup(config?: Partial<BackupConfig>): Promise<{
    success: boolean
    backupId: string
    size: number
    duration: number
    error?: string
  }> {
    const finalConfig = { ...this.defaultConfig, ...config }
    const backupId = `backup_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    const startTime = Date.now()

    try {
      console.log(`开始创建备份: ${backupId}`)

      // 创建备份元数据
      const metadata: BackupMetadata = {
        id: backupId,
        timestamp: new Date(),
        size: 0,
        checksum: '',
        config: finalConfig,
        tables: [],
        fileCount: 0,
        status: 'creating'
      }

      // 创建备份目录
      const backupPath = path.join(this.backupDir, backupId)
      await fs.mkdir(backupPath, { recursive: true })

      // 备份数据库
      const dbBackupResult = await this.backupDatabase(backupPath, finalConfig)
      metadata.tables = dbBackupResult.tables
      metadata.size += dbBackupResult.size

      // 备份文件
      if (finalConfig.includeFiles) {
        const fileBackupResult = await this.backupFiles(backupPath)
        metadata.fileCount = fileBackupResult.fileCount
        metadata.size += fileBackupResult.size
      }

      // 计算校验和
      metadata.checksum = await this.calculateChecksum(backupPath)

      // 检查备份大小限制
      if (metadata.size > finalConfig.maxBackupSize) {
        throw new Error(`备份大小 ${metadata.size} 超过限制 ${finalConfig.maxBackupSize}`)
      }

      // 压缩备份（如果启用）
      if (finalConfig.compression) {
        await this.compressBackup(backupPath)
      }

      // 保存备份元数据
      metadata.status = 'completed'
      await this.saveBackupMetadata(backupPath, metadata)

      const duration = Date.now() - startTime

      // 记录备份成功
      await prisma.taskLog.create({
        data: {
          level: 'INFO',
          message: `数据备份创建成功: ${backupId}`,
          timestamp: new Date(),
          metadata: {
            source: 'data_backup_service',
            action: 'backup_created',
            backupId,
            size: metadata.size,
            duration,
            tables: metadata.tables.length,
            files: metadata.fileCount
          }
        }
      })

      performanceMonitor.recordMetric('backup_created', 1, 'count', {
        backupId,
        size: metadata.size,
        duration,
        compressed: finalConfig.compression
      })

      console.log(`备份创建完成: ${backupId}, 大小: ${metadata.size} bytes, 耗时: ${duration}ms`)

      return {
        success: true,
        backupId,
        size: metadata.size,
        duration
      }

    } catch (error) {
      console.error('创建备份失败:', error)

      // 清理失败的备份
      try {
        const backupPath = path.join(this.backupDir, backupId)
        await fs.rm(backupPath, { recursive: true, force: true })
      } catch (cleanupError) {
        console.error('清理失败备份时出错:', cleanupError)
      }

      // 记录备份失败
      await prisma.taskLog.create({
        data: {
          level: 'ERROR',
          message: `数据备份创建失败: ${error.message}`,
          timestamp: new Date(),
          metadata: {
            source: 'data_backup_service',
            action: 'backup_failed',
            backupId,
            error: error.stack
          }
        }
      })

      return {
        success: false,
        backupId,
        size: 0,
        duration: Date.now() - startTime,
        error: error.message
      }
    }
  }

  /**
   * 恢复数据
   */
  async restoreBackup(options: RestoreOptions): Promise<{
    success: boolean
    restoredTables: string[]
    restoredFiles: number
    duration: number
    error?: string
  }> {
    const startTime = Date.now()
    const restoredTables: string[] = []
    let restoredFiles = 0

    try {
      console.log(`开始恢复备份: ${options.backupId}`)

      // 验证备份
      const backupPath = path.join(this.backupDir, options.backupId)
      const metadata = await this.loadBackupMetadata(backupPath)

      if (!metadata) {
        throw new Error('备份元数据不存在')
      }

      if (metadata.status !== 'completed') {
        throw new Error(`备份状态无效: ${metadata.status}`)
      }

      // 验证备份完整性
      if (options.validateBeforeRestore) {
        const isValid = await this.validateBackup(backupPath, metadata)
        if (!isValid) {
          throw new Error('备份完整性验证失败')
        }
      }

      // 解压备份（如果需要）
      if (metadata.config.compression) {
        await this.decompressBackup(backupPath)
      }

      // 恢复数据库
      if (options.restoreTables.length > 0) {
        const dbRestoreResult = await this.restoreDatabase(
          backupPath,
          options.restoreTables,
          options.overwriteExisting
        )
        restoredTables.push(...dbRestoreResult.tables)
      }

      // 恢复文件
      if (options.restoreFiles && metadata.config.includeFiles) {
        const fileRestoreResult = await this.restoreFiles(
          backupPath,
          options.overwriteExisting
        )
        restoredFiles = fileRestoreResult.fileCount
      }

      const duration = Date.now() - startTime

      // 记录恢复成功
      await prisma.taskLog.create({
        data: {
          level: 'INFO',
          message: `数据恢复完成: ${options.backupId}`,
          timestamp: new Date(),
          metadata: {
            source: 'data_backup_service',
            action: 'restore_completed',
            backupId: options.backupId,
            restoredTables: restoredTables.length,
            restoredFiles,
            duration
          }
        }
      })

      performanceMonitor.recordMetric('backup_restored', 1, 'count', {
        backupId: options.backupId,
        tables: restoredTables.length,
        files: restoredFiles,
        duration
      })

      console.log(`数据恢复完成: ${restoredTables.length} 个表, ${restoredFiles} 个文件, 耗时: ${duration}ms`)

      return {
        success: true,
        restoredTables,
        restoredFiles,
        duration
      }

    } catch (error) {
      console.error('恢复备份失败:', error)

      // 记录恢复失败
      await prisma.taskLog.create({
        data: {
          level: 'ERROR',
          message: `数据恢复失败: ${error.message}`,
          timestamp: new Date(),
          metadata: {
            source: 'data_backup_service',
            action: 'restore_failed',
            backupId: options.backupId,
            error: error.stack
          }
        }
      })

      return {
        success: false,
        restoredTables,
        restoredFiles,
        duration: Date.now() - startTime,
        error: error.message
      }
    }
  }

  /**
   * 列出所有备份
   */
  async listBackups(): Promise<BackupMetadata[]> {
    try {
      const backupDirs = await fs.readdir(this.backupDir)
      const backups: BackupMetadata[] = []

      for (const dir of backupDirs) {
        if (dir.startsWith('backup_')) {
          try {
            const backupPath = path.join(this.backupDir, dir)
            const metadata = await this.loadBackupMetadata(backupPath)
            if (metadata) {
              backups.push(metadata)
            }
          } catch (error) {
            console.warn(`加载备份元数据失败: ${dir}`, error)
          }
        }
      }

      return backups.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())

    } catch (error) {
      console.error('列出备份失败:', error)
      return []
    }
  }

  /**
   * 删除备份
   */
  async deleteBackup(backupId: string): Promise<{
    success: boolean
    error?: string
  }> {
    try {
      const backupPath = path.join(this.backupDir, backupId)
      await fs.rm(backupPath, { recursive: true, force: true })

      await prisma.taskLog.create({
        data: {
          level: 'INFO',
          message: `备份已删除: ${backupId}`,
          timestamp: new Date(),
          metadata: {
            source: 'data_backup_service',
            action: 'backup_deleted',
            backupId
          }
        }
      })

      return { success: true }

    } catch (error) {
      console.error('删除备份失败:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }

  /**
   * 清理过期备份
   */
  async cleanupExpiredBackups(): Promise<{
    deletedCount: number
    freedSpace: number
  }> {
    let deletedCount = 0
    let freedSpace = 0

    try {
      const backups = await this.listBackups()
      const cutoffDate = new Date(Date.now() - this.defaultConfig.retentionDays * 24 * 60 * 60 * 1000)

      for (const backup of backups) {
        if (backup.timestamp < cutoffDate) {
          const deleteResult = await this.deleteBackup(backup.id)
          if (deleteResult.success) {
            deletedCount++
            freedSpace += backup.size
          }
        }
      }

      if (deletedCount > 0) {
        await prisma.taskLog.create({
          data: {
            level: 'INFO',
            message: `清理了 ${deletedCount} 个过期备份，释放空间 ${freedSpace} bytes`,
            timestamp: new Date(),
            metadata: {
              source: 'data_backup_service',
              action: 'cleanup_completed',
              deletedCount,
              freedSpace
            }
          }
        })
      }

    } catch (error) {
      console.error('清理过期备份失败:', error)
    }

    return { deletedCount, freedSpace }
  }

  // 私有方法

  private async ensureBackupDirectory(): Promise<void> {
    try {
      await fs.mkdir(this.backupDir, { recursive: true })
    } catch (error) {
      console.error('创建备份目录失败:', error)
    }
  }

  private async backupDatabase(backupPath: string, config: BackupConfig): Promise<{
    tables: string[]
    size: number
  }> {
    const dbBackupPath = path.join(backupPath, 'database')
    await fs.mkdir(dbBackupPath, { recursive: true })

    const tables = ['Task', 'TaskLog', 'File']
    let totalSize = 0

    for (const tableName of tables) {
      try {
        let data: any[]
        
        switch (tableName) {
          case 'Task':
            data = await prisma.task.findMany()
            break
          case 'TaskLog':
            if (config.includeLogs) {
              data = await prisma.taskLog.findMany()
            } else {
              data = []
            }
            break
          case 'File':
            data = await prisma.file.findMany()
            break
          default:
            data = []
        }

        const tableData = JSON.stringify(data, null, 2)
        const tablePath = path.join(dbBackupPath, `${tableName}.json`)
        await fs.writeFile(tablePath, tableData, 'utf8')
        
        const stats = await fs.stat(tablePath)
        totalSize += stats.size

      } catch (error) {
        console.error(`备份表 ${tableName} 失败:`, error)
      }
    }

    return { tables, size: totalSize }
  }

  private async backupFiles(backupPath: string): Promise<{
    fileCount: number
    size: number
  }> {
    const filesBackupPath = path.join(backupPath, 'files')
    await fs.mkdir(filesBackupPath, { recursive: true })

    let fileCount = 0
    let totalSize = 0

    try {
      const uploadsDir = path.join(process.cwd(), 'uploads')
      const files = await fs.readdir(uploadsDir)

      for (const file of files) {
        try {
          const sourcePath = path.join(uploadsDir, file)
          const targetPath = path.join(filesBackupPath, file)
          
          await fs.copyFile(sourcePath, targetPath)
          
          const stats = await fs.stat(targetPath)
          totalSize += stats.size
          fileCount++

        } catch (error) {
          console.warn(`备份文件 ${file} 失败:`, error)
        }
      }

    } catch (error) {
      console.warn('备份文件目录失败:', error)
    }

    return { fileCount, size: totalSize }
  }

  private async restoreDatabase(
    backupPath: string,
    tables: string[],
    overwrite: boolean
  ): Promise<{
    tables: string[]
  }> {
    const dbBackupPath = path.join(backupPath, 'database')
    const restoredTables: string[] = []

    for (const tableName of tables) {
      try {
        const tablePath = path.join(dbBackupPath, `${tableName}.json`)
        const tableData = await fs.readFile(tablePath, 'utf8')
        const records = JSON.parse(tableData)

        if (overwrite) {
          // 清空现有数据
          switch (tableName) {
            case 'Task':
              await prisma.task.deleteMany()
              break
            case 'TaskLog':
              await prisma.taskLog.deleteMany()
              break
            case 'File':
              await prisma.file.deleteMany()
              break
          }
        }

        // 恢复数据
        for (const record of records) {
          try {
            switch (tableName) {
              case 'Task':
                await prisma.task.create({ data: record })
                break
              case 'TaskLog':
                await prisma.taskLog.create({ data: record })
                break
              case 'File':
                await prisma.file.create({ data: record })
                break
            }
          } catch (error) {
            if (!overwrite && error.code === 'P2002') {
              // 唯一约束冲突，跳过
              continue
            }
            throw error
          }
        }

        restoredTables.push(tableName)

      } catch (error) {
        console.error(`恢复表 ${tableName} 失败:`, error)
      }
    }

    return { tables: restoredTables }
  }

  private async restoreFiles(
    backupPath: string,
    overwrite: boolean
  ): Promise<{
    fileCount: number
  }> {
    const filesBackupPath = path.join(backupPath, 'files')
    let fileCount = 0

    try {
      const files = await fs.readdir(filesBackupPath)
      const uploadsDir = path.join(process.cwd(), 'uploads')
      
      await fs.mkdir(uploadsDir, { recursive: true })

      for (const file of files) {
        try {
          const sourcePath = path.join(filesBackupPath, file)
          const targetPath = path.join(uploadsDir, file)

          // 检查文件是否已存在
          if (!overwrite) {
            try {
              await fs.access(targetPath)
              continue // 文件已存在，跳过
            } catch {
              // 文件不存在，继续恢复
            }
          }

          await fs.copyFile(sourcePath, targetPath)
          fileCount++

        } catch (error) {
          console.warn(`恢复文件 ${file} 失败:`, error)
        }
      }

    } catch (error) {
      console.warn('恢复文件目录失败:', error)
    }

    return { fileCount }
  }

  private async calculateChecksum(backupPath: string): Promise<string> {
    // 简化的校验和计算
    const crypto = require('crypto')
    const hash = crypto.createHash('sha256')
    
    try {
      const files = await this.getAllFiles(backupPath)
      
      for (const file of files.sort()) {
        const content = await fs.readFile(file)
        hash.update(content)
      }
      
      return hash.digest('hex')
    } catch (error) {
      console.warn('计算校验和失败:', error)
      return 'unknown'
    }
  }

  private async getAllFiles(dir: string): Promise<string[]> {
    const files: string[] = []
    const items = await fs.readdir(dir, { withFileTypes: true })
    
    for (const item of items) {
      const fullPath = path.join(dir, item.name)
      if (item.isDirectory()) {
        files.push(...await this.getAllFiles(fullPath))
      } else {
        files.push(fullPath)
      }
    }
    
    return files
  }

  private async compressBackup(backupPath: string): Promise<void> {
    // 简化的压缩实现
    console.log(`压缩备份: ${backupPath}`)
    // 实际实现中可以使用 tar 或 zip 库
  }

  private async decompressBackup(backupPath: string): Promise<void> {
    // 简化的解压实现
    console.log(`解压备份: ${backupPath}`)
    // 实际实现中可以使用 tar 或 zip 库
  }

  private async saveBackupMetadata(backupPath: string, metadata: BackupMetadata): Promise<void> {
    const metadataPath = path.join(backupPath, 'metadata.json')
    await fs.writeFile(metadataPath, JSON.stringify(metadata, null, 2), 'utf8')
  }

  private async loadBackupMetadata(backupPath: string): Promise<BackupMetadata | null> {
    try {
      const metadataPath = path.join(backupPath, 'metadata.json')
      const content = await fs.readFile(metadataPath, 'utf8')
      const metadata = JSON.parse(content)
      
      // 转换日期字符串为Date对象
      metadata.timestamp = new Date(metadata.timestamp)
      
      return metadata
    } catch (error) {
      return null
    }
  }

  private async validateBackup(backupPath: string, metadata: BackupMetadata): Promise<boolean> {
    try {
      // 验证校验和
      const currentChecksum = await this.calculateChecksum(backupPath)
      if (currentChecksum !== metadata.checksum && metadata.checksum !== 'unknown') {
        console.error('备份校验和不匹配')
        return false
      }

      // 验证必要文件存在
      const metadataPath = path.join(backupPath, 'metadata.json')
      const dbPath = path.join(backupPath, 'database')
      
      await fs.access(metadataPath)
      await fs.access(dbPath)

      if (metadata.config.includeFiles) {
        const filesPath = path.join(backupPath, 'files')
        await fs.access(filesPath)
      }

      return true

    } catch (error) {
      console.error('备份验证失败:', error)
      return false
    }
  }
}

// 导出单例实例
export const dataBackupService = DataBackupService.getInstance()