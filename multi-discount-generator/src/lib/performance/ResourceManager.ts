import { EventEmitter } from 'events'
import { performanceMonitor } from './PerformanceMonitor'

interface ResourceLimits {
  maxMemoryUsage: number // bytes
  maxConcurrentTasks: number
  maxFileSize: number // bytes
  maxQueueLength: number
  maxEventLoopDelay: number // ms
}

interface ResourceUsage {
  memoryUsage: NodeJS.MemoryUsage
  activeTasks: number
  queueLength: number
  eventLoopDelay: number
  fileSystemUsage: {
    uploadDir: number
    outputDir: number
    tempDir: number
  }
}

/**
 * 资源管理服务
 * 负责监控和管理系统资源使用
 */
export class ResourceManager extends EventEmitter {
  private static instance: ResourceManager
  private limits: ResourceLimits
  private monitoringInterval: NodeJS.Timeout | null = null
  private gcInterval: NodeJS.Timeout | null = null

  private constructor() {
    super()
    
    // 设置默认资源限制
    this.limits = {
      maxMemoryUsage: parseInt(process.env.MAX_MEMORY_USAGE || '1073741824'), // 1GB
      maxConcurrentTasks: parseInt(process.env.MAX_CONCURRENT_TASKS || '5'),
      maxFileSize: parseInt(process.env.MAX_FILE_SIZE || '104857600'), // 100MB
      maxQueueLength: parseInt(process.env.MAX_QUEUE_LENGTH || '100'),
      maxEventLoopDelay: parseInt(process.env.MAX_EVENT_LOOP_DELAY || '100') // 100ms
    }

    this.startResourceMonitoring()
  }

  static getInstance(): ResourceManager {
    if (!ResourceManager.instance) {
      ResourceManager.instance = new ResourceManager()
    }
    return ResourceManager.instance
  }

  /**
   * 开始资源监控
   */
  private startResourceMonitoring(): void {
    // 每30秒检查一次资源使用情况
    this.monitoringInterval = setInterval(() => {
      this.checkResourceUsage()
    }, 30000)

    // 每5分钟执行一次垃圾回收
    this.gcInterval = setInterval(() => {
      this.performGarbageCollection()
    }, 300000)

    console.log('资源监控已启动')
  }

  /**
   * 停止资源监控
   */
  stopResourceMonitoring(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval)
      this.monitoringInterval = null
    }

    if (this.gcInterval) {
      clearInterval(this.gcInterval)
      this.gcInterval = null
    }

    console.log('资源监控已停止')
  }

  /**
   * 获取当前资源使用情况
   */
  async getCurrentResourceUsage(): Promise<ResourceUsage> {
    const memoryUsage = process.memoryUsage()
    const fileSystemUsage = await this.getFileSystemUsage()

    // 获取队列长度（需要从任务队列服务获取）
    let queueLength = 0
    let activeTasks = 0
    try {
      const { taskQueue } = await import('@/lib/queue')
      const queueStatus = await taskQueue.getStats()
      queueLength = queueStatus.queueLength
      activeTasks = queueStatus.activeWorkers
    } catch (error) {
      console.warn('无法获取队列状态:', error)
    }

    // 测量事件循环延迟
    const eventLoopDelay = await this.measureEventLoopDelay()

    return {
      memoryUsage,
      activeTasks,
      queueLength,
      eventLoopDelay,
      fileSystemUsage
    }
  }

  /**
   * 检查资源使用情况
   */
  private async checkResourceUsage(): Promise<void> {
    const usage = await this.getCurrentResourceUsage()

    // 检查内存使用
    if (usage.memoryUsage.heapUsed > this.limits.maxMemoryUsage) {
      this.emit('resourceLimitExceeded', {
        type: 'memory',
        current: usage.memoryUsage.heapUsed,
        limit: this.limits.maxMemoryUsage,
        severity: 'high'
      })
      
      // 触发垃圾回收
      this.performGarbageCollection()
    }

    // 检查并发任务数
    if (usage.activeTasks > this.limits.maxConcurrentTasks) {
      this.emit('resourceLimitExceeded', {
        type: 'concurrency',
        current: usage.activeTasks,
        limit: this.limits.maxConcurrentTasks,
        severity: 'medium'
      })
    }

    // 检查队列长度
    if (usage.queueLength > this.limits.maxQueueLength) {
      this.emit('resourceLimitExceeded', {
        type: 'queue',
        current: usage.queueLength,
        limit: this.limits.maxQueueLength,
        severity: 'medium'
      })
    }

    // 检查事件循环延迟
    if (usage.eventLoopDelay > this.limits.maxEventLoopDelay) {
      this.emit('resourceLimitExceeded', {
        type: 'eventLoop',
        current: usage.eventLoopDelay,
        limit: this.limits.maxEventLoopDelay,
        severity: 'high'
      })
    }

    // 记录资源使用指标
    performanceMonitor.recordMetric('resource_memory_usage', usage.memoryUsage.heapUsed, 'bytes')
    performanceMonitor.recordMetric('resource_active_tasks', usage.activeTasks, 'count')
    performanceMonitor.recordMetric('resource_queue_length', usage.queueLength, 'count')
    performanceMonitor.recordMetric('resource_event_loop_delay', usage.eventLoopDelay, 'ms')
  }

  /**
   * 测量事件循环延迟
   */
  private measureEventLoopDelay(): Promise<number> {
    return new Promise((resolve) => {
      const start = process.hrtime.bigint()
      setImmediate(() => {
        const delay = Number(process.hrtime.bigint() - start) / 1000000 // 转换为毫秒
        resolve(delay)
      })
    })
  }

  /**
   * 获取文件系统使用情况
   */
  private async getFileSystemUsage(): Promise<{
    uploadDir: number
    outputDir: number
    tempDir: number
  }> {
    const fs = require('fs')
    const path = require('path')

    const calculateDirSize = (dirPath: string): number => {
      let size = 0
      try {
        if (!fs.existsSync(dirPath)) return 0
        
        const files = fs.readdirSync(dirPath)
        files.forEach((file: string) => {
          const filePath = path.join(dirPath, file)
          const stats = fs.statSync(filePath)
          if (stats.isDirectory()) {
            size += calculateDirSize(filePath)
          } else {
            size += stats.size
          }
        })
      } catch (error) {
        console.warn(`计算目录大小失败: ${dirPath}`, error)
      }
      return size
    }

    const uploadDir = calculateDirSize(path.join(process.cwd(), 'uploads', 'input'))
    const outputDir = calculateDirSize(path.join(process.cwd(), 'uploads', 'output'))
    const tempDir = calculateDirSize(path.join(process.cwd(), 'uploads', 'temp'))

    return { uploadDir, outputDir, tempDir }
  }

  /**
   * 执行垃圾回收
   */
  private performGarbageCollection(): void {
    if (global.gc) {
      console.log('执行垃圾回收...')
      const beforeMemory = process.memoryUsage()
      
      global.gc()
      
      const afterMemory = process.memoryUsage()
      const freed = beforeMemory.heapUsed - afterMemory.heapUsed
      
      console.log(`垃圾回收完成，释放内存: ${Math.round(freed / 1024 / 1024)}MB`)
      
      performanceMonitor.recordMetric('gc_memory_freed', freed, 'bytes')
      this.emit('garbageCollected', { freed, beforeMemory, afterMemory })
    } else {
      console.warn('垃圾回收不可用，请使用 --expose-gc 标志启动应用')
    }
  }

  /**
   * 清理过期文件
   */
  async cleanupExpiredFiles(maxAge: number = 24 * 60 * 60 * 1000): Promise<void> {
    const fs = require('fs')
    const path = require('path')

    const directories = [
      path.join(process.cwd(), 'uploads', 'temp'),
      path.join(process.cwd(), 'uploads', 'input'),
      path.join(process.cwd(), 'uploads', 'output')
    ]

    let totalCleaned = 0
    let totalSize = 0

    for (const dir of directories) {
      try {
        if (!fs.existsSync(dir)) continue

        const files = fs.readdirSync(dir)
        const now = Date.now()

        for (const file of files) {
          const filePath = path.join(dir, file)
          const stats = fs.statSync(filePath)
          
          if (now - stats.mtime.getTime() > maxAge) {
            totalSize += stats.size
            fs.unlinkSync(filePath)
            totalCleaned++
          }
        }
      } catch (error) {
        console.error(`清理目录失败: ${dir}`, error)
      }
    }

    if (totalCleaned > 0) {
      console.log(`清理了 ${totalCleaned} 个过期文件，释放空间: ${Math.round(totalSize / 1024 / 1024)}MB`)
      performanceMonitor.recordMetric('files_cleaned', totalCleaned, 'count')
      performanceMonitor.recordMetric('disk_space_freed', totalSize, 'bytes')
      this.emit('filesCleanedUp', { count: totalCleaned, size: totalSize })
    }
  }

  /**
   * 检查是否可以执行新任务
   */
  async canExecuteNewTask(): Promise<{ allowed: boolean; reason?: string }> {
    const usage = await this.getCurrentResourceUsage()

    // 检查内存使用
    if (usage.memoryUsage.heapUsed > this.limits.maxMemoryUsage * 0.9) {
      return {
        allowed: false,
        reason: '内存使用率过高，暂时无法执行新任务'
      }
    }

    // 检查并发任务数
    if (usage.activeTasks >= this.limits.maxConcurrentTasks) {
      return {
        allowed: false,
        reason: '已达到最大并发任务数限制'
      }
    }

    // 检查队列长度
    if (usage.queueLength >= this.limits.maxQueueLength) {
      return {
        allowed: false,
        reason: '任务队列已满'
      }
    }

    // 检查事件循环延迟
    if (usage.eventLoopDelay > this.limits.maxEventLoopDelay) {
      return {
        allowed: false,
        reason: '系统负载过高，事件循环延迟严重'
      }
    }

    return { allowed: true }
  }

  /**
   * 优化系统性能
   */
  async optimizePerformance(): Promise<void> {
    console.log('开始性能优化...')

    // 执行垃圾回收
    this.performGarbageCollection()

    // 清理过期文件
    await this.cleanupExpiredFiles()

    // 优化事件循环
    this.optimizeEventLoop()

    console.log('性能优化完成')
    this.emit('performanceOptimized')
  }

  /**
   * 优化事件循环
   */
  private optimizeEventLoop(): void {
    // 设置更高的事件循环优先级
    if (process.platform !== 'win32') {
      try {
        process.setpriority(0, -10) // 提高进程优先级
      } catch (error) {
        console.warn('无法设置进程优先级:', error)
      }
    }

    // 调整UV线程池大小
    if (!process.env.UV_THREADPOOL_SIZE) {
      process.env.UV_THREADPOOL_SIZE = '16'
    }
  }

  /**
   * 获取资源限制
   */
  getResourceLimits(): ResourceLimits {
    return { ...this.limits }
  }

  /**
   * 更新资源限制
   */
  updateResourceLimits(newLimits: Partial<ResourceLimits>): void {
    this.limits = { ...this.limits, ...newLimits }
    console.log('资源限制已更新:', this.limits)
    this.emit('limitsUpdated', this.limits)
  }

  /**
   * 生成资源使用报告
   */
  async generateResourceReport(): Promise<{
    current: ResourceUsage
    limits: ResourceLimits
    utilization: {
      memory: number
      concurrency: number
      queue: number
      eventLoop: number
    }
    recommendations: string[]
  }> {
    const current = await this.getCurrentResourceUsage()
    const recommendations: string[] = []

    const utilization = {
      memory: (current.memoryUsage.heapUsed / this.limits.maxMemoryUsage) * 100,
      concurrency: (current.activeTasks / this.limits.maxConcurrentTasks) * 100,
      queue: (current.queueLength / this.limits.maxQueueLength) * 100,
      eventLoop: (current.eventLoopDelay / this.limits.maxEventLoopDelay) * 100
    }

    // 生成建议
    if (utilization.memory > 80) {
      recommendations.push('内存使用率较高，建议增加内存限制或优化内存使用')
    }

    if (utilization.concurrency > 80) {
      recommendations.push('并发任务数接近上限，建议增加并发限制或优化任务处理')
    }

    if (utilization.queue > 80) {
      recommendations.push('任务队列接近满载，建议增加处理能力或队列容量')
    }

    if (utilization.eventLoop > 80) {
      recommendations.push('事件循环延迟较高，建议减少同步操作或增加异步处理')
    }

    return {
      current,
      limits: this.limits,
      utilization,
      recommendations
    }
  }

  /**
   * 清理资源
   */
  cleanup(): void {
    this.stopResourceMonitoring()
    this.removeAllListeners()
  }
}

// 导出单例实例
export const resourceManager = ResourceManager.getInstance()