import { EventEmitter } from 'events'
import { prisma } from '@/lib/prisma'

interface PerformanceMetric {
  name: string
  value: number
  unit: string
  timestamp: Date
  metadata?: Record<string, any>
}

interface TaskPerformanceData {
  taskId: string
  startTime: number
  endTime?: number
  duration?: number
  itemsProcessed: number
  throughput?: number // items per second
  memoryUsage: {
    start: NodeJS.MemoryUsage
    peak: NodeJS.MemoryUsage
    end?: NodeJS.MemoryUsage
  }
  errors: number
  retries: number
}

/**
 * 性能监控服务
 * 负责收集和分析系统性能指标
 */
export class PerformanceMonitor extends EventEmitter {
  private static instance: PerformanceMonitor
  private metrics: Map<string, PerformanceMetric[]> = new Map()
  private taskPerformance: Map<string, TaskPerformanceData> = new Map()
  private maxMetricsHistory = 1000
  private monitoringInterval: NodeJS.Timeout | null = null

  private constructor() {
    super()
    this.startMonitoring()
  }

  static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor()
    }
    return PerformanceMonitor.instance
  }

  /**
   * 开始性能监控
   */
  private startMonitoring(): void {
    // 每10秒收集一次系统指标
    this.monitoringInterval = setInterval(() => {
      this.collectSystemMetrics()
    }, 10000)

    console.log('性能监控已启动')
  }

  /**
   * 停止性能监控
   */
  stopMonitoring(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval)
      this.monitoringInterval = null
    }
    console.log('性能监控已停止')
  }

  /**
   * 记录性能指标
   */
  recordMetric(name: string, value: number, unit: string, metadata?: Record<string, any>): void {
    const metric: PerformanceMetric = {
      name,
      value,
      unit,
      timestamp: new Date(),
      metadata
    }

    if (!this.metrics.has(name)) {
      this.metrics.set(name, [])
    }

    const metricHistory = this.metrics.get(name)!
    metricHistory.push(metric)

    // 保持历史记录大小限制
    if (metricHistory.length > this.maxMetricsHistory) {
      metricHistory.shift()
    }

    this.emit('metricRecorded', metric)
  }

  /**
   * 开始任务性能监控
   */
  startTaskMonitoring(taskId: string): void {
    const performanceData: TaskPerformanceData = {
      taskId,
      startTime: Date.now(),
      itemsProcessed: 0,
      memoryUsage: {
        start: process.memoryUsage(),
        peak: process.memoryUsage()
      },
      errors: 0,
      retries: 0
    }

    this.taskPerformance.set(taskId, performanceData)
    this.recordMetric('task_started', 1, 'count', { taskId })
  }

  /**
   * 更新任务进度
   */
  updateTaskProgress(taskId: string, itemsProcessed: number, errors: number = 0, retries: number = 0): void {
    const performanceData = this.taskPerformance.get(taskId)
    if (!performanceData) return

    performanceData.itemsProcessed = itemsProcessed
    performanceData.errors = errors
    performanceData.retries = retries

    // 更新峰值内存使用
    const currentMemory = process.memoryUsage()
    if (currentMemory.heapUsed > performanceData.memoryUsage.peak.heapUsed) {
      performanceData.memoryUsage.peak = currentMemory
    }

    // 计算当前吞吐量
    const elapsed = (Date.now() - performanceData.startTime) / 1000
    if (elapsed > 0) {
      performanceData.throughput = itemsProcessed / elapsed
    }

    this.recordMetric('task_throughput', performanceData.throughput || 0, 'items/sec', { taskId })
    this.recordMetric('task_memory_usage', currentMemory.heapUsed, 'bytes', { taskId })
  }

  /**
   * 完成任务性能监控
   */
  finishTaskMonitoring(taskId: string): TaskPerformanceData | null {
    const performanceData = this.taskPerformance.get(taskId)
    if (!performanceData) return null

    performanceData.endTime = Date.now()
    performanceData.duration = performanceData.endTime - performanceData.startTime
    performanceData.memoryUsage.end = process.memoryUsage()

    // 计算最终吞吐量
    if (performanceData.duration > 0) {
      performanceData.throughput = performanceData.itemsProcessed / (performanceData.duration / 1000)
    }

    // 记录最终指标
    this.recordMetric('task_completed', 1, 'count', { taskId })
    this.recordMetric('task_duration', performanceData.duration, 'ms', { taskId })
    this.recordMetric('task_final_throughput', performanceData.throughput || 0, 'items/sec', { taskId })
    this.recordMetric('task_error_rate', performanceData.errors / performanceData.itemsProcessed, 'ratio', { taskId })

    this.emit('taskCompleted', performanceData)
    this.taskPerformance.delete(taskId)

    return performanceData
  }

  /**
   * 收集系统指标
   */
  private collectSystemMetrics(): void {
    const memoryUsage = process.memoryUsage()
    const cpuUsage = process.cpuUsage()

    // 内存指标
    this.recordMetric('system_memory_heap_used', memoryUsage.heapUsed, 'bytes')
    this.recordMetric('system_memory_heap_total', memoryUsage.heapTotal, 'bytes')
    this.recordMetric('system_memory_rss', memoryUsage.rss, 'bytes')

    // CPU指标
    this.recordMetric('system_cpu_user', cpuUsage.user, 'microseconds')
    this.recordMetric('system_cpu_system', cpuUsage.system, 'microseconds')

    // 事件循环延迟
    const start = process.hrtime.bigint()
    setImmediate(() => {
      const delay = Number(process.hrtime.bigint() - start) / 1000000 // 转换为毫秒
      this.recordMetric('event_loop_delay', delay, 'ms')
    })

    // 活跃句柄数
    const activeHandles = (process as any)._getActiveHandles?.()?.length || 0
    this.recordMetric('active_handles', activeHandles, 'count')
  }

  /**
   * 获取指标历史
   */
  getMetricHistory(name: string, limit?: number): PerformanceMetric[] {
    const history = this.metrics.get(name) || []
    return limit ? history.slice(-limit) : history
  }

  /**
   * 获取所有指标名称
   */
  getMetricNames(): string[] {
    return Array.from(this.metrics.keys())
  }

  /**
   * 获取任务性能统计
   */
  getTaskPerformanceStats(taskId?: string): TaskPerformanceData[] {
    if (taskId) {
      const data = this.taskPerformance.get(taskId)
      return data ? [data] : []
    }
    return Array.from(this.taskPerformance.values())
  }

  /**
   * 分析性能趋势
   */
  analyzePerformanceTrends(metricName: string, timeWindow: number = 3600000): {
    trend: 'increasing' | 'decreasing' | 'stable'
    average: number
    min: number
    max: number
    variance: number
  } {
    const cutoffTime = Date.now() - timeWindow
    const metrics = this.getMetricHistory(metricName)
      .filter(m => m.timestamp.getTime() > cutoffTime)

    if (metrics.length < 2) {
      return {
        trend: 'stable',
        average: 0,
        min: 0,
        max: 0,
        variance: 0
      }
    }

    const values = metrics.map(m => m.value)
    const average = values.reduce((sum, val) => sum + val, 0) / values.length
    const min = Math.min(...values)
    const max = Math.max(...values)
    
    // 计算方差
    const variance = values.reduce((sum, val) => sum + Math.pow(val - average, 2), 0) / values.length

    // 简单趋势分析：比较前半部分和后半部分的平均值
    const midPoint = Math.floor(values.length / 2)
    const firstHalf = values.slice(0, midPoint)
    const secondHalf = values.slice(midPoint)
    
    const firstAvg = firstHalf.reduce((sum, val) => sum + val, 0) / firstHalf.length
    const secondAvg = secondHalf.reduce((sum, val) => sum + val, 0) / secondHalf.length
    
    const threshold = average * 0.05 // 5% 阈值
    let trend: 'increasing' | 'decreasing' | 'stable'
    
    if (secondAvg > firstAvg + threshold) {
      trend = 'increasing'
    } else if (secondAvg < firstAvg - threshold) {
      trend = 'decreasing'
    } else {
      trend = 'stable'
    }

    return { trend, average, min, max, variance }
  }

  /**
   * 检测性能异常
   */
  detectPerformanceAnomalies(): {
    metric: string
    type: 'spike' | 'drop' | 'high_variance'
    severity: 'low' | 'medium' | 'high'
    description: string
    value: number
    threshold: number
  }[] {
    const anomalies: any[] = []
    const criticalMetrics = [
      'system_memory_heap_used',
      'event_loop_delay',
      'task_throughput',
      'task_error_rate'
    ]

    for (const metricName of criticalMetrics) {
      const analysis = this.analyzePerformanceTrends(metricName)
      const recentMetrics = this.getMetricHistory(metricName, 10)
      
      if (recentMetrics.length === 0) continue

      const latestValue = recentMetrics[recentMetrics.length - 1].value

      // 检测内存使用异常
      if (metricName === 'system_memory_heap_used') {
        const memoryThreshold = 500 * 1024 * 1024 // 500MB
        if (latestValue > memoryThreshold) {
          anomalies.push({
            metric: metricName,
            type: 'spike',
            severity: latestValue > memoryThreshold * 2 ? 'high' : 'medium',
            description: '内存使用量过高',
            value: latestValue,
            threshold: memoryThreshold
          })
        }
      }

      // 检测事件循环延迟异常
      if (metricName === 'event_loop_delay') {
        const delayThreshold = 100 // 100ms
        if (latestValue > delayThreshold) {
          anomalies.push({
            metric: metricName,
            type: 'spike',
            severity: latestValue > delayThreshold * 5 ? 'high' : 'medium',
            description: '事件循环延迟过高',
            value: latestValue,
            threshold: delayThreshold
          })
        }
      }

      // 检测吞吐量异常
      if (metricName === 'task_throughput' && analysis.average > 0) {
        const throughputThreshold = analysis.average * 0.5
        if (latestValue < throughputThreshold) {
          anomalies.push({
            metric: metricName,
            type: 'drop',
            severity: latestValue < throughputThreshold * 0.5 ? 'high' : 'medium',
            description: '任务吞吐量显著下降',
            value: latestValue,
            threshold: throughputThreshold
          })
        }
      }

      // 检测错误率异常
      if (metricName === 'task_error_rate') {
        const errorThreshold = 0.05 // 5%
        if (latestValue > errorThreshold) {
          anomalies.push({
            metric: metricName,
            type: 'spike',
            severity: latestValue > errorThreshold * 2 ? 'high' : 'medium',
            description: '任务错误率过高',
            value: latestValue,
            threshold: errorThreshold
          })
        }
      }

      // 检测高方差（不稳定）
      if (analysis.variance > analysis.average * 0.5 && analysis.average > 0) {
        anomalies.push({
          metric: metricName,
          type: 'high_variance',
          severity: 'low',
          description: '指标波动较大，系统不稳定',
          value: analysis.variance,
          threshold: analysis.average * 0.5
        })
      }
    }

    return anomalies
  }

  /**
   * 生成性能报告
   */
  generatePerformanceReport(timeWindow: number = 3600000): {
    summary: {
      totalTasks: number
      averageThroughput: number
      averageMemoryUsage: number
      averageEventLoopDelay: number
      errorRate: number
    }
    trends: Record<string, any>
    anomalies: any[]
    recommendations: string[]
  } {
    const anomalies = this.detectPerformanceAnomalies()
    const recommendations: string[] = []

    // 分析关键指标趋势
    const trends = {
      memory: this.analyzePerformanceTrends('system_memory_heap_used', timeWindow),
      throughput: this.analyzePerformanceTrends('task_throughput', timeWindow),
      eventLoop: this.analyzePerformanceTrends('event_loop_delay', timeWindow),
      errorRate: this.analyzePerformanceTrends('task_error_rate', timeWindow)
    }

    // 生成建议
    if (trends.memory.trend === 'increasing') {
      recommendations.push('内存使用量持续增长，建议检查内存泄漏')
    }

    if (trends.throughput.trend === 'decreasing') {
      recommendations.push('任务吞吐量下降，建议优化处理逻辑或增加并发数')
    }

    if (trends.eventLoop.average > 50) {
      recommendations.push('事件循环延迟较高，建议减少同步操作')
    }

    if (trends.errorRate.average > 0.02) {
      recommendations.push('错误率较高，建议检查错误处理逻辑')
    }

    // 计算汇总统计
    const taskMetrics = this.getMetricHistory('task_completed')
    const throughputMetrics = this.getMetricHistory('task_throughput')
    const memoryMetrics = this.getMetricHistory('system_memory_heap_used')
    const eventLoopMetrics = this.getMetricHistory('event_loop_delay')
    const errorMetrics = this.getMetricHistory('task_error_rate')

    const summary = {
      totalTasks: taskMetrics.length,
      averageThroughput: throughputMetrics.length > 0 
        ? throughputMetrics.reduce((sum, m) => sum + m.value, 0) / throughputMetrics.length 
        : 0,
      averageMemoryUsage: memoryMetrics.length > 0 
        ? memoryMetrics.reduce((sum, m) => sum + m.value, 0) / memoryMetrics.length 
        : 0,
      averageEventLoopDelay: eventLoopMetrics.length > 0 
        ? eventLoopMetrics.reduce((sum, m) => sum + m.value, 0) / eventLoopMetrics.length 
        : 0,
      errorRate: errorMetrics.length > 0 
        ? errorMetrics.reduce((sum, m) => sum + m.value, 0) / errorMetrics.length 
        : 0
    }

    return {
      summary,
      trends,
      anomalies,
      recommendations
    }
  }

  /**
   * 清理资源
   */
  cleanup(): void {
    this.stopMonitoring()
    this.metrics.clear()
    this.taskPerformance.clear()
    this.removeAllListeners()
  }
}

// 导出单例实例
export const performanceMonitor = PerformanceMonitor.getInstance()