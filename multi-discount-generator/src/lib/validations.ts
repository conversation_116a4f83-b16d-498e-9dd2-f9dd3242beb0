import { z } from 'zod'
import { 
  REGIONAL_AREAS, 
  VALIDATION_RULES, 
  ERROR_MESSAGES,
  DISCOUNT_OPTIONS,
  QUANTITY_OPTIONS 
} from '@/constants'
import { format, isAfter, isBefore, parseISO } from 'date-fns'
import { TIME_FORMAT } from '@/constants'

// 区域验证模式
const regionalAreaSchema = z.enum(REGIONAL_AREAS)

const regionSchema = z.discriminatedUnion('type', [
  z.object({
    type: z.literal('national')
  }),
  z.object({
    type: z.literal('regional'),
    regions: z.array(regionalAreaSchema).min(1, '请至少选择一个区域')
  })
])

// 时间验证辅助函数
const timeStringSchema = z.string().refine(
  (time) => {
    try {
      const parsed = parseISO(`2025-01-01 ${time}`)
      return !isNaN(parsed.getTime())
    } catch {
      return false
    }
  },
  { message: '时间格式不正确，请使用 HH:mm:ss 格式' }
)

const dateTimeSchema = z.date().or(
  z.string().transform((str) => {
    const date = parseISO(str)
    if (isNaN(date.getTime())) {
      throw new Error('无效的日期时间格式')
    }
    return date
  })
)

// 跨天时间验证
const crossDayTimeSchema = z.object({
  startTime: dateTimeSchema,
  endTime: dateTimeSchema
}).refine(
  (data) => {
    // 允许跨天时间段（结束时间可以小于开始时间）
    return true
  },
  { message: '时间段配置无效' }
)

// 连续模式配置验证
const continuousConfigSchema = z.object({
  startTime: dateTimeSchema,
  endTime: dateTimeSchema,
  days: z.number()
    .min(VALIDATION_RULES.DAYS.MIN, `天数不能少于${VALIDATION_RULES.DAYS.MIN}天`)
    .max(VALIDATION_RULES.DAYS.MAX, `天数不能超过${VALIDATION_RULES.DAYS.MAX}天`),
  weekendFullDay: z.boolean()
})

// 随机模式配置验证
const randomConfigSchema = z.object({
  startTime: dateTimeSchema,
  slotCount: z.number()
    .min(VALIDATION_RULES.TIME_SLOT_COUNT.MIN, '时间段数量至少为1')
    .max(VALIDATION_RULES.TIME_SLOT_COUNT.MAX, '时间段数量不能超过50'),
  slotDuration: z.union([z.literal(1), z.literal(2)])
})

// 主表单验证模式
export const formDataSchema = z.object({
  // 商品ID验证
  productId: z.string()
    .min(VALIDATION_RULES.PRODUCT_ID.MIN_LENGTH, ERROR_MESSAGES.REQUIRED_FIELD)
    .max(VALIDATION_RULES.PRODUCT_ID.MAX_LENGTH, '商品ID长度不能超过50个字符')
    .regex(/^[a-zA-Z0-9_-]+$/, '商品ID只能包含字母、数字、下划线和连字符'),

  // 区域验证
  region: regionSchema,

  // 满件数量验证 (1-10)
  minQuantity: z.number()
    .min(VALIDATION_RULES.QUANTITY.MIN, ERROR_MESSAGES.INVALID_QUANTITY)
    .max(VALIDATION_RULES.QUANTITY.MAX, ERROR_MESSAGES.INVALID_QUANTITY)
    .refine((val) => QUANTITY_OPTIONS.includes(val), ERROR_MESSAGES.INVALID_QUANTITY),

  // 折扣验证 (5.0-9.9，步长0.1)
  discount: z.number()
    .min(VALIDATION_RULES.DISCOUNT.MIN, ERROR_MESSAGES.INVALID_DISCOUNT)
    .max(VALIDATION_RULES.DISCOUNT.MAX, ERROR_MESSAGES.INVALID_DISCOUNT)
    .refine(
      (val) => DISCOUNT_OPTIONS.includes(Number(val.toFixed(1))),
      ERROR_MESSAGES.INVALID_DISCOUNT
    ),

  // 时间模式
  timeMode: z.enum(['continuous', 'random']),

  // 连续模式字段
  startTime: dateTimeSchema.optional(),
  endTime: dateTimeSchema.optional(),
  days: z.number().optional(),
  weekendFullDay: z.boolean().optional(),

  // 随机模式字段
  randomStartTime: dateTimeSchema.optional(),
  timeSlotCount: z.number().optional(),
  slotDuration: z.union([z.literal(1), z.literal(2)]).optional()
}).superRefine((data, ctx) => {
  // 根据时间模式验证对应字段
  if (data.timeMode === 'continuous') {
    if (!data.startTime) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: '连续模式下开始时间为必填',
        path: ['startTime']
      })
    }
    if (!data.endTime) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: '连续模式下结束时间为必填',
        path: ['endTime']
      })
    }
    if (!data.days || data.days < 1) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: '连续模式下天数为必填且大于0',
        path: ['days']
      })
    }
  } else if (data.timeMode === 'random') {
    if (!data.randomStartTime) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: '随机模式下起始时间为必填',
        path: ['randomStartTime']
      })
    }
    if (!data.timeSlotCount || data.timeSlotCount < 1) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: '随机模式下时间段数量为必填且大于0',
        path: ['timeSlotCount']
      })
    }
    if (!data.slotDuration) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: '随机模式下时间段长度为必填',
        path: ['slotDuration']
      })
    }
  }
})

// 时间段验证模式
export const timeSlotSchema = z.object({
  startTime: dateTimeSchema,
  endTime: dateTimeSchema
}).refine(
  (data) => {
    // 对于跨天时间段，我们允许结束时间小于开始时间
    // 实际验证在业务逻辑中处理
    return true
  },
  { message: '时间段配置无效' }
)

// Excel行数据验证模式
export const excelRowDataSchema = z.object({
  活动类型: z.string().min(1),
  商品ID: z.string().min(1),
  '开始时间\n示例：\n2020-03-27 00:00:00': z.string().regex(
    /^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/,
    '开始时间格式必须为 YYYY-MM-DD HH:mm:ss'
  ),
  '结束时间\n示例：\n2020-03-27 23:59:59': z.string().regex(
    /^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/,
    '结束时间格式必须为 YYYY-MM-DD HH:mm:ss'
  ),
  '活动区域\n支持填写"全国"及5大区\n5大区：华中,西南/西北,华南,华东,华北': z.string().min(1),
  '满几件\n说明：表示满N件，件数只能在下拉框中选择': z.number().min(1).max(10),
  '折扣-打几折\n说明：表示打几折，折扣只能下拉选择': z.number().min(5.0).max(9.9),
  '立减金额-多件总额下的扣减金额\n表示扣减的金额，单位元，支持最多2位小数，与折扣只能择一生效': z.string()
})

// 批量验证辅助函数
export const validateFormData = (data: unknown) => {
  return formDataSchema.safeParse(data)
}

export const validateTimeSlot = (data: unknown) => {
  return timeSlotSchema.safeParse(data)
}

export const validateExcelRowData = (data: unknown) => {
  return excelRowDataSchema.safeParse(data)
}

// 自定义验证函数
export const validateTimeRange = (startTime: Date, endTime: Date, allowCrossDay = true) => {
  if (!allowCrossDay && isAfter(startTime, endTime)) {
    return {
      isValid: false,
      error: '结束时间必须晚于开始时间'
    }
  }
  
  return {
    isValid: true,
    error: null
  }
}

// 中国时区时间格式验证
export const validateChineseDateTime = (dateTime: string) => {
  try {
    const parsed = parseISO(dateTime)
    if (isNaN(parsed.getTime())) {
      return {
        isValid: false,
        error: '无效的日期时间格式'
      }
    }
    
    // 验证格式是否为 YYYY-MM-DD HH:mm:ss
    const formatted = format(parsed, TIME_FORMAT)
    if (formatted !== dateTime) {
      return {
        isValid: false,
        error: `时间格式必须为 ${TIME_FORMAT}`
      }
    }
    
    return {
      isValid: true,
      error: null,
      parsed
    }
  } catch (error) {
    return {
      isValid: false,
      error: '日期时间解析失败'
    }
  }
}

// 导出类型
export type FormDataInput = z.input<typeof formDataSchema>
export type FormDataOutput = z.output<typeof formDataSchema>
export type TimeSlotInput = z.input<typeof timeSlotSchema>
export type ExcelRowDataInput = z.input<typeof excelRowDataSchema>