import { FileUploadService } from '../FileUploadService'
import { FileValidationService } from '../FileValidationService'
import { TestDataGenerator } from '@/lib/testing/TestDataGenerator'
import { prisma } from '@/lib/prisma'

// Mock dependencies
jest.mock('@/lib/prisma', () => ({
  prisma: {
    file: {
      create: jest.fn(),
      findUnique: jest.fn(),
      findMany: jest.fn(),
      delete: jest.fn()
    }
  }
}))

jest.mock('../FileValidationService')
jest.mock('fs/promises')
jest.mock('path')

describe('FileUploadService', () => {
  const mockPrisma = prisma as jest.Mocked<typeof prisma>
  const mockFileValidation = FileValidationService as jest.Mocked<typeof FileValidationService>

  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('uploadFile', () => {
    it('should upload file successfully', async () => {
      const mockFile = {
        buffer: Buffer.from('test,data\nPROD001,商品1\nPROD002,商品2'),
        originalname: 'test.csv',
        mimetype: 'text/csv',
        size: 100
      }

      const mockFileRecord = {
        id: 'file-123',
        filename: 'test.csv',
        originalName: 'test.csv',
        size: 100,
        mimeType: 'text/csv',
        path: '/uploads/file-123.csv',
        createdAt: new Date()
      }

      mockFileValidation.validateFile.mockReturnValue({ isValid: true })
      mockPrisma.file.create.mockResolvedValue(mockFileRecord)

      const result = await FileUploadService.uploadFile(mockFile)

      expect(result.fileId).toBe('file-123')
      expect(result.path).toBe('/uploads/file-123.csv')
      expect(mockFileValidation.validateFile).toHaveBeenCalledWith(mockFile)
      expect(mockPrisma.file.create).toHaveBeenCalled()
    })

    it('should reject invalid file', async () => {
      const mockFile = {
        buffer: Buffer.from('invalid content'),
        originalname: 'test.txt',
        mimetype: 'text/plain',
        size: 100
      }

      mockFileValidation.validateFile.mockReturnValue({
        isValid: false,
        errors: ['不支持的文件类型']
      })

      await expect(FileUploadService.uploadFile(mockFile)).rejects.toThrow('文件验证失败')
    })

    it('should reject file that is too large', async () => {
      const mockFile = {
        buffer: Buffer.alloc(11 * 1024 * 1024), // 11MB
        originalname: 'large.csv',
        mimetype: 'text/csv',
        size: 11 * 1024 * 1024
      }

      mockFileValidation.validateFile.mockReturnValue({
        isValid: false,
        errors: ['文件大小超过限制']
      })

      await expect(FileUploadService.uploadFile(mockFile)).rejects.toThrow('文件验证失败')
    })
  })

  describe('parseProductFile', () => {
    it('should parse CSV file successfully', async () => {
      const fileId = 'file-123'
      const mockFile = {
        id: fileId,
        path: '/uploads/test.csv',
        mimeType: 'text/csv'
      }

      const csvContent = 'ID,名称\nPROD001,商品1\nPROD002,商品2\nPROD001,重复商品'

      mockPrisma.file.findUnique.mockResolvedValue(mockFile)
      
      // Mock fs.readFile
      const fs = require('fs/promises')
      fs.readFile = jest.fn().mockResolvedValue(csvContent)

      const result = await FileUploadService.parseProductFile(fileId)

      expect(result.products).toHaveLength(2) // 去重后应该只有2个
      expect(result.products).toContain('PROD001')
      expect(result.products).toContain('PROD002')
      expect(result.summary.total).toBe(3)
      expect(result.summary.valid).toBe(2)
      expect(result.summary.duplicates).toBe(1)
      expect(result.summary.invalid).toBe(0)
    })

    it('should handle Excel file parsing', async () => {
      const fileId = 'file-123'
      const mockFile = {
        id: fileId,
        path: '/uploads/test.xlsx',
        mimeType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      }

      mockPrisma.file.findUnique.mockResolvedValue(mockFile)

      // Mock XLSX parsing
      const XLSX = require('xlsx')
      XLSX.readFile = jest.fn().mockReturnValue({
        SheetNames: ['Sheet1'],
        Sheets: {
          Sheet1: {}
        }
      })
      XLSX.utils = {
        sheet_to_json: jest.fn().mockReturnValue([
          { 'ID': 'PROD001', '名称': '商品1' },
          { 'ID': 'PROD002', '名称': '商品2' }
        ])
      }

      const result = await FileUploadService.parseProductFile(fileId)

      expect(result.products).toHaveLength(2)
      expect(result.products).toContain('PROD001')
      expect(result.products).toContain('PROD002')
    })

    it('should throw error for non-existent file', async () => {
      const fileId = 'non-existent'
      mockPrisma.file.findUnique.mockResolvedValue(null)

      await expect(FileUploadService.parseProductFile(fileId)).rejects.toThrow('文件不存在')
    })

    it('should handle parsing errors gracefully', async () => {
      const fileId = 'file-123'
      const mockFile = {
        id: fileId,
        path: '/uploads/corrupt.csv',
        mimeType: 'text/csv'
      }

      mockPrisma.file.findUnique.mockResolvedValue(mockFile)
      
      const fs = require('fs/promises')
      fs.readFile = jest.fn().mockRejectedValue(new Error('文件读取失败'))

      await expect(FileUploadService.parseProductFile(fileId)).rejects.toThrow('文件解析失败')
    })
  })

  describe('getFileInfo', () => {
    it('should return file information', async () => {
      const fileId = 'file-123'
      const mockFile = {
        id: fileId,
        filename: 'test.csv',
        originalName: 'test.csv',
        size: 100,
        mimeType: 'text/csv',
        path: '/uploads/test.csv',
        createdAt: new Date()
      }

      mockPrisma.file.findUnique.mockResolvedValue(mockFile)

      const result = await FileUploadService.getFileInfo(fileId)

      expect(result).toEqual(mockFile)
    })

    it('should return null for non-existent file', async () => {
      const fileId = 'non-existent'
      mockPrisma.file.findUnique.mockResolvedValue(null)

      const result = await FileUploadService.getFileInfo(fileId)

      expect(result).toBeNull()
    })
  })

  describe('deleteFile', () => {
    it('should delete file successfully', async () => {
      const fileId = 'file-123'
      const mockFile = {
        id: fileId,
        path: '/uploads/test.csv'
      }

      mockPrisma.file.findUnique.mockResolvedValue(mockFile)
      mockPrisma.file.delete.mockResolvedValue(mockFile)

      const fs = require('fs/promises')
      fs.unlink = jest.fn().mockResolvedValue(undefined)

      const result = await FileUploadService.deleteFile(fileId)

      expect(result.success).toBe(true)
      expect(mockPrisma.file.delete).toHaveBeenCalledWith({
        where: { id: fileId }
      })
      expect(fs.unlink).toHaveBeenCalledWith(mockFile.path)
    })

    it('should handle file deletion errors', async () => {
      const fileId = 'file-123'
      const mockFile = {
        id: fileId,
        path: '/uploads/test.csv'
      }

      mockPrisma.file.findUnique.mockResolvedValue(mockFile)
      
      const fs = require('fs/promises')
      fs.unlink = jest.fn().mockRejectedValue(new Error('文件删除失败'))

      const result = await FileUploadService.deleteFile(fileId)

      expect(result.success).toBe(false)
      expect(result.error).toContain('文件删除失败')
    })
  })

  describe('listFiles', () => {
    it('should return paginated file list', async () => {
      const mockFiles = [
        { id: 'file-1', filename: 'test1.csv', createdAt: new Date() },
        { id: 'file-2', filename: 'test2.csv', createdAt: new Date() }
      ]

      mockPrisma.file.findMany.mockResolvedValue(mockFiles)
      mockPrisma.file.count = jest.fn().mockResolvedValue(2)

      const result = await FileUploadService.listFiles({
        page: 1,
        limit: 10
      })

      expect(result.files).toEqual(mockFiles)
      expect(result.total).toBe(2)
      expect(result.page).toBe(1)
      expect(result.limit).toBe(10)
    })

    it('should filter files by type', async () => {
      const mimeType = 'text/csv'
      
      await FileUploadService.listFiles({ mimeType })

      expect(mockPrisma.file.findMany).toHaveBeenCalledWith({
        where: { mimeType },
        orderBy: { createdAt: 'desc' },
        skip: 0,
        take: 20
      })
    })
  })

  describe('cleanupOldFiles', () => {
    it('should cleanup files older than specified days', async () => {
      const retentionDays = 7
      const mockOldFiles = [
        { id: 'file-1', path: '/uploads/old1.csv' },
        { id: 'file-2', path: '/uploads/old2.csv' }
      ]

      mockPrisma.file.findMany.mockResolvedValue(mockOldFiles)
      mockPrisma.file.delete.mockResolvedValue({})

      const fs = require('fs/promises')
      fs.unlink = jest.fn().mockResolvedValue(undefined)

      const result = await FileUploadService.cleanupOldFiles(retentionDays)

      expect(result.deletedCount).toBe(2)
      expect(mockPrisma.file.findMany).toHaveBeenCalledWith({
        where: {
          createdAt: { lt: expect.any(Date) }
        },
        select: { id: true, path: true }
      })
    })
  })
})