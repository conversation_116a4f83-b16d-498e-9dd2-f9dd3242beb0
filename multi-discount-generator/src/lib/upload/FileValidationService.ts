import { ExcelService } from '@/lib/excel'
import type { BatchUploadResult } from '@/types/batch'

/**
 * 文件验证服务
 * 负责上传文件的格式验证、内容验证和安全检查
 */
export class FileValidationService {
  // 支持的文件类型
  private static readonly SUPPORTED_EXTENSIONS = ['.xlsx', '.xls', '.csv']
  private static readonly SUPPORTED_MIME_TYPES = [
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/vnd.ms-excel',
    'text/csv',
    'application/csv'
  ]

  // 文件大小限制
  private static readonly MAX_FILE_SIZE = 10 * 1024 * 1024 // 10MB
  private static readonly MAX_PRODUCT_COUNT = 1000
  private static readonly MIN_PRODUCT_COUNT = 1

  // 商品ID格式验证
  private static readonly PRODUCT_ID_PATTERN = /^[A-Za-z0-9_-]{1,50}$/

  /**
   * 验证文件基本信息
   */
  static validateFileBasics(file: {
    name: string
    size: number
    type: string
  }): { isValid: boolean; errors: string[] } {
    const errors: string[] = []

    // 检查文件名
    if (!file.name || file.name.trim().length === 0) {
      errors.push('文件名不能为空')
    }

    // 检查文件大小
    if (file.size <= 0) {
      errors.push('文件不能为空')
    } else if (file.size > this.MAX_FILE_SIZE) {
      errors.push(`文件大小不能超过${this.MAX_FILE_SIZE / 1024 / 1024}MB`)
    }

    // 检查文件扩展名
    const extension = this.getFileExtension(file.name)
    if (!this.SUPPORTED_EXTENSIONS.includes(extension)) {
      errors.push(`不支持的文件格式，请上传${this.SUPPORTED_EXTENSIONS.join('、')}格式的文件`)
    }

    // 检查MIME类型
    if (file.type && !this.SUPPORTED_MIME_TYPES.includes(file.type)) {
      errors.push('文件类型不正确，请上传Excel或CSV文件')
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }

  /**
   * 验证商品ID列表
   */
  static validateProductIds(productIds: string[]): {
    isValid: boolean
    errors: string[]
    warnings: string[]
  } {
    const errors: string[] = []
    const warnings: string[] = []

    // 检查数量
    if (productIds.length < this.MIN_PRODUCT_COUNT) {
      errors.push('至少需要包含1个商品ID')
    } else if (productIds.length > this.MAX_PRODUCT_COUNT) {
      errors.push(`商品数量不能超过${this.MAX_PRODUCT_COUNT}个`)
    }

    // 检查重复
    const uniqueIds = new Set(productIds)
    if (uniqueIds.size !== productIds.length) {
      const duplicateCount = productIds.length - uniqueIds.size
      warnings.push(`发现${duplicateCount}个重复的商品ID，已自动去重`)
    }

    // 检查格式
    const invalidIds: string[] = []
    const emptyIds: string[] = []
    
    productIds.forEach((id, index) => {
      if (!id || id.trim().length === 0) {
        emptyIds.push(`第${index + 1}行`)
      } else if (!this.PRODUCT_ID_PATTERN.test(id.trim())) {
        invalidIds.push(id.trim())
      }
    })

    if (emptyIds.length > 0) {
      errors.push(`发现${emptyIds.length}个空的商品ID: ${emptyIds.slice(0, 5).join('、')}${emptyIds.length > 5 ? '等' : ''}`)
    }

    if (invalidIds.length > 0) {
      errors.push(`发现${invalidIds.length}个格式不正确的商品ID: ${invalidIds.slice(0, 5).join('、')}${invalidIds.length > 5 ? '等' : ''}`)
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    }
  }

  /**
   * 获取文件扩展名
   */
  private static getFileExtension(filename: string): string {
    const lastDotIndex = filename.lastIndexOf('.')
    return lastDotIndex === -1 ? '' : filename.substring(lastDotIndex).toLowerCase()
  }

  /**
   * 获取支持的文件类型信息
   */
  static getSupportedFileTypes(): {
    extensions: string[]
    mimeTypes: string[]
    maxSize: number
    maxProductCount: number
  } {
    return {
      extensions: [...this.SUPPORTED_EXTENSIONS],
      mimeTypes: [...this.SUPPORTED_MIME_TYPES],
      maxSize: this.MAX_FILE_SIZE,
      maxProductCount: this.MAX_PRODUCT_COUNT
    }
  }
}