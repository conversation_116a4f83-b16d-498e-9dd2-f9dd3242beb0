import { writeFileSync, mkdirSync, existsSync, unlinkSync, statSync } from 'fs'
import { join, extname, basename } from 'path'
import { ExcelService } from '@/lib/excel'
import type { 
  FileUploadInfo, 
  BatchUploadResult,
  FileProcessConfig 
} from '@/types/batch'

/**
 * 文件上传服务
 * 负责文件的上传、存储、验证和解析
 */
export class FileUploadService {
  private static config: FileProcessConfig = {
    maxFileSize: 10 * 1024 * 1024, // 10MB
    allowedMimeTypes: [
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
      'application/vnd.ms-excel', // .xls
      'text/csv', // .csv
      'application/csv'
    ],
    uploadDir: join(process.cwd(), 'uploads', 'input'),
    tempDir: join(process.cwd(), 'uploads', 'temp'),
    outputDir: join(process.cwd(), 'uploads', 'output')
  }

  /**
   * 初始化上传目录
   */
  static initializeDirectories(): void {
    try {
      mkdirSync(this.config.uploadDir, { recursive: true })
      mkdirSync(this.config.tempDir, { recursive: true })
      mkdirSync(this.config.outputDir, { recursive: true })
      console.log('上传目录初始化完成')
    } catch (error) {
      console.error('上传目录初始化失败:', error)
      throw new Error('上传目录初始化失败')
    }
  }

  /**
   * 验证上传文件
   */
  static validateFile(file: {
    size: number
    mimetype: string
    originalname: string
  }): { isValid: boolean; errors: string[] } {
    const errors: string[] = []

    // 检查文件大小
    if (file.size > this.config.maxFileSize) {
      errors.push(`文件大小超过${this.config.maxFileSize / 1024 / 1024}MB限制`)
    }

    // 检查文件类型
    if (!this.config.allowedMimeTypes.includes(file.mimetype)) {
      errors.push('不支持的文件类型，请上传Excel或CSV文件')
    }

    // 检查文件扩展名
    const ext = extname(file.originalname).toLowerCase()
    if (!['.xlsx', '.xls', '.csv'].includes(ext)) {
      errors.push('文件扩展名不正确，请上传.xlsx、.xls或.csv文件')
    }

    // 检查文件名
    if (!file.originalname || file.originalname.trim().length === 0) {
      errors.push('文件名不能为空')
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }

  /**
   * 保存上传文件
   */
  static async saveUploadedFile(
    fileBuffer: Buffer,
    originalName: string,
    mimetype: string
  ): Promise<FileUploadInfo> {
    try {
      // 确保上传目录存在
      this.initializeDirectories()

      // 生成唯一文件名
      const timestamp = Date.now()
      const ext = extname(originalName)
      const baseName = basename(originalName, ext)
      const filename = `${baseName}_${timestamp}${ext}`
      const filePath = join(this.config.uploadDir, filename)

      // 保存文件
      writeFileSync(filePath, fileBuffer)

      return {
        filename,
        originalName,
        size: fileBuffer.length,
        mimetype,
        path: filePath
      }
    } catch (error) {
      console.error('保存上传文件失败:', error)
      throw new Error('文件保存失败')
    }
  }

  /**
   * 解析批量上传文件
   */
  static async parseBatchFile(filePath: string): Promise<BatchUploadResult> {
    try {
      // 检查文件是否存在
      if (!existsSync(filePath)) {
        throw new Error('文件不存在')
      }

      // 读取商品ID列表
      const productIds = await ExcelService.readProductIdsFromFile(filePath)

      if (productIds.length === 0) {
        return {
          success: false,
          message: '文件中没有找到有效的商品ID',
          productCount: 0,
          errors: ['文件中没有找到有效的商品ID']
        }
      }

      // 验证商品ID格式
      const invalidIds = productIds.filter(id => !/^[A-Za-z0-9_-]+$/.test(id))
      const errors: string[] = []

      if (invalidIds.length > 0) {
        errors.push(`发现${invalidIds.length}个格式不正确的商品ID: ${invalidIds.slice(0, 5).join(', ')}${invalidIds.length > 5 ? '...' : ''}`)
      }

      // 检查数量限制
      if (productIds.length > 1000) {
        errors.push('商品数量超过1000个限制')
      }

      return {
        success: errors.length === 0,
        message: errors.length === 0 
          ? `成功解析${productIds.length}个商品ID` 
          : `解析完成，但存在${errors.length}个问题`,
        productCount: productIds.length,
        errors: errors.length > 0 ? errors : undefined
      }
    } catch (error) {
      console.error('解析批量文件失败:', error)
      return {
        success: false,
        message: '文件解析失败',
        productCount: 0,
        errors: [error.message || '文件解析失败']
      }
    }
  }

  /**
   * 从文件中提取商品ID列表
   */
  static async extractProductIds(filePath: string): Promise<string[]> {
    try {
      return await ExcelService.readProductIdsFromFile(filePath)
    } catch (error) {
      console.error('提取商品ID失败:', error)
      throw new Error('提取商品ID失败')
    }
  }

  /**
   * 清理临时文件
   */
  static cleanupFile(filePath: string): void {
    try {
      if (existsSync(filePath)) {
        unlinkSync(filePath)
        console.log(`临时文件已清理: ${filePath}`)
      }
    } catch (error) {
      console.error('清理临时文件失败:', error)
    }
  }

  /**
   * 获取配置信息
   */
  static getConfig(): FileProcessConfig {
    return { ...this.config }
  }
}