import { prisma } from '@/lib/prisma'
import { TaskStatus, TaskType } from '@prisma/client'
import { performanceMonitor } from '@/lib/performance/PerformanceMonitor'
import { TaskWorker } from './TaskWorker'
import type { 
  TaskWithRelations, 
  CreateTaskInput, 
  QueueConfig,
  TaskListOptions,
  TaskStats
} from '@/types/batch'

/**
 * 任务队列管理器
 * 负责任务的创建、调度、执行和监控
 */
export class TaskQueue {
  private static instance: TaskQueue
  private workers: Map<string, TaskWorker> = new Map()
  private isRunning = false
  private config: QueueConfig = {
    maxConcurrentTasks: 5,
    retryAttempts: 3,
    retryDelay: 5000,
    taskTimeout: 300000, // 5分钟
    cleanupInterval: 3600000 // 1小时
  }

  private constructor() {}

  static getInstance(): TaskQueue {
    if (!TaskQueue.instance) {
      TaskQueue.instance = new TaskQueue()
    }
    return TaskQueue.instance
  }

  /**
   * 创建新任务
   */
  async createTask(input: CreateTaskInput): Promise<TaskWithRelations> {
    try {
      // 验证输入
      this.validateTaskInput(input)

      const task = await prisma.task.create({
        data: {
          name: input.name,
          type: input.type,
          status: TaskStatus.PENDING,
          config: input.config,
          priority: input.priority || 0,
          progress: 0
        },
        include: {
          logs: {
            orderBy: { timestamp: 'desc' },
            take: 10
          }
        }
      })

      // 记录性能指标
      performanceMonitor.recordMetric('task_created', 1, {
        type: input.type,
        priority: input.priority
      })

      // 如果队列正在运行，尝试立即处理任务
      if (this.isRunning) {
        this.processNextTask()
      }

      return task
    } catch (error) {
      console.error('创建任务失败:', error)
      throw new Error(`创建任务失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 获取任务详情
   */
  async getTask(taskId: string): Promise<TaskWithRelations | null> {
    try {
      return await prisma.task.findUnique({
        where: { id: taskId },
        include: {
          logs: {
            orderBy: { timestamp: 'desc' },
            take: 10
          }
        }
      })
    } catch (error) {
      console.error('获取任务失败:', error)
      return null
    }
  }

  /**
   * 更新任务状态
   */
  async updateTaskStatus(
    taskId: string, 
    status: TaskStatus, 
    progress?: number,
    error?: string
  ): Promise<TaskWithRelations> {
    const updateData: any = {
      status,
      updatedAt: new Date()
    }

    if (progress !== undefined) {
      updateData.progress = progress
    }

    if (error) {
      updateData.error = error
    }

    if (status === TaskStatus.COMPLETED || status === TaskStatus.FAILED || status === TaskStatus.CANCELLED) {
      updateData.completedAt = new Date()
    }

    const task = await prisma.task.update({
      where: { id: taskId },
      data: updateData,
      include: {
        logs: {
          orderBy: { timestamp: 'desc' },
          take: 10
        }
      }
    })

    // 记录日志
    await this.addTaskLog(taskId, `任务状态更新为: ${status}`, progress)

    return task
  }

  /**
   * 暂停任务
   */
  async pauseTask(taskId: string): Promise<TaskWithRelations> {
    const task = await this.getTask(taskId)
    if (!task) {
      throw new Error('任务不存在')
    }

    if (task.status !== TaskStatus.RUNNING) {
      throw new Error('只能暂停正在运行的任务')
    }

    // 停止对应的worker
    const worker = this.workers.get(taskId)
    if (worker) {
      await worker.pause()
    }

    return await this.updateTaskStatus(taskId, TaskStatus.PAUSED)
  }

  /**
   * 恢复任务
   */
  async resumeTask(taskId: string): Promise<TaskWithRelations> {
    const task = await this.getTask(taskId)
    if (!task) {
      throw new Error('任务不存在')
    }

    if (task.status !== TaskStatus.PAUSED) {
      throw new Error('只能恢复已暂停的任务')
    }

    // 恢复对应的worker
    const worker = this.workers.get(taskId)
    if (worker) {
      await worker.resume()
    } else {
      // 如果worker不存在，重新创建并启动
      await this.processTask(task)
    }

    return await this.updateTaskStatus(taskId, TaskStatus.RUNNING)
  }

  /**
   * 取消任务
   */
  async cancelTask(taskId: string): Promise<TaskWithRelations> {
    const task = await this.getTask(taskId)
    if (!task) {
      throw new Error('任务不存在')
    }

    if (task.status === TaskStatus.COMPLETED) {
      throw new Error('无法取消已完成的任务')
    }

    // 停止对应的worker
    const worker = this.workers.get(taskId)
    if (worker) {
      await worker.cancel()
      this.workers.delete(taskId)
    }

    return await this.updateTaskStatus(taskId, TaskStatus.CANCELLED)
  }

  /**
   * 获取任务列表
   */
  async listTasks(options: TaskListOptions = {}): Promise<{
    tasks: TaskWithRelations[]
    total: number
    page: number
    limit: number
  }> {
    const {
      page = 1,
      limit = 20,
      status,
      type,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = options

    const skip = (page - 1) * limit
    const where: any = {}

    if (status) {
      where.status = status
    }

    if (type) {
      where.type = type
    }

    const [tasks, total] = await Promise.all([
      prisma.task.findMany({
        where,
        orderBy: { [sortBy]: sortOrder },
        skip,
        take: limit,
        include: {
          logs: {
            orderBy: { timestamp: 'desc' },
            take: 5
          }
        }
      }),
      prisma.task.count({ where })
    ])

    return {
      tasks,
      total,
      page,
      limit
    }
  }

  /**
   * 获取任务统计信息
   */
  async getStats(): Promise<TaskStats> {
    const stats = await prisma.task.groupBy({
      by: ['status'],
      _count: {
        status: true
      }
    })

    const result: TaskStats = {
      pending: 0,
      running: 0,
      completed: 0,
      failed: 0,
      paused: 0,
      cancelled: 0,
      total: 0
    }

    stats.forEach(stat => {
      const count = stat._count.status
      result.total += count

      switch (stat.status) {
        case TaskStatus.PENDING:
          result.pending = count
          break
        case TaskStatus.RUNNING:
          result.running = count
          break
        case TaskStatus.COMPLETED:
          result.completed = count
          break
        case TaskStatus.FAILED:
          result.failed = count
          break
        case TaskStatus.PAUSED:
          result.paused = count
          break
        case TaskStatus.CANCELLED:
          result.cancelled = count
          break
      }
    })

    return result
  }

  /**
   * 启动任务队列
   */
  async start(): Promise<void> {
    if (this.isRunning) {
      return
    }

    this.isRunning = true
    console.log('任务队列已启动')

    // 处理待处理的任务
    this.processNextTask()

    // 启动清理定时器
    this.startCleanupTimer()
  }

  /**
   * 停止任务队列
   */
  async stop(): Promise<void> {
    if (!this.isRunning) {
      return
    }

    this.isRunning = false

    // 停止所有worker
    for (const [taskId, worker] of this.workers) {
      await worker.cancel()
    }
    this.workers.clear()

    console.log('任务队列已停止')
  }

  /**
   * 检查队列是否正在运行
   */
  isQueueRunning(): boolean {
    return this.isRunning
  }

  /**
   * 清理旧任务
   */
  async cleanup(retentionDays: number = 30): Promise<{ cleanedTasks: number }> {
    const cutoffDate = new Date(Date.now() - retentionDays * 24 * 60 * 60 * 1000)

    const tasksToDelete = await prisma.task.findMany({
      where: {
        status: { 
          in: [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED] 
        },
        completedAt: { lt: cutoffDate }
      },
      select: { id: true }
    })

    if (tasksToDelete.length > 0) {
      // 删除任务日志
      await prisma.taskLog.deleteMany({
        where: {
          taskId: { in: tasksToDelete.map(t => t.id) }
        }
      })

      // 删除任务
      await prisma.task.deleteMany({
        where: {
          id: { in: tasksToDelete.map(t => t.id) }
        }
      })
    }

    return { cleanedTasks: tasksToDelete.length }
  }

  /**
   * 处理下一个任务
   */
  private async processNextTask(): Promise<void> {
    if (!this.isRunning || this.workers.size >= this.config.maxConcurrentTasks) {
      return
    }

    try {
      const nextTask = await prisma.task.findFirst({
        where: { status: TaskStatus.PENDING },
        orderBy: [
          { priority: 'desc' },
          { createdAt: 'asc' }
        ],
        include: {
          logs: {
            orderBy: { timestamp: 'desc' },
            take: 10
          }
        }
      })

      if (nextTask) {
        await this.processTask(nextTask)
      }
    } catch (error) {
      console.error('处理下一个任务失败:', error)
    }
  }

  /**
   * 处理单个任务
   */
  private async processTask(task: TaskWithRelations): Promise<void> {
    try {
      // 更新任务状态为运行中
      await this.updateTaskStatus(task.id, TaskStatus.RUNNING, 0)

      // 创建worker
      const worker = new TaskWorker(task, this.config)
      this.workers.set(task.id, worker)

      // 监听worker事件
      worker.on('progress', (progress: number) => {
        this.updateTaskStatus(task.id, TaskStatus.RUNNING, progress)
      })

      worker.on('completed', async () => {
        await this.updateTaskStatus(task.id, TaskStatus.COMPLETED, 100)
        this.workers.delete(task.id)
        this.processNextTask() // 处理下一个任务
      })

      worker.on('failed', async (error: string) => {
        await this.updateTaskStatus(task.id, TaskStatus.FAILED, undefined, error)
        this.workers.delete(task.id)
        this.processNextTask() // 处理下一个任务
      })

      // 启动worker
      await worker.start()

    } catch (error) {
      console.error('处理任务失败:', error)
      await this.updateTaskStatus(
        task.id, 
        TaskStatus.FAILED, 
        undefined, 
        error instanceof Error ? error.message : '未知错误'
      )
      this.workers.delete(task.id)
      this.processNextTask()
    }
  }

  /**
   * 添加任务日志
   */
  private async addTaskLog(
    taskId: string, 
    message: string, 
    progress?: number
  ): Promise<void> {
    try {
      await prisma.taskLog.create({
        data: {
          taskId,
          message,
          progress,
          timestamp: new Date()
        }
      })
    } catch (error) {
      console.error('添加任务日志失败:', error)
    }
  }

  /**
   * 验证任务输入
   */
  private validateTaskInput(input: CreateTaskInput): void {
    if (!input.name || input.name.trim().length === 0) {
      throw new Error('任务名称不能为空')
    }

    if (!Object.values(TaskType).includes(input.type)) {
      throw new Error('无效的任务类型')
    }

    if (input.priority !== undefined && (input.priority < 0 || input.priority > 10)) {
      throw new Error('任务优先级必须在0-10之间')
    }

    if (!input.config || typeof input.config !== 'object') {
      throw new Error('任务配置不能为空')
    }
  }

  /**
   * 启动清理定时器
   */
  private startCleanupTimer(): void {
    setInterval(async () => {
      if (this.isRunning) {
        try {
          await this.cleanup()
        } catch (error) {
          console.error('自动清理任务失败:', error)
        }
      }
    }, this.config.cleanupInterval)
  }
}