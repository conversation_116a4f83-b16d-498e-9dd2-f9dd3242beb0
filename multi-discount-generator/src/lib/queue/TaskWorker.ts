import { EventEmitter } from 'events'
import { TaskType } from '@prisma/client'
import { BatchExcelService } from '@/lib/batch/BatchExcelService'
import { performanceMonitor } from '@/lib/performance/PerformanceMonitor'
import type { TaskWithRelations, QueueConfig, ProductProcessResult } from '@/types/batch'

/**
 * 任务执行器
 * 负责具体任务的执行逻辑
 */
export class TaskWorker extends EventEmitter {
  private task: TaskWithRelations
  private config: QueueConfig
  private isRunning = false
  private isPaused = false
  private isCancelled = false
  private batchService: BatchExcelService

  constructor(task: TaskWithRelations, config: QueueConfig) {
    super()
    this.task = task
    this.config = config
    this.batchService = new BatchExcelService()
  }

  /**
   * 启动任务执行
   */
  async start(): Promise<void> {
    if (this.isRunning) {
      return
    }

    this.isRunning = true
    this.isPaused = false
    this.isCancelled = false

    try {
      console.log(`开始执行任务: ${this.task.name} (${this.task.id})`)
      
      // 根据任务类型执行不同的处理逻辑
      switch (this.task.type) {
        case TaskType.BATCH_EXCEL_GENERATION:
          await this.processBatchExcelGeneration()
          break
        case TaskType.BATCH_PRODUCT_IMPORT:
          await this.processBatchProductImport()
          break
        case TaskType.BATCH_TEMPLATE_GENERATION:
          await this.processBatchTemplateGeneration()
          break
        default:
          throw new Error(`不支持的任务类型: ${this.task.type}`)
      }

      if (!this.isCancelled) {
        this.emit('completed')
        console.log(`任务执行完成: ${this.task.name} (${this.task.id})`)
      }

    } catch (error) {
      console.error(`任务执行失败: ${this.task.name} (${this.task.id})`, error)
      this.emit('failed', error instanceof Error ? error.message : '未知错误')
    } finally {
      this.isRunning = false
    }
  }

  /**
   * 暂停任务
   */
  async pause(): Promise<void> {
    if (!this.isRunning || this.isPaused) {
      return
    }

    this.isPaused = true
    console.log(`任务已暂停: ${this.task.name} (${this.task.id})`)
  }

  /**
   * 恢复任务
   */
  async resume(): Promise<void> {
    if (!this.isRunning || !this.isPaused) {
      return
    }

    this.isPaused = false
    console.log(`任务已恢复: ${this.task.name} (${this.task.id})`)
  }

  /**
   * 取消任务
   */
  async cancel(): Promise<void> {
    this.isCancelled = true
    this.isPaused = false
    console.log(`任务已取消: ${this.task.name} (${this.task.id})`)
  }

  /**
   * 处理批量Excel生成任务
   */
  private async processBatchExcelGeneration(): Promise<void> {
    const config = this.task.config as any
    const products = config.products || []
    
    if (!Array.isArray(products) || products.length === 0) {
      throw new Error('没有找到要处理的商品')
    }

    const totalProducts = products.length
    let processedCount = 0
    const results: ProductProcessResult[] = []

    // 分批处理商品
    const batchSize = 10
    for (let i = 0; i < products.length; i += batchSize) {
      if (this.isCancelled) {
        throw new Error('任务已被取消')
      }

      // 等待暂停状态结束
      while (this.isPaused && !this.isCancelled) {
        await this.sleep(1000)
      }

      const batch = products.slice(i, i + batchSize)
      
      try {
        // 处理当前批次
        const batchResults = await this.processBatch(batch, config)
        results.push(...batchResults)
        
        processedCount += batch.length
        const progress = Math.round((processedCount / totalProducts) * 100)
        
        this.emit('progress', progress)
        
        // 记录性能指标
        performanceMonitor.recordMetric('batch_processed', batch.length, {
          taskId: this.task.id,
          taskType: this.task.type
        })

      } catch (error) {
        console.error(`处理批次失败 (${i}-${i + batch.length}):`, error)
        
        // 记录失败的商品
        batch.forEach(productId => {
          results.push({
            productId,
            success: false,
            error: error instanceof Error ? error.message : '处理失败'
          })
        })
        
        processedCount += batch.length
        const progress = Math.round((processedCount / totalProducts) * 100)
        this.emit('progress', progress)
      }

      // 短暂休息，避免过度占用资源
      await this.sleep(100)
    }

    // 生成最终的Excel文件
    if (results.some(r => r.success)) {
      await this.generateFinalExcel(results, config)
    }

    console.log(`批量处理完成，成功: ${results.filter(r => r.success).length}，失败: ${results.filter(r => !r.success).length}`)
  }

  /**
   * 处理批量商品导入任务
   */
  private async processBatchProductImport(): Promise<void> {
    const config = this.task.config as any
    const filePath = config.filePath
    
    if (!filePath) {
      throw new Error('没有找到要导入的文件')
    }

    try {
      // 解析Excel文件
      const products = await this.batchService.parseExcelFile(filePath)
      
      if (!products || products.length === 0) {
        throw new Error('文件中没有找到有效的商品数据')
      }

      const totalProducts = products.length
      let processedCount = 0

      // 逐个验证和导入商品
      for (const product of products) {
        if (this.isCancelled) {
          throw new Error('任务已被取消')
        }

        // 等待暂停状态结束
        while (this.isPaused && !this.isCancelled) {
          await this.sleep(1000)
        }

        try {
          // 验证商品数据
          await this.validateProductData(product)
          
          // 这里可以添加实际的商品导入逻辑
          // await this.importProduct(product)
          
          processedCount++
          const progress = Math.round((processedCount / totalProducts) * 100)
          this.emit('progress', progress)

        } catch (error) {
          console.error(`导入商品失败 (${product.id}):`, error)
          // 继续处理下一个商品
        }

        await this.sleep(50)
      }

    } catch (error) {
      throw new Error(`批量导入失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 处理批量模板生成任务
   */
  private async processBatchTemplateGeneration(): Promise<void> {
    const config = this.task.config as any
    const templates = config.templates || []
    
    if (!Array.isArray(templates) || templates.length === 0) {
      throw new Error('没有找到要生成的模板')
    }

    const totalTemplates = templates.length
    let processedCount = 0

    for (const template of templates) {
      if (this.isCancelled) {
        throw new Error('任务已被取消')
      }

      // 等待暂停状态结束
      while (this.isPaused && !this.isCancelled) {
        await this.sleep(1000)
      }

      try {
        // 生成单个模板
        await this.generateTemplate(template, config)
        
        processedCount++
        const progress = Math.round((processedCount / totalTemplates) * 100)
        this.emit('progress', progress)

      } catch (error) {
        console.error(`生成模板失败 (${template.name}):`, error)
        // 继续处理下一个模板
      }

      await this.sleep(100)
    }
  }

  /**
   * 处理单个批次的商品
   */
  private async processBatch(
    productIds: string[], 
    config: any
  ): Promise<ProductProcessResult[]> {
    const results: ProductProcessResult[] = []

    for (const productId of productIds) {
      try {
        // 这里添加实际的商品处理逻辑
        // 例如：生成折扣模板、处理商品数据等
        
        // 模拟处理时间
        await this.sleep(Math.random() * 1000 + 500)
        
        results.push({
          productId,
          success: true,
          data: {
            // 处理结果数据
            processedAt: new Date(),
            config: config
          }
        })

      } catch (error) {
        results.push({
          productId,
          success: false,
          error: error instanceof Error ? error.message : '处理失败'
        })
      }
    }

    return results
  }

  /**
   * 生成最终的Excel文件
   */
  private async generateFinalExcel(
    results: ProductProcessResult[], 
    config: any
  ): Promise<void> {
    try {
      const successResults = results.filter(r => r.success)
      
      if (successResults.length === 0) {
        throw new Error('没有成功处理的商品数据')
      }

      // 使用BatchExcelService生成Excel文件
      const excelBuffer = await this.batchService.generateBatchExcel(
        successResults.map(r => r.data),
        config
      )

      // 保存文件
      const fileName = `batch_result_${this.task.id}_${Date.now()}.xlsx`
      const filePath = `uploads/results/${fileName}`
      
      // 这里可以添加文件保存逻辑
      console.log(`Excel文件已生成: ${filePath}`)

    } catch (error) {
      console.error('生成Excel文件失败:', error)
      throw error
    }
  }

  /**
   * 验证商品数据
   */
  private async validateProductData(product: any): Promise<void> {
    if (!product.id) {
      throw new Error('商品ID不能为空')
    }

    if (!product.name) {
      throw new Error('商品名称不能为空')
    }

    // 添加更多验证逻辑
  }

  /**
   * 生成单个模板
   */
  private async generateTemplate(template: any, config: any): Promise<void> {
    // 模拟模板生成
    await this.sleep(Math.random() * 2000 + 1000)
    
    console.log(`模板已生成: ${template.name}`)
  }

  /**
   * 休眠指定毫秒数
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }
}