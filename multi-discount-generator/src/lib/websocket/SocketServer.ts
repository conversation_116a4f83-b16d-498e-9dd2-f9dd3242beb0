import { Server as SocketIOServer } from 'socket.io'
import type { Server as HTTPServer } from 'http'
import { taskMonitor } from '@/lib/monitoring'
import { taskQueue } from '@/lib/queue'
import type { 
  SocketEvents,
  TaskProgress,
  SystemMetrics 
} from '@/types/batch'

/**
 * WebSocket服务器
 * 负责实时通信和状态推送
 */
export class SocketServer {
  private static instance: SocketServer
  private io: SocketIOServer | null = null
  private connectedClients = new Map<string, Set<string>>() // taskId -> Set<socketId>

  private constructor() {}

  /**
   * 获取单例实例
   */
  static getInstance(): SocketServer {
    if (!SocketServer.instance) {
      SocketServer.instance = new SocketServer()
    }
    return SocketServer.instance
  }

  /**
   * 初始化WebSocket服务器
   */
  initialize(httpServer: HTTPServer): void {
    this.io = new SocketIOServer(httpServer, {
      cors: {
        origin: process.env.NODE_ENV === 'production' 
          ? process.env.NEXT_PUBLIC_APP_URL 
          : ['http://localhost:3000', 'http://127.0.0.1:3000'],
        methods: ['GET', 'POST'],
        credentials: true
      },
      transports: ['websocket', 'polling']
    })

    this.setupEventHandlers()
    this.setupTaskMonitoring()
    
    console.log('WebSocket服务器已初始化')
  }

  /**
   * 设置事件处理器
   */
  private setupEventHandlers(): void {
    if (!this.io) return

    this.io.on('connection', (socket) => {
      console.log(`客户端连接: ${socket.id}`)

      // 处理加入任务房间
      socket.on('joinTask', (taskId: string) => {
        this.handleJoinTask(socket, taskId)
      })

      // 处理离开任务房间
      socket.on('leaveTask', (taskId: string) => {
        this.handleLeaveTask(socket, taskId)
      })

      // 处理获取任务状态
      socket.on('getTaskStatus', async (taskId: string) => {
        await this.handleGetTaskStatus(socket, taskId)
      })

      // 处理获取系统指标
      socket.on('getSystemMetrics', async () => {
        await this.handleGetSystemMetrics(socket)
      })

      // 处理断开连接
      socket.on('disconnect', () => {
        this.handleDisconnect(socket)
      })

      // 发送连接成功消息
      socket.emit('connected', {
        message: '连接成功',
        socketId: socket.id,
        timestamp: new Date()
      })
    })
  }

  /**
   * 设置任务监控
   */
  private setupTaskMonitoring(): void {
    // 监听任务队列事件
    taskQueue.on('taskProgress', (progress: TaskProgress) => {
      this.broadcastTaskProgress(progress)
    })

    taskQueue.on('taskCompleted', (data: { taskId: string; result: any }) => {
      this.broadcastTaskCompleted(data.taskId, data.result)
    })

    taskQueue.on('taskFailed', (data: { taskId: string; error: Error }) => {
      this.broadcastTaskFailed(data.taskId, data.error)
    })

    taskQueue.on('taskCancelled', (data: { taskId: string }) => {
      this.broadcastTaskCancelled(data.taskId)
    })

    // 监听系统指标更新
    taskMonitor.on('metricsUpdated', (metrics: SystemMetrics) => {
      this.broadcastSystemMetrics(metrics)
    })
  }

  /**
   * 处理加入任务房间
   */
  private handleJoinTask(socket: any, taskId: string): void {
    if (!taskId || typeof taskId !== 'string') {
      socket.emit('error', { message: '任务ID无效' })
      return
    }

    // 加入房间
    socket.join(`task:${taskId}`)
    
    // 记录连接
    if (!this.connectedClients.has(taskId)) {
      this.connectedClients.set(taskId, new Set())
    }
    this.connectedClients.get(taskId)!.add(socket.id)

    console.log(`客户端 ${socket.id} 加入任务 ${taskId}`)
    
    // 发送当前任务状态
    this.sendCurrentTaskStatus(socket, taskId)
  }

  /**
   * 处理离开任务房间
   */
  private handleLeaveTask(socket: any, taskId: string): void {
    if (!taskId || typeof taskId !== 'string') {
      return
    }

    // 离开房间
    socket.leave(`task:${taskId}`)
    
    // 移除连接记录
    const clients = this.connectedClients.get(taskId)
    if (clients) {
      clients.delete(socket.id)
      if (clients.size === 0) {
        this.connectedClients.delete(taskId)
      }
    }

    console.log(`客户端 ${socket.id} 离开任务 ${taskId}`)
  }

  /**
   * 处理获取任务状态
   */
  private async handleGetTaskStatus(socket: any, taskId: string): Promise<void> {
    try {
      const progress = await taskMonitor.getTaskProgress(taskId)
      const task = await taskMonitor.getTaskDetails(taskId)
      
      socket.emit('taskStatus', {
        taskId,
        progress,
        task,
        timestamp: new Date()
      })
    } catch (error) {
      socket.emit('error', {
        message: '获取任务状态失败',
        error: error instanceof Error ? error.message : '未知错误'
      })
    }
  }

  /**
   * 处理获取系统指标
   */
  private async handleGetSystemMetrics(socket: any): Promise<void> {
    try {
      const metrics = await taskMonitor.collectSystemMetrics()
      const queueStatus = await taskQueue.getStats()
      
      socket.emit('systemMetrics', {
        metrics,
        queueStatus,
        timestamp: new Date()
      })
    } catch (error) {
      socket.emit('error', {
        message: '获取系统指标失败',
        error: error instanceof Error ? error.message : '未知错误'
      })
    }
  }

  /**
   * 处理断开连接
   */
  private handleDisconnect(socket: any): void {
    console.log(`客户端断开连接: ${socket.id}`)
    
    // 清理连接记录
    for (const [taskId, clients] of this.connectedClients.entries()) {
      clients.delete(socket.id)
      if (clients.size === 0) {
        this.connectedClients.delete(taskId)
      }
    }
  }

  /**
   * 发送当前任务状态
   */
  private async sendCurrentTaskStatus(socket: any, taskId: string): Promise<void> {
    try {
      const progress = await taskMonitor.getTaskProgress(taskId)
      const task = await taskMonitor.getTaskDetails(taskId)
      
      if (progress && task) {
        socket.emit('taskStatus', {
          taskId,
          progress,
          task: {
            id: task.id,
            name: task.name,
            status: task.status,
            type: task.type,
            createdAt: task.createdAt,
            startedAt: task.startedAt,
            completedAt: task.completedAt
          },
          timestamp: new Date()
        })
      }
    } catch (error) {
      console.error('发送任务状态失败:', error)
    }
  }

  /**
   * 广播任务进度更新
   */
  private broadcastTaskProgress(progress: TaskProgress): void {
    if (!this.io) return

    this.io.to(`task:${progress.taskId}`).emit('taskProgress', {
      taskId: progress.taskId,
      progress: progress.progress,
      processedItems: progress.processedItems,
      totalItems: progress.totalItems,
      currentProduct: progress.currentProduct,
      estimatedTimeRemaining: progress.estimatedTimeRemaining,
      timestamp: new Date()
    })
  }

  /**
   * 广播任务完成
   */
  private broadcastTaskCompleted(taskId: string, result: any): void {
    if (!this.io) return

    this.io.to(`task:${taskId}`).emit('taskCompleted', {
      taskId,
      outputFile: result.outputFile,
      downloadUrl: `/api/download/tasks/${taskId}`,
      totalItems: result.totalItems || 0,
      processedItems: result.processedItems || 0,
      failedItems: result.failedItems || 0,
      timestamp: new Date()
    })
  }

  /**
   * 广播任务失败
   */
  private broadcastTaskFailed(taskId: string, error: Error): void {
    if (!this.io) return

    this.io.to(`task:${taskId}`).emit('taskFailed', {
      taskId,
      error: error.message,
      details: {
        name: error.name,
        stack: error.stack
      },
      timestamp: new Date()
    })
  }

  /**
   * 广播任务取消
   */
  private broadcastTaskCancelled(taskId: string): void {
    if (!this.io) return

    this.io.to(`task:${taskId}`).emit('taskCancelled', {
      taskId,
      message: '任务已取消',
      timestamp: new Date()
    })
  }

  /**
   * 广播系统指标更新
   */
  private broadcastSystemMetrics(metrics: SystemMetrics): void {
    if (!this.io) return

    // 只向订阅了系统指标的客户端发送
    this.io.emit('systemMetricsUpdate', {
      metrics,
      timestamp: new Date()
    })
  }

  /**
   * 发送通知给所有客户端
   */
  broadcastNotification(notification: {
    type: 'info' | 'warning' | 'error' | 'success'
    title: string
    message: string
    data?: any
  }): void {
    if (!this.io) return

    this.io.emit('notification', {
      ...notification,
      timestamp: new Date()
    })
  }

  /**
   * 发送通知给特定任务的客户端
   */
  sendTaskNotification(taskId: string, notification: {
    type: 'info' | 'warning' | 'error' | 'success'
    title: string
    message: string
    data?: any
  }): void {
    if (!this.io) return

    this.io.to(`task:${taskId}`).emit('taskNotification', {
      ...notification,
      taskId,
      timestamp: new Date()
    })
  }

  /**
   * 获取连接统计
   */
  getConnectionStats(): {
    totalConnections: number
    taskConnections: Record<string, number>
  } {
    const totalConnections = this.io?.engine.clientsCount || 0
    const taskConnections: Record<string, number> = {}
    
    for (const [taskId, clients] of this.connectedClients.entries()) {
      taskConnections[taskId] = clients.size
    }

    return {
      totalConnections,
      taskConnections
    }
  }

  /**
   * 关闭WebSocket服务器
   */
  close(): void {
    if (this.io) {
      this.io.close()
      this.io = null
      this.connectedClients.clear()
      console.log('WebSocket服务器已关闭')
    }
  }
}