import { SocketServer } from '../SocketServer'
import { Server as HttpServer } from 'http'
import { Server as SocketIOServer } from 'socket.io'
import { TestDataGenerator } from '@/lib/testing/TestDataGenerator'

// Mock socket.io
jest.mock('socket.io', () => {
  const mockSocket = {
    id: 'socket-123',
    join: jest.fn(),
    leave: jest.fn(),
    emit: jest.fn(),
    on: jest.fn(),
    disconnect: jest.fn()
  }

  const mockIO = {
    on: jest.fn(),
    to: jest.fn().mockReturnThis(),
    emit: jest.fn(),
    sockets: {
      sockets: new Map([['socket-123', mockSocket]])
    }
  }

  return {
    Server: jest.fn().mockImplementation(() => mockIO)
  }
})

describe('SocketServer', () => {
  let socketServer: SocketServer
  let mockHttpServer: HttpServer
  let mockIO: any

  beforeEach(() => {
    mockHttpServer = {} as HttpServer
    socketServer = SocketServer.getInstance()
    
    // Get the mocked socket.io instance
    const { Server } = require('socket.io')
    mockIO = new Server()
    
    jest.clearAllMocks()
  })

  describe('initialize', () => {
    it('should initialize socket server successfully', () => {
      socketServer.initialize(mockHttpServer)

      expect(mockIO.on).toHaveBeenCalledWith('connection', expect.any(Function))
    })

    it('should not initialize twice', () => {
      socketServer.initialize(mockHttpServer)
      socketServer.initialize(mockHttpServer)

      // Should only be called once
      expect(require('socket.io').Server).toHaveBeenCalledTimes(1)
    })
  })

  describe('broadcastTaskUpdate', () => {
    it('should broadcast task update to all clients', () => {
      socketServer.initialize(mockHttpServer)

      const taskUpdate = {
        taskId: 'task-123',
        status: 'RUNNING',
        progress: 50
      }

      socketServer.broadcastTaskUpdate(taskUpdate)

      expect(mockIO.emit).toHaveBeenCalledWith('task_update', taskUpdate)
    })

    it('should handle broadcast when not initialized', () => {
      const taskUpdate = {
        taskId: 'task-123',
        status: 'RUNNING',
        progress: 50
      }

      // Should not throw error
      expect(() => {
        socketServer.broadcastTaskUpdate(taskUpdate)
      }).not.toThrow()
    })
  })

  describe('broadcastTaskProgress', () => {
    it('should broadcast task progress to specific room', () => {
      socketServer.initialize(mockHttpServer)

      const taskId = 'task-123'
      const progress = 75

      socketServer.broadcastTaskProgress(taskId, progress)

      expect(mockIO.to).toHaveBeenCalledWith(`task_${taskId}`)
      expect(mockIO.emit).toHaveBeenCalledWith('task_progress', {
        taskId,
        progress,
        timestamp: expect.any(Date)
      })
    })
  })

  describe('broadcastTaskCompleted', () => {
    it('should broadcast task completion', () => {
      socketServer.initialize(mockHttpServer)

      const taskId = 'task-123'
      const result = {
        success: true,
        filePath: '/uploads/result.xlsx',
        duration: 5000
      }

      socketServer.broadcastTaskCompleted(taskId, result)

      expect(mockIO.to).toHaveBeenCalledWith(`task_${taskId}`)
      expect(mockIO.emit).toHaveBeenCalledWith('task_completed', {
        taskId,
        result,
        timestamp: expect.any(Date)
      })
    })
  })

  describe('broadcastTaskFailed', () => {
    it('should broadcast task failure', () => {
      socketServer.initialize(mockHttpServer)

      const taskId = 'task-123'
      const error = 'Task execution failed'

      socketServer.broadcastTaskFailed(taskId, error)

      expect(mockIO.to).toHaveBeenCalledWith(`task_${taskId}`)
      expect(mockIO.emit).toHaveBeenCalledWith('task_failed', {
        taskId,
        error,
        timestamp: expect.any(Date)
      })
    })
  })

  describe('broadcastSystemStatus', () => {
    it('should broadcast system status update', () => {
      socketServer.initialize(mockHttpServer)

      const status = {
        health: 'healthy',
        activeConnections: 5,
        queueSize: 3
      }

      socketServer.broadcastSystemStatus(status)

      expect(mockIO.emit).toHaveBeenCalledWith('system_status', {
        ...status,
        timestamp: expect.any(Date)
      })
    })
  })

  describe('getConnectedClients', () => {
    it('should return number of connected clients', () => {
      socketServer.initialize(mockHttpServer)

      const count = socketServer.getConnectedClients()

      expect(count).toBe(1) // One mock socket
    })

    it('should return 0 when not initialized', () => {
      const count = socketServer.getConnectedClients()

      expect(count).toBe(0)
    })
  })

  describe('disconnectClient', () => {
    it('should disconnect specific client', () => {
      socketServer.initialize(mockHttpServer)

      const socketId = 'socket-123'
      const mockSocket = mockIO.sockets.sockets.get(socketId)

      socketServer.disconnectClient(socketId)

      expect(mockSocket.disconnect).toHaveBeenCalledWith(true)
    })

    it('should handle disconnect for non-existent client', () => {
      socketServer.initialize(mockHttpServer)

      expect(() => {
        socketServer.disconnectClient('non-existent')
      }).not.toThrow()
    })
  })

  describe('socket connection handling', () => {
    it('should handle socket connection events', () => {
      socketServer.initialize(mockHttpServer)

      // Get the connection handler
      const connectionHandler = mockIO.on.mock.calls.find(
        call => call[0] === 'connection'
      )[1]

      const mockSocket = {
        id: 'new-socket',
        join: jest.fn(),
        leave: jest.fn(),
        on: jest.fn(),
        emit: jest.fn()
      }

      // Simulate connection
      connectionHandler(mockSocket)

      // Should set up event listeners
      expect(mockSocket.on).toHaveBeenCalledWith('join_task', expect.any(Function))
      expect(mockSocket.on).toHaveBeenCalledWith('leave_task', expect.any(Function))
      expect(mockSocket.on).toHaveBeenCalledWith('disconnect', expect.any(Function))
    })

    it('should handle join_task event', () => {
      socketServer.initialize(mockHttpServer)

      const connectionHandler = mockIO.on.mock.calls.find(
        call => call[0] === 'connection'
      )[1]

      const mockSocket = {
        id: 'new-socket',
        join: jest.fn(),
        leave: jest.fn(),
        on: jest.fn(),
        emit: jest.fn()
      }

      connectionHandler(mockSocket)

      // Get the join_task handler
      const joinTaskHandler = mockSocket.on.mock.calls.find(
        call => call[0] === 'join_task'
      )[1]

      const taskId = 'task-123'
      joinTaskHandler(taskId)

      expect(mockSocket.join).toHaveBeenCalledWith(`task_${taskId}`)
    })

    it('should handle leave_task event', () => {
      socketServer.initialize(mockHttpServer)

      const connectionHandler = mockIO.on.mock.calls.find(
        call => call[0] === 'connection'
      )[1]

      const mockSocket = {
        id: 'new-socket',
        join: jest.fn(),
        leave: jest.fn(),
        on: jest.fn(),
        emit: jest.fn()
      }

      connectionHandler(mockSocket)

      // Get the leave_task handler
      const leaveTaskHandler = mockSocket.on.mock.calls.find(
        call => call[0] === 'leave_task'
      )[1]

      const taskId = 'task-123'
      leaveTaskHandler(taskId)

      expect(mockSocket.leave).toHaveBeenCalledWith(`task_${taskId}`)
    })
  })

  describe('error handling', () => {
    it('should handle socket errors gracefully', () => {
      socketServer.initialize(mockHttpServer)

      const connectionHandler = mockIO.on.mock.calls.find(
        call => call[0] === 'connection'
      )[1]

      const mockSocket = {
        id: 'error-socket',
        join: jest.fn().mockImplementation(() => {
          throw new Error('Join failed')
        }),
        leave: jest.fn(),
        on: jest.fn(),
        emit: jest.fn()
      }

      connectionHandler(mockSocket)

      const joinTaskHandler = mockSocket.on.mock.calls.find(
        call => call[0] === 'join_task'
      )[1]

      // Should not throw error
      expect(() => {
        joinTaskHandler('task-123')
      }).not.toThrow()
    })

    it('should handle broadcast errors gracefully', () => {
      socketServer.initialize(mockHttpServer)

      // Mock emit to throw error
      mockIO.emit.mockImplementation(() => {
        throw new Error('Broadcast failed')
      })

      const taskUpdate = {
        taskId: 'task-123',
        status: 'RUNNING',
        progress: 50
      }

      // Should not throw error
      expect(() => {
        socketServer.broadcastTaskUpdate(taskUpdate)
      }).not.toThrow()
    })
  })

  describe('cleanup', () => {
    it('should cleanup resources on shutdown', () => {
      socketServer.initialize(mockHttpServer)

      const mockClose = jest.fn()
      mockIO.close = mockClose

      socketServer.cleanup()

      expect(mockClose).toHaveBeenCalled()
    })

    it('should handle cleanup when not initialized', () => {
      expect(() => {
        socketServer.cleanup()
      }).not.toThrow()
    })
  })
})