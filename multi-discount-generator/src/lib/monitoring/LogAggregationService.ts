import { prisma } from '@/lib/prisma'
import { performanceMonitor } from '@/lib/performance/PerformanceMonitor'

interface LogQuery {
  level?: string[]
  taskId?: string
  source?: string
  startTime?: Date
  endTime?: Date
  search?: string
  limit?: number
  offset?: number
  sortBy?: 'timestamp' | 'level' | 'taskId'
  sortOrder?: 'asc' | 'desc'
}

interface LogAggregation {
  totalLogs: number
  byLevel: Record<string, number>
  bySource: Record<string, number>
  byHour: Array<{
    hour: string
    count: number
    errorCount: number
  }>
  topErrors: Array<{
    message: string
    count: number
    lastOccurrence: Date
  }>
  trends: {
    errorRate: number
    errorRateChange: number
    avgLogsPerHour: number
  }
}

interface LogPattern {
  pattern: string
  count: number
  level: string
  examples: string[]
  firstSeen: Date
  lastSeen: Date
}

/**
 * 日志聚合和查询服务
 * 提供高效的日志搜索、聚合和分析功能
 */
export class LogAggregationService {
  private static instance: LogAggregationService

  private constructor() {}

  static getInstance(): LogAggregationService {
    if (!LogAggregationService.instance) {
      LogAggregationService.instance = new LogAggregationService()
    }
    return LogAggregationService.instance
  }

  /**
   * 查询日志
   */
  async queryLogs(query: LogQuery): Promise<{
    logs: any[]
    total: number
    hasMore: boolean
  }> {
    const {
      level,
      taskId,
      source,
      startTime,
      endTime,
      search,
      limit = 50,
      offset = 0,
      sortBy = 'timestamp',
      sortOrder = 'desc'
    } = query

    // 构建查询条件
    const where: any = {}

    if (level && level.length > 0) {
      where.level = { in: level }
    }

    if (taskId) {
      where.taskId = taskId
    }

    if (source) {
      where.metadata = {
        path: ['source'],
        equals: source
      }
    }

    if (startTime || endTime) {
      where.timestamp = {}
      if (startTime) where.timestamp.gte = startTime
      if (endTime) where.timestamp.lte = endTime
    }

    if (search) {
      where.message = {
        contains: search,
        mode: 'insensitive'
      }
    }

    // 执行查询
    const [logs, total] = await Promise.all([
      prisma.taskLog.findMany({
        where,
        orderBy: { [sortBy]: sortOrder },
        take: limit,
        skip: offset,
        include: {
          task: {
            select: {
              id: true,
              name: true,
              type: true,
              status: true
            }
          }
        }
      }),
      prisma.taskLog.count({ where })
    ])

    const hasMore = offset + limit < total

    // 记录查询性能
    performanceMonitor.recordMetric('log_query_count', logs.length, 'count', {
      hasFilters: Object.keys(where).length > 0,
      resultSize: logs.length
    })

    return {
      logs,
      total,
      hasMore
    }
  }

  /**
   * 聚合日志统计
   */
  async aggregateLogs(timeWindow: number = 24 * 60 * 60 * 1000): Promise<LogAggregation> {
    const startTime = new Date(Date.now() - timeWindow)
    const endTime = new Date()

    // 并行执行多个聚合查询
    const [
      totalLogs,
      logsByLevel,
      logsBySource,
      hourlyStats,
      topErrors
    ] = await Promise.all([
      this.getTotalLogCount(startTime, endTime),
      this.getLogsByLevel(startTime, endTime),
      this.getLogsBySource(startTime, endTime),
      this.getHourlyLogStats(startTime, endTime),
      this.getTopErrors(startTime, endTime)
    ])

    // 计算趋势
    const trends = await this.calculateLogTrends(timeWindow)

    return {
      totalLogs,
      byLevel: logsByLevel,
      bySource: logsBySource,
      byHour: hourlyStats,
      topErrors,
      trends
    }
  }

  /**
   * 检测日志模式
   */
  async detectLogPatterns(timeWindow: number = 24 * 60 * 60 * 1000): Promise<LogPattern[]> {
    const startTime = new Date(Date.now() - timeWindow)
    
    // 获取所有日志消息
    const logs = await prisma.taskLog.findMany({
      where: {
        timestamp: { gte: startTime }
      },
      select: {
        message: true,
        level: true,
        timestamp: true
      }
    })

    // 使用简单的模式匹配来识别相似的日志
    const patterns = new Map<string, {
      count: number
      level: string
      examples: string[]
      firstSeen: Date
      lastSeen: Date
    }>()

    for (const log of logs) {
      const normalizedMessage = this.normalizeLogMessage(log.message)
      
      if (!patterns.has(normalizedMessage)) {
        patterns.set(normalizedMessage, {
          count: 0,
          level: log.level,
          examples: [],
          firstSeen: log.timestamp,
          lastSeen: log.timestamp
        })
      }

      const pattern = patterns.get(normalizedMessage)!
      pattern.count++
      pattern.examples.push(log.message)
      
      if (log.timestamp < pattern.firstSeen) {
        pattern.firstSeen = log.timestamp
      }
      if (log.timestamp > pattern.lastSeen) {
        pattern.lastSeen = log.timestamp
      }

      // 限制示例数量
      if (pattern.examples.length > 5) {
        pattern.examples = pattern.examples.slice(0, 5)
      }
    }

    // 转换为数组并按出现次数排序
    return Array.from(patterns.entries())
      .map(([pattern, data]) => ({
        pattern,
        ...data
      }))
      .filter(p => p.count > 1) // 只返回出现多次的模式
      .sort((a, b) => b.count - a.count)
      .slice(0, 20) // 返回前20个模式
  }

  /**
   * 导出日志
   */
  async exportLogs(query: LogQuery, format: 'json' | 'csv' = 'json'): Promise<string> {
    // 获取所有匹配的日志（不分页）
    const { logs } = await this.queryLogs({
      ...query,
      limit: 10000, // 设置一个合理的上限
      offset: 0
    })

    if (format === 'csv') {
      return this.convertLogsToCSV(logs)
    } else {
      return JSON.stringify(logs, null, 2)
    }
  }

  /**
   * 清理旧日志
   */
  async cleanupOldLogs(retentionDays: number = 30): Promise<{
    deletedCount: number
    oldestRemaining: Date | null
  }> {
    const cutoffDate = new Date(Date.now() - retentionDays * 24 * 60 * 60 * 1000)
    
    // 删除旧日志
    const deleteResult = await prisma.taskLog.deleteMany({
      where: {
        timestamp: { lt: cutoffDate }
      }
    })

    // 获取最旧的剩余日志时间
    const oldestLog = await prisma.taskLog.findFirst({
      orderBy: { timestamp: 'asc' },
      select: { timestamp: true }
    })

    performanceMonitor.recordMetric('log_cleanup_deleted', deleteResult.count, 'count', {
      retentionDays
    })

    return {
      deletedCount: deleteResult.count,
      oldestRemaining: oldestLog?.timestamp || null
    }
  }

  /**
   * 获取日志统计摘要
   */
  async getLogSummary(timeWindow: number = 24 * 60 * 60 * 1000): Promise<{
    total: number
    errors: number
    warnings: number
    info: number
    errorRate: number
    topSources: Array<{ source: string; count: number }>
    recentErrors: any[]
  }> {
    const startTime = new Date(Date.now() - timeWindow)
    
    const [
      total,
      errors,
      warnings,
      info,
      topSources,
      recentErrors
    ] = await Promise.all([
      prisma.taskLog.count({
        where: { timestamp: { gte: startTime } }
      }),
      prisma.taskLog.count({
        where: { 
          timestamp: { gte: startTime },
          level: 'ERROR'
        }
      }),
      prisma.taskLog.count({
        where: { 
          timestamp: { gte: startTime },
          level: 'WARN'
        }
      }),
      prisma.taskLog.count({
        where: { 
          timestamp: { gte: startTime },
          level: 'INFO'
        }
      }),
      this.getTopLogSources(startTime),
      prisma.taskLog.findMany({
        where: {
          timestamp: { gte: startTime },
          level: 'ERROR'
        },
        orderBy: { timestamp: 'desc' },
        take: 5,
        include: {
          task: {
            select: { id: true, name: true }
          }
        }
      })
    ])

    const errorRate = total > 0 ? (errors / total) * 100 : 0

    return {
      total,
      errors,
      warnings,
      info,
      errorRate,
      topSources,
      recentErrors
    }
  }

  // 私有辅助方法

  private async getTotalLogCount(startTime: Date, endTime: Date): Promise<number> {
    return prisma.taskLog.count({
      where: {
        timestamp: { gte: startTime, lte: endTime }
      }
    })
  }

  private async getLogsByLevel(startTime: Date, endTime: Date): Promise<Record<string, number>> {
    const result = await prisma.taskLog.groupBy({
      by: ['level'],
      _count: { level: true },
      where: {
        timestamp: { gte: startTime, lte: endTime }
      }
    })

    return result.reduce((acc, item) => {
      acc[item.level] = item._count.level
      return acc
    }, {} as Record<string, number>)
  }

  private async getLogsBySource(startTime: Date, endTime: Date): Promise<Record<string, number>> {
    // 由于metadata是JSON字段，我们需要使用原始查询
    const result = await prisma.$queryRaw`
      SELECT 
        JSON_EXTRACT(metadata, '$.source') as source,
        COUNT(*) as count
      FROM TaskLog 
      WHERE timestamp >= ${startTime} AND timestamp <= ${endTime}
        AND JSON_EXTRACT(metadata, '$.source') IS NOT NULL
      GROUP BY JSON_EXTRACT(metadata, '$.source')
      ORDER BY count DESC
    ` as Array<{ source: string; count: bigint }>

    return result.reduce((acc, item) => {
      acc[item.source] = Number(item.count)
      return acc
    }, {} as Record<string, number>)
  }

  private async getHourlyLogStats(startTime: Date, endTime: Date): Promise<Array<{
    hour: string
    count: number
    errorCount: number
  }>> {
    const result = await prisma.$queryRaw`
      SELECT 
        strftime('%Y-%m-%d %H:00:00', timestamp) as hour,
        COUNT(*) as count,
        SUM(CASE WHEN level = 'ERROR' THEN 1 ELSE 0 END) as errorCount
      FROM TaskLog 
      WHERE timestamp >= ${startTime} AND timestamp <= ${endTime}
      GROUP BY strftime('%Y-%m-%d %H:00:00', timestamp)
      ORDER BY hour
    ` as Array<{ hour: string; count: bigint; errorCount: bigint }>

    return result.map(item => ({
      hour: item.hour,
      count: Number(item.count),
      errorCount: Number(item.errorCount)
    }))
  }

  private async getTopErrors(startTime: Date, endTime: Date): Promise<Array<{
    message: string
    count: number
    lastOccurrence: Date
  }>> {
    const result = await prisma.$queryRaw`
      SELECT 
        message,
        COUNT(*) as count,
        MAX(timestamp) as lastOccurrence
      FROM TaskLog 
      WHERE timestamp >= ${startTime} AND timestamp <= ${endTime}
        AND level = 'ERROR'
      GROUP BY message
      ORDER BY count DESC
      LIMIT 10
    ` as Array<{ message: string; count: bigint; lastOccurrence: Date }>

    return result.map(item => ({
      message: item.message,
      count: Number(item.count),
      lastOccurrence: item.lastOccurrence
    }))
  }

  private async getTopLogSources(startTime: Date): Promise<Array<{ source: string; count: number }>> {
    const result = await prisma.$queryRaw`
      SELECT 
        JSON_EXTRACT(metadata, '$.source') as source,
        COUNT(*) as count
      FROM TaskLog 
      WHERE timestamp >= ${startTime}
        AND JSON_EXTRACT(metadata, '$.source') IS NOT NULL
      GROUP BY JSON_EXTRACT(metadata, '$.source')
      ORDER BY count DESC
      LIMIT 5
    ` as Array<{ source: string; count: bigint }>

    return result.map(item => ({
      source: item.source,
      count: Number(item.count)
    }))
  }

  private async calculateLogTrends(timeWindow: number): Promise<{
    errorRate: number
    errorRateChange: number
    avgLogsPerHour: number
  }> {
    const now = Date.now()
    const currentPeriodStart = new Date(now - timeWindow)
    const previousPeriodStart = new Date(now - timeWindow * 2)
    const previousPeriodEnd = new Date(now - timeWindow)

    const [currentStats, previousStats] = await Promise.all([
      this.getPeriodStats(currentPeriodStart, new Date()),
      this.getPeriodStats(previousPeriodStart, previousPeriodEnd)
    ])

    const currentErrorRate = currentStats.total > 0 ? (currentStats.errors / currentStats.total) * 100 : 0
    const previousErrorRate = previousStats.total > 0 ? (previousStats.errors / previousStats.total) * 100 : 0
    const errorRateChange = currentErrorRate - previousErrorRate

    const avgLogsPerHour = currentStats.total / (timeWindow / (60 * 60 * 1000))

    return {
      errorRate: currentErrorRate,
      errorRateChange,
      avgLogsPerHour
    }
  }

  private async getPeriodStats(startTime: Date, endTime: Date): Promise<{
    total: number
    errors: number
  }> {
    const [total, errors] = await Promise.all([
      prisma.taskLog.count({
        where: { timestamp: { gte: startTime, lte: endTime } }
      }),
      prisma.taskLog.count({
        where: { 
          timestamp: { gte: startTime, lte: endTime },
          level: 'ERROR'
        }
      })
    ])

    return { total, errors }
  }

  private normalizeLogMessage(message: string): string {
    // 移除时间戳、ID等变量部分，保留消息模式
    return message
      .replace(/\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/g, 'TIMESTAMP')
      .replace(/\b[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}\b/gi, 'UUID')
      .replace(/\b\d+\b/g, 'NUMBER')
      .replace(/\b[A-Z0-9]{10,}\b/g, 'ID')
      .trim()
  }

  private convertLogsToCSV(logs: any[]): string {
    if (logs.length === 0) return ''

    const headers = ['timestamp', 'level', 'message', 'taskId', 'taskName', 'source']
    const rows = logs.map(log => [
      log.timestamp.toISOString(),
      log.level,
      `"${log.message.replace(/"/g, '""')}"`, // 转义CSV中的引号
      log.taskId || '',
      log.task?.name || '',
      log.metadata?.source || ''
    ])

    return [headers, ...rows]
      .map(row => row.join(','))
      .join('\n')
  }
}

// 导出单例实例
export const logAggregationService = LogAggregationService.getInstance()