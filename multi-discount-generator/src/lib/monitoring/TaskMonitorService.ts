import { EventEmitter } from 'events'
import { prisma } from '@/lib/prisma'
import type { 
  TaskWithRelations,
  TaskProgress,
  SystemMetrics,
  TaskStats
} from '@/types/batch'
import { TaskStatus } from '@prisma/client'

/**
 * 任务监控服务
 * 负责任务执行状态的监控、统计和性能指标收集
 */
export class TaskMonitorService extends EventEmitter {
  private static instance: TaskMonitorService
  private monitoringInterval: NodeJS.Timeout | null = null
  private metricsHistory: SystemMetrics[] = []
  private readonly maxHistorySize = 1000

  private constructor() {
    super()
    this.startMonitoring()
  }

  /**
   * 获取单例实例
   */
  static getInstance(): TaskMonitorService {
    if (!TaskMonitorService.instance) {
      TaskMonitorService.instance = new TaskMonitorService()
    }
    return TaskMonitorService.instance
  }

  /**
   * 开始监控
   */
  private startMonitoring(): void {
    // 每30秒收集一次系统指标
    this.monitoringInterval = setInterval(async () => {
      try {
        const metrics = await this.collectSystemMetrics()
        this.addMetricsToHistory(metrics)
        this.emit('metricsUpdated', metrics)
      } catch (error) {
        console.error('收集系统指标失败:', error)
      }
    }, 30000)

    console.log('任务监控服务已启动')
  }

  /**
   * 停止监控
   */
  stopMonitoring(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval)
      this.monitoringInterval = null
    }
    console.log('任务监控服务已停止')
  }

  /**
   * 收集系统指标
   */
  async collectSystemMetrics(): Promise<SystemMetrics> {
    try {
      // 获取任务统计
      const taskStats = await this.getTaskStats()
      
      // 获取内存使用情况
      const memoryUsage = process.memoryUsage()
      const totalMemory = require('os').totalmem()
      
      // 获取CPU使用情况
      const cpuUsage = process.cpuUsage()
      
      // 获取磁盘使用情况（简化版）
      const diskUsage = await this.getDiskUsage()
      
      return {
        timestamp: new Date(),
        activeTasks: taskStats.running,
        queueLength: taskStats.pending,
        memoryUsage: {
          used: memoryUsage.heapUsed,
          total: totalMemory,
          percentage: (memoryUsage.heapUsed / totalMemory) * 100
        },
        cpuUsage: (cpuUsage.user + cpuUsage.system) / 1000000, // 转换为秒
        diskUsage
      }
    } catch (error) {
      console.error('收集系统指标失败:', error)
      throw error
    }
  }

  /**
   * 获取任务统计信息
   */
  async getTaskStats(): Promise<TaskStats> {
    try {
      const stats = await prisma.task.groupBy({
        by: ['status'],
        _count: {
          id: true
        }
      })

      const statusCounts = stats.reduce((acc, stat) => {
        acc[stat.status] = stat._count.id
        return acc
      }, {} as Record<string, number>)

      const total = stats.reduce((sum, stat) => sum + stat._count.id, 0)

      return {
        total,
        pending: statusCounts[TaskStatus.PENDING] || 0,
        running: statusCounts[TaskStatus.RUNNING] || 0,
        completed: statusCounts[TaskStatus.COMPLETED] || 0,
        failed: statusCounts[TaskStatus.FAILED] || 0,
        cancelled: statusCounts[TaskStatus.CANCELLED] || 0
      }
    } catch (error) {
      console.error('获取任务统计失败:', error)
      throw error
    }
  }

  /**
   * 获取磁盘使用情况
   */
  private async getDiskUsage(): Promise<{ used: number; total: number; percentage: number }> {
    try {
      const fs = require('fs')
      const path = require('path')
      const uploadDir = path.join(process.cwd(), 'uploads')
      
      // 简化的磁盘使用统计
      let totalSize = 0
      
      const calculateDirSize = (dirPath: string): number => {
        let size = 0
        try {
          const files = fs.readdirSync(dirPath)
          files.forEach((file: string) => {
            const filePath = path.join(dirPath, file)
            const stats = fs.statSync(filePath)
            if (stats.isDirectory()) {
              size += calculateDirSize(filePath)
            } else {
              size += stats.size
            }
          })
        } catch (error) {
          // 忽略访问错误
        }
        return size
      }
      
      if (fs.existsSync(uploadDir)) {
        totalSize = calculateDirSize(uploadDir)
      }
      
      // 假设总磁盘空间（实际应该通过系统API获取）
      const totalDisk = 100 * 1024 * 1024 * 1024 // 100GB
      
      return {
        used: totalSize,
        total: totalDisk,
        percentage: (totalSize / totalDisk) * 100
      }
    } catch (error) {
      console.error('获取磁盘使用情况失败:', error)
      return { used: 0, total: 0, percentage: 0 }
    }
  }

  /**
   * 获取任务列表
   */
  async getTaskList(params: {
    page?: number
    limit?: number
    status?: string
    search?: string
  } = {}): Promise<{
    tasks: any[]
    total: number
    page: number
    limit: number
    pages: number
  }> {
    try {
      const { page = 1, limit = 20, status, search } = params
      const skip = (page - 1) * limit

      // 构建查询条件
      const where: any = {}
      if (status) {
        where.status = status
      }
      if (search) {
        where.OR = [
          { name: { contains: search, mode: 'insensitive' } },
          { inputFile: { contains: search, mode: 'insensitive' } }
        ]
      }

      // 获取任务列表
      const [tasks, total] = await Promise.all([
        prisma.task.findMany({
          where,
          include: {
            products: {
              take: 5 // 只取前5个商品用于预览
            },
            logs: {
              take: 3,
              orderBy: { createdAt: 'desc' }
            }
          },
          orderBy: [
            { priority: 'desc' },
            { createdAt: 'desc' }
          ],
          skip,
          take: limit
        }),
        prisma.task.count({ where })
      ])

      return {
        tasks,
        total,
        page,
        limit,
        pages: Math.ceil(total / limit)
      }
    } catch (error) {
      console.error('获取任务列表失败:', error)
      throw error
    }
  }

  /**
   * 检查系统健康状态
   */
  async checkSystemHealth(): Promise<{
    status: 'healthy' | 'warning' | 'critical'
    issues: string[]
    metrics: SystemMetrics
  }> {
    try {
      const metrics = await this.collectSystemMetrics()
      const issues: string[] = []
      let status: 'healthy' | 'warning' | 'critical' = 'healthy'

      // 检查内存使用
      if (metrics.memoryUsage.percentage > 90) {
        issues.push('内存使用率过高')
        status = 'critical'
      } else if (metrics.memoryUsage.percentage > 80) {
        issues.push('内存使用率较高')
        if (status === 'healthy') status = 'warning'
      }

      // 检查磁盘使用
      if (metrics.diskUsage.percentage > 95) {
        issues.push('磁盘空间不足')
        status = 'critical'
      } else if (metrics.diskUsage.percentage > 85) {
        issues.push('磁盘空间较少')
        if (status === 'healthy') status = 'warning'
      }

      // 检查队列长度
      if (metrics.queueLength > 100) {
        issues.push('任务队列积压严重')
        if (status === 'healthy') status = 'warning'
      }

      return { status, issues, metrics }
    } catch (error) {
      console.error('系统健康检查失败:', error)
      return {
        status: 'critical',
        issues: ['系统健康检查失败'],
        metrics: {
          timestamp: new Date(),
          activeTasks: 0,
          queueLength: 0,
          memoryUsage: { used: 0, total: 0, percentage: 0 },
          cpuUsage: 0,
          diskUsage: { used: 0, total: 0, percentage: 0 }
        }
      }
    }
  }

  /**
   * 添加指标到历史记录
   */
  private addMetricsToHistory(metrics: SystemMetrics): void {
    this.metricsHistory.push(metrics)
    
    // 保持历史记录大小限制
    if (this.metricsHistory.length > this.maxHistorySize) {
      this.metricsHistory = this.metricsHistory.slice(-this.maxHistorySize)
    }
  }

  /**
   * 清理资源
   */
  cleanup(): void {
    this.stopMonitoring()
    this.removeAllListeners()
    this.metricsHistory = []
  }
}

// 导出单例实例
export const taskMonitorService = TaskMonitorService.getInstance()