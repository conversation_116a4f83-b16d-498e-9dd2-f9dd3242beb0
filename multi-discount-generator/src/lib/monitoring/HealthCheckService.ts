import { prisma } from '@/lib/prisma'
import { performanceMonitor } from '@/lib/performance/PerformanceMonitor'
import { resourceManager } from '@/lib/performance/ResourceManager'
import { databaseOptimizer } from '@/lib/performance/DatabaseOptimizer'
import { TaskQueue } from '@/lib/queue/TaskQueue'

interface HealthCheckResult {
  status: 'healthy' | 'warning' | 'critical'
  component: string
  message: string
  details?: any
  timestamp: Date
  responseTime?: number
}

interface SystemHealth {
  overall: 'healthy' | 'warning' | 'critical'
  checks: HealthCheckResult[]
  summary: {
    healthy: number
    warning: number
    critical: number
  }
  uptime: number
  timestamp: Date
}

/**
 * 系统健康检查服务
 * 监控系统各组件的健康状态
 */
export class HealthCheckService {
  private static instance: HealthCheckService
  private startTime: Date = new Date()
  private checkInterval: NodeJS.Timeout | null = null
  private lastHealthCheck: SystemHealth | null = null

  private constructor() {}

  static getInstance(): HealthCheckService {
    if (!HealthCheckService.instance) {
      HealthCheckService.instance = new HealthCheckService()
    }
    return HealthCheckService.instance
  }

  /**
   * 启动定期健康检查
   */
  startPeriodicChecks(intervalMs: number = 60000): void {
    if (this.checkInterval) {
      clearInterval(this.checkInterval)
    }

    this.checkInterval = setInterval(async () => {
      try {
        const health = await this.performHealthCheck()
        this.lastHealthCheck = health
        
        // 记录健康状态指标
        performanceMonitor.recordMetric('system_health_overall', 
          health.overall === 'healthy' ? 1 : health.overall === 'warning' ? 0.5 : 0, 
          'score'
        )
        
        // 如果系统状态不健康，记录警告
        if (health.overall !== 'healthy') {
          console.warn('系统健康检查发现问题:', {
            status: health.overall,
            criticalIssues: health.checks.filter(c => c.status === 'critical').length,
            warningIssues: health.checks.filter(c => c.status === 'warning').length
          })
        }
      } catch (error) {
        console.error('健康检查执行失败:', error)
      }
    }, intervalMs)

    console.log(`健康检查已启动，检查间隔: ${intervalMs}ms`)
  }

  /**
   * 停止定期健康检查
   */
  stopPeriodicChecks(): void {
    if (this.checkInterval) {
      clearInterval(this.checkInterval)
      this.checkInterval = null
      console.log('健康检查已停止')
    }
  }

  /**
   * 执行完整的系统健康检查
   */
  async performHealthCheck(): Promise<SystemHealth> {
    const checks: HealthCheckResult[] = []
    const startTime = Date.now()

    // 并行执行所有健康检查
    const checkPromises = [
      this.checkDatabase(),
      this.checkTaskQueue(),
      this.checkFileSystem(),
      this.checkMemoryUsage(),
      this.checkCpuUsage(),
      this.checkDiskSpace(),
      this.checkDatabasePerformance(),
      this.checkActiveConnections(),
      this.checkErrorRates(),
      this.checkResponseTimes()
    ]

    const results = await Promise.allSettled(checkPromises)
    
    results.forEach((result, index) => {
      if (result.status === 'fulfilled') {
        checks.push(result.value)
      } else {
        checks.push({
          status: 'critical',
          component: `health_check_${index}`,
          message: `健康检查执行失败: ${result.reason}`,
          timestamp: new Date()
        })
      }
    })

    // 计算总体健康状态
    const criticalCount = checks.filter(c => c.status === 'critical').length
    const warningCount = checks.filter(c => c.status === 'warning').length
    const healthyCount = checks.filter(c => c.status === 'healthy').length

    let overall: 'healthy' | 'warning' | 'critical'
    if (criticalCount > 0) {
      overall = 'critical'
    } else if (warningCount > 0) {
      overall = 'warning'
    } else {
      overall = 'healthy'
    }

    const totalTime = Date.now() - startTime
    const uptime = Date.now() - this.startTime.getTime()

    return {
      overall,
      checks,
      summary: {
        healthy: healthyCount,
        warning: warningCount,
        critical: criticalCount
      },
      uptime,
      timestamp: new Date()
    }
  }

  /**
   * 检查数据库连接
   */
  private async checkDatabase(): Promise<HealthCheckResult> {
    const startTime = Date.now()
    
    try {
      await prisma.$queryRaw`SELECT 1`
      const responseTime = Date.now() - startTime
      
      return {
        status: responseTime > 1000 ? 'warning' : 'healthy',
        component: 'database',
        message: responseTime > 1000 ? '数据库响应较慢' : '数据库连接正常',
        details: { responseTime },
        responseTime,
        timestamp: new Date()
      }
    } catch (error) {
      return {
        status: 'critical',
        component: 'database',
        message: `数据库连接失败: ${error.message}`,
        details: { error: error.message },
        responseTime: Date.now() - startTime,
        timestamp: new Date()
      }
    }
  }

  /**
   * 检查任务队列状态
   */
  private async checkTaskQueue(): Promise<HealthCheckResult> {
    try {
      const taskQueue = TaskQueue.getInstance()
      const stats = await taskQueue.getStats()
      
      let status: 'healthy' | 'warning' | 'critical' = 'healthy'
      let message = '任务队列运行正常'
      
      if (stats.pending > 100) {
        status = 'warning'
        message = '待处理任务数量较多'
      }
      
      if (stats.failed > stats.completed * 0.1) {
        status = 'critical'
        message = '任务失败率过高'
      }
      
      return {
        status,
        component: 'task_queue',
        message,
        details: stats,
        timestamp: new Date()
      }
    } catch (error) {
      return {
        status: 'critical',
        component: 'task_queue',
        message: `任务队列检查失败: ${error.message}`,
        timestamp: new Date()
      }
    }
  }

  /**
   * 检查文件系统
   */
  private async checkFileSystem(): Promise<HealthCheckResult> {
    try {
      const fs = require('fs').promises
      const path = require('path')
      
      const uploadsDir = path.join(process.cwd(), 'uploads')
      const tempDir = path.join(process.cwd(), 'temp')
      
      // 检查目录是否存在和可写
      await fs.access(uploadsDir, fs.constants.W_OK)
      await fs.access(tempDir, fs.constants.W_OK)
      
      // 检查磁盘空间
      const stats = await fs.stat(uploadsDir)
      
      return {
        status: 'healthy',
        component: 'filesystem',
        message: '文件系统正常',
        details: { uploadsDir, tempDir },
        timestamp: new Date()
      }
    } catch (error) {
      return {
        status: 'critical',
        component: 'filesystem',
        message: `文件系统检查失败: ${error.message}`,
        timestamp: new Date()
      }
    }
  }

  /**
   * 检查内存使用情况
   */
  private async checkMemoryUsage(): Promise<HealthCheckResult> {
    const memoryUsage = process.memoryUsage()
    const totalMemory = memoryUsage.heapTotal
    const usedMemory = memoryUsage.heapUsed
    const memoryUsagePercent = (usedMemory / totalMemory) * 100
    
    let status: 'healthy' | 'warning' | 'critical' = 'healthy'
    let message = '内存使用正常'
    
    if (memoryUsagePercent > 90) {
      status = 'critical'
      message = '内存使用率过高'
    } else if (memoryUsagePercent > 75) {
      status = 'warning'
      message = '内存使用率较高'
    }
    
    return {
      status,
      component: 'memory',
      message,
      details: {
        usedMB: Math.round(usedMemory / 1024 / 1024),
        totalMB: Math.round(totalMemory / 1024 / 1024),
        usagePercent: Math.round(memoryUsagePercent)
      },
      timestamp: new Date()
    }
  }

  /**
   * 检查CPU使用情况
   */
  private async checkCpuUsage(): Promise<HealthCheckResult> {
    // 简单的CPU使用率检查（基于事件循环延迟）
    const startTime = process.hrtime()
    
    await new Promise(resolve => setImmediate(resolve))
    
    const [seconds, nanoseconds] = process.hrtime(startTime)
    const delay = seconds * 1000 + nanoseconds / 1000000 // 转换为毫秒
    
    let status: 'healthy' | 'warning' | 'critical' = 'healthy'
    let message = 'CPU使用正常'
    
    if (delay > 100) {
      status = 'critical'
      message = 'CPU负载过高'
    } else if (delay > 50) {
      status = 'warning'
      message = 'CPU负载较高'
    }
    
    return {
      status,
      component: 'cpu',
      message,
      details: { eventLoopDelay: Math.round(delay) },
      timestamp: new Date()
    }
  }

  /**
   * 检查磁盘空间
   */
  private async checkDiskSpace(): Promise<HealthCheckResult> {
    try {
      const fs = require('fs')
      const path = require('path')
      
      const uploadsDir = path.join(process.cwd(), 'uploads')
      
      // 获取目录大小（简化实现）
      let totalSize = 0
      const files = fs.readdirSync(uploadsDir)
      
      for (const file of files) {
        const filePath = path.join(uploadsDir, file)
        const stats = fs.statSync(filePath)
        totalSize += stats.size
      }
      
      const totalSizeMB = totalSize / 1024 / 1024
      const maxSizeMB = 1000 // 1GB限制
      
      let status: 'healthy' | 'warning' | 'critical' = 'healthy'
      let message = '磁盘空间充足'
      
      if (totalSizeMB > maxSizeMB * 0.9) {
        status = 'critical'
        message = '磁盘空间不足'
      } else if (totalSizeMB > maxSizeMB * 0.75) {
        status = 'warning'
        message = '磁盘空间使用较多'
      }
      
      return {
        status,
        component: 'disk',
        message,
        details: {
          usedMB: Math.round(totalSizeMB),
          maxMB: maxSizeMB,
          usagePercent: Math.round((totalSizeMB / maxSizeMB) * 100)
        },
        timestamp: new Date()
      }
    } catch (error) {
      return {
        status: 'warning',
        component: 'disk',
        message: `磁盘空间检查失败: ${error.message}`,
        timestamp: new Date()
      }
    }
  }

  /**
   * 检查数据库性能
   */
  private async checkDatabasePerformance(): Promise<HealthCheckResult> {
    const stats = databaseOptimizer.getQueryStats(300000) // 5分钟内的统计
    
    let status: 'healthy' | 'warning' | 'critical' = 'healthy'
    let message = '数据库性能正常'
    
    if (stats.slowQueries > 10) {
      status = 'critical'
      message = '慢查询数量过多'
    } else if (stats.slowQueries > 5) {
      status = 'warning'
      message = '存在慢查询'
    }
    
    if (stats.averageDuration > 500) {
      status = status === 'critical' ? 'critical' : 'warning'
      message = '数据库平均响应时间较长'
    }
    
    return {
      status,
      component: 'database_performance',
      message,
      details: {
        totalQueries: stats.totalQueries,
        slowQueries: stats.slowQueries,
        averageDuration: Math.round(stats.averageDuration)
      },
      timestamp: new Date()
    }
  }

  /**
   * 检查活跃连接数
   */
  private async checkActiveConnections(): Promise<HealthCheckResult> {
    try {
      // 检查数据库连接池状态
      const connectionCount = await prisma.$queryRaw`
        SELECT COUNT(*) as count FROM pragma_database_list
      ` as any[]
      
      return {
        status: 'healthy',
        component: 'connections',
        message: '连接状态正常',
        details: { activeConnections: connectionCount[0]?.count || 0 },
        timestamp: new Date()
      }
    } catch (error) {
      return {
        status: 'warning',
        component: 'connections',
        message: `连接检查失败: ${error.message}`,
        timestamp: new Date()
      }
    }
  }

  /**
   * 检查错误率
   */
  private async checkErrorRates(): Promise<HealthCheckResult> {
    try {
      const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000)
      
      const totalLogs = await prisma.taskLog.count({
        where: {
          timestamp: { gte: fiveMinutesAgo }
        }
      })
      
      const errorLogs = await prisma.taskLog.count({
        where: {
          timestamp: { gte: fiveMinutesAgo },
          level: 'ERROR'
        }
      })
      
      const errorRate = totalLogs > 0 ? (errorLogs / totalLogs) * 100 : 0
      
      let status: 'healthy' | 'warning' | 'critical' = 'healthy'
      let message = '错误率正常'
      
      if (errorRate > 10) {
        status = 'critical'
        message = '错误率过高'
      } else if (errorRate > 5) {
        status = 'warning'
        message = '错误率较高'
      }
      
      return {
        status,
        component: 'error_rate',
        message,
        details: {
          totalLogs,
          errorLogs,
          errorRate: Math.round(errorRate * 100) / 100
        },
        timestamp: new Date()
      }
    } catch (error) {
      return {
        status: 'warning',
        component: 'error_rate',
        message: `错误率检查失败: ${error.message}`,
        timestamp: new Date()
      }
    }
  }

  /**
   * 检查响应时间
   */
  private async checkResponseTimes(): Promise<HealthCheckResult> {
    const metrics = performanceMonitor.getMetrics(300000) // 5分钟内的指标
    const responseTimeMetrics = metrics.filter(m => m.name.includes('response_time'))
    
    if (responseTimeMetrics.length === 0) {
      return {
        status: 'healthy',
        component: 'response_time',
        message: '暂无响应时间数据',
        timestamp: new Date()
      }
    }
    
    const avgResponseTime = responseTimeMetrics.reduce((sum, m) => sum + m.value, 0) / responseTimeMetrics.length
    
    let status: 'healthy' | 'warning' | 'critical' = 'healthy'
    let message = '响应时间正常'
    
    if (avgResponseTime > 5000) {
      status = 'critical'
      message = '平均响应时间过长'
    } else if (avgResponseTime > 2000) {
      status = 'warning'
      message = '平均响应时间较长'
    }
    
    return {
      status,
      component: 'response_time',
      message,
      details: {
        averageMs: Math.round(avgResponseTime),
        sampleCount: responseTimeMetrics.length
      },
      timestamp: new Date()
    }
  }

  /**
   * 获取最近的健康检查结果
   */
  getLastHealthCheck(): SystemHealth | null {
    return this.lastHealthCheck
  }

  /**
   * 获取系统运行时间
   */
  getUptime(): number {
    return Date.now() - this.startTime.getTime()
  }

  /**
   * 获取健康检查历史
   */
  async getHealthHistory(hours: number = 24): Promise<SystemHealth[]> {
    // 在实际实现中，这里应该从数据库或缓存中获取历史数据
    // 目前返回模拟数据
    const history: SystemHealth[] = []
    const now = Date.now()
    const interval = (hours * 60 * 60 * 1000) / 24 // 每小时一个数据点
    
    for (let i = 0; i < 24; i++) {
      const timestamp = new Date(now - i * interval)
      history.push({
        overall: 'healthy',
        checks: [],
        summary: { healthy: 10, warning: 0, critical: 0 },
        uptime: this.getUptime(),
        timestamp
      })
    }
    
    return history.reverse()
  }
}

// 导出单例实例
export const healthCheckService = HealthCheckService.getInstance()