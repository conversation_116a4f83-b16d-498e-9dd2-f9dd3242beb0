import { prisma } from '@/lib/prisma'
import type { TaskError } from '@/types/batch'
import { TaskStatus, ProductStatus, LogLevel } from '@prisma/client'

/**
 * 错误处理和重试服务
 * 负责任务执行过程中的错误处理、重试逻辑和故障恢复
 */
export class ErrorHandlingService {
  // 重试配置
  private static readonly RETRY_CONFIG = {
    maxAttempts: 3,
    baseDelay: 1000, // 1秒
    maxDelay: 30000, // 30秒
    backoffMultiplier: 2
  }

  // 错误类型分类
  private static readonly ERROR_TYPES = {
    RECOVERABLE: 'recoverable', // 可恢复错误
    PERMANENT: 'permanent', // 永久性错误
    SYSTEM: 'system', // 系统错误
    VALIDATION: 'validation' // 验证错误
  }

  /**
   * 处理任务错误
   * @param taskId 任务ID
   * @param error 错误信息
   * @param context 错误上下文
   */
  static async handleTaskError(
    taskId: string,
    error: Error,
    context?: any
  ): Promise<void> {
    try {
      const errorType = this.classifyError(error)
      const taskError: TaskError = {
        code: this.getErrorCode(error),
        message: error.message,
        details: {
          stack: error.stack,
          context,
          type: errorType,
          timestamp: new Date()
        },
        timestamp: new Date()
      }

      // 记录错误日志
      await this.logError(taskId, taskError)

      // 根据错误类型决定处理策略
      switch (errorType) {
        case this.ERROR_TYPES.RECOVERABLE:
          await this.handleRecoverableError(taskId, taskError)
          break
        case this.ERROR_TYPES.PERMANENT:
          await this.handlePermanentError(taskId, taskError)
          break
        case this.ERROR_TYPES.SYSTEM:
          await this.handleSystemError(taskId, taskError)
          break
        case this.ERROR_TYPES.VALIDATION:
          await this.handleValidationError(taskId, taskError)
          break
        default:
          await this.handleUnknownError(taskId, taskError)
      }
    } catch (handlingError) {
      console.error('处理任务错误时发生异常:', handlingError)
      // 记录处理错误的日志
      await this.logCriticalError(taskId, handlingError)
    }
  }

  /**
   * 处理商品处理错误
   * @param taskId 任务ID
   * @param productId 商品ID
   * @param error 错误信息
   */
  static async handleProductError(
    taskId: string,
    productId: string,
    error: Error
  ): Promise<boolean> {
    try {
      const errorType = this.classifyError(error)
      
      // 更新商品状态为失败
      await prisma.product.updateMany({
        where: {
          taskId,
          productId
        },
        data: {
          status: ProductStatus.FAILED,
          errorMessage: error.message,
          updatedAt: new Date()
        }
      })

      // 记录商品错误日志
      await this.logProductError(taskId, productId, error)

      // 检查是否需要重试
      if (errorType === this.ERROR_TYPES.RECOVERABLE) {
        return await this.shouldRetryProduct(taskId, productId)
      }

      return false
    } catch (handlingError) {
      console.error('处理商品错误时发生异常:', handlingError)
      return false
    }
  }

  /**
   * 重试失败的任务
   * @param taskId 任务ID
   * @returns 新任务ID或null
   */
  static async retryTask(taskId: string): Promise<string | null> {
    try {
      const task = await prisma.task.findUnique({
        where: { id: taskId },
        include: {
          products: true,
          logs: {
            where: { level: LogLevel.ERROR },
            orderBy: { createdAt: 'desc' },
            take: 1
          }
        }
      })

      if (!task || task.status !== TaskStatus.FAILED) {
        throw new Error('任务不存在或状态不正确')
      }

      // 检查重试次数
      const retryCount = await this.getRetryCount(taskId)
      if (retryCount >= this.RETRY_CONFIG.maxAttempts) {
        await this.logInfo(taskId, '任务重试次数已达上限，停止重试')
        return null
      }

      // 创建重试任务
      const retryTask = await prisma.task.create({
        data: {
          name: `${task.name} (重试 ${retryCount + 1})`,
          type: task.type,
          status: TaskStatus.PENDING,
          priority: task.priority + 1, // 提高优先级
          config: task.config,
          totalItems: task.totalItems,
          products: {
            create: task.products.map(p => ({
              productId: p.productId,
              status: ProductStatus.PENDING
            }))
          }
        }
      })

      // 记录重试日志
      await this.logInfo(retryTask.id, `从任务 ${taskId} 创建重试任务 (第${retryCount + 1}次重试)`)
      await this.logInfo(taskId, `创建重试任务: ${retryTask.id}`)

      return retryTask.id
    } catch (error) {
      console.error('重试任务失败:', error)
      await this.logError(taskId, {
        code: 'RETRY_FAILED',
        message: '重试任务失败',
        details: error,
        timestamp: new Date()
      })
      return null
    }
  }

  /**
   * 重试失败的商品
   * @param taskId 任务ID
   * @param productId 商品ID
   */
  static async retryProduct(taskId: string, productId: string): Promise<boolean> {
    try {
      // 重置商品状态
      await prisma.product.updateMany({
        where: {
          taskId,
          productId,
          status: ProductStatus.FAILED
        },
        data: {
          status: ProductStatus.PENDING,
          errorMessage: null,
          updatedAt: new Date()
        }
      })

      await this.logInfo(taskId, `重试商品: ${productId}`)
      return true
    } catch (error) {
      console.error('重试商品失败:', error)
      return false
    }
  }

  /**
   * 系统故障恢复
   */
  static async performSystemRecovery(): Promise<void> {
    try {
      console.log('开始系统故障恢复...')

      // 1. 恢复中断的任务
      await this.recoverInterruptedTasks()

      // 2. 清理孤立的处理状态
      await this.cleanupOrphanedProcessingStates()

      // 3. 修复数据不一致
      await this.fixDataInconsistencies()

      // 4. 清理过期的临时文件
      await this.cleanupExpiredTempFiles()

      console.log('系统故障恢复完成')
    } catch (error) {
      console.error('系统故障恢复失败:', error)
      throw error
    }
  }

  /**
   * 分类错误类型
   */
  private static classifyError(error: Error): string {
    const message = error.message.toLowerCase()
    const stack = error.stack?.toLowerCase() || ''

    // 网络相关错误 - 可恢复
    if (message.includes('network') || message.includes('timeout') || 
        message.includes('connection') || message.includes('econnreset')) {
      return this.ERROR_TYPES.RECOVERABLE
    }

    // 文件系统错误 - 部分可恢复
    if (message.includes('enoent') || message.includes('file not found')) {
      return this.ERROR_TYPES.PERMANENT
    }

    // 内存不足 - 系统错误
    if (message.includes('out of memory') || message.includes('heap')) {
      return this.ERROR_TYPES.SYSTEM
    }

    // 验证错误 - 永久性
    if (message.includes('validation') || message.includes('invalid') ||
        message.includes('format')) {
      return this.ERROR_TYPES.VALIDATION
    }

    // 数据库错误 - 可能可恢复
    if (message.includes('database') || message.includes('prisma') ||
        message.includes('sql')) {
      return this.ERROR_TYPES.RECOVERABLE
    }

    // 默认为可恢复
    return this.ERROR_TYPES.RECOVERABLE
  }

  /**
   * 获取错误代码
   */
  private static getErrorCode(error: Error): string {
    if (error.name) {
      return error.name.toUpperCase()
    }
    
    const message = error.message.toLowerCase()
    if (message.includes('timeout')) return 'TIMEOUT'
    if (message.includes('network')) return 'NETWORK_ERROR'
    if (message.includes('validation')) return 'VALIDATION_ERROR'
    if (message.includes('file')) return 'FILE_ERROR'
    if (message.includes('memory')) return 'MEMORY_ERROR'
    
    return 'UNKNOWN_ERROR'
  }

  /**
   * 处理可恢复错误
   */
  private static async handleRecoverableError(taskId: string, error: TaskError): Promise<void> {
    const retryCount = await this.getRetryCount(taskId)
    
    if (retryCount < this.RETRY_CONFIG.maxAttempts) {
      // 计算延迟时间
      const delay = Math.min(
        this.RETRY_CONFIG.baseDelay * Math.pow(this.RETRY_CONFIG.backoffMultiplier, retryCount),
        this.RETRY_CONFIG.maxDelay
      )
      
      await this.logInfo(taskId, `可恢复错误，将在 ${delay}ms 后重试 (第${retryCount + 1}次)`)
      
      // 延迟后重试
      setTimeout(async () => {
        await this.retryTask(taskId)
      }, delay)
    } else {
      await this.handlePermanentError(taskId, error)
    }
  }

  /**
   * 处理永久性错误
   */
  private static async handlePermanentError(taskId: string, error: TaskError): Promise<void> {
    await prisma.task.update({
      where: { id: taskId },
      data: {
        status: TaskStatus.FAILED,
        completedAt: new Date()
      }
    })
    
    await this.logError(taskId, {
      ...error,
      message: `永久性错误，任务终止: ${error.message}`
    })
  }

  /**
   * 处理系统错误
   */
  private static async handleSystemError(taskId: string, error: TaskError): Promise<void> {
    // 暂停任务
    await prisma.task.update({
      where: { id: taskId },
      data: { status: TaskStatus.PENDING }
    })
    
    await this.logError(taskId, {
      ...error,
      message: `系统错误，任务已暂停: ${error.message}`
    })
    
    // 触发系统健康检查
    // 这里可以集成监控系统
  }

  /**
   * 处理验证错误
   */
  private static async handleValidationError(taskId: string, error: TaskError): Promise<void> {
    await this.handlePermanentError(taskId, error)
  }

  /**
   * 处理未知错误
   */
  private static async handleUnknownError(taskId: string, error: TaskError): Promise<void> {
    await this.logError(taskId, {
      ...error,
      message: `未知错误类型: ${error.message}`
    })
    
    // 默认按可恢复错误处理
    await this.handleRecoverableError(taskId, error)
  }

  /**
   * 获取任务重试次数
   */
  private static async getRetryCount(taskId: string): Promise<number> {
    const logs = await prisma.taskLog.findMany({
      where: {
        taskId,
        message: { contains: '重试' }
      }
    })
    
    return logs.length
  }

  /**
   * 检查商品是否应该重试
   */
  private static async shouldRetryProduct(taskId: string, productId: string): Promise<boolean> {
    const errorLogs = await prisma.taskLog.count({
      where: {
        taskId,
        level: LogLevel.ERROR,
        message: { contains: productId }
      }
    })
    
    return errorLogs < this.RETRY_CONFIG.maxAttempts
  }

  /**
   * 恢复中断的任务
   */
  private static async recoverInterruptedTasks(): Promise<void> {
    // 将所有RUNNING状态的任务重置为PENDING
    const result = await prisma.task.updateMany({
      where: { status: TaskStatus.RUNNING },
      data: {
        status: TaskStatus.PENDING,
        updatedAt: new Date()
      }
    })
    
    if (result.count > 0) {
      console.log(`恢复了 ${result.count} 个中断的任务`)
    }
  }

  /**
   * 清理孤立的处理状态
   */
  private static async cleanupOrphanedProcessingStates(): Promise<void> {
    const result = await prisma.product.updateMany({
      where: { status: ProductStatus.PROCESSING },
      data: {
        status: ProductStatus.PENDING,
        updatedAt: new Date()
      }
    })
    
    if (result.count > 0) {
      console.log(`清理了 ${result.count} 个孤立的处理状态`)
    }
  }

  /**
   * 修复数据不一致
   */
  private static async fixDataInconsistencies(): Promise<void> {
    // 修复任务统计数据
    const tasks = await prisma.task.findMany({
      include: {
        products: true
      }
    })
    
    for (const task of tasks) {
      const processedItems = task.products.filter(p => 
        p.status === ProductStatus.COMPLETED
      ).length
      
      const failedItems = task.products.filter(p => 
        p.status === ProductStatus.FAILED
      ).length
      
      if (task.processedItems !== processedItems || task.failedItems !== failedItems) {
        await prisma.task.update({
          where: { id: task.id },
          data: {
            processedItems,
            failedItems,
            updatedAt: new Date()
          }
        })
      }
    }
  }

  /**
   * 清理过期的临时文件
   */
  private static async cleanupExpiredTempFiles(): Promise<void> {
    const fs = require('fs')
    const path = require('path')
    const tempDir = path.join(process.cwd(), 'uploads', 'temp')
    
    if (!fs.existsSync(tempDir)) {
      return
    }
    
    const files = fs.readdirSync(tempDir)
    const now = Date.now()
    const maxAge = 24 * 60 * 60 * 1000 // 24小时
    let cleanedCount = 0
    
    files.forEach((file: string) => {
      const filePath = path.join(tempDir, file)
      try {
        const stats = fs.statSync(filePath)
        if (now - stats.mtime.getTime() > maxAge) {
          fs.unlinkSync(filePath)
          cleanedCount++
        }
      } catch (error) {
        console.error(`清理临时文件失败: ${filePath}`, error)
      }
    })
    
    if (cleanedCount > 0) {
      console.log(`清理了 ${cleanedCount} 个过期的临时文件`)
    }
  }

  /**
   * 记录错误日志
   */
  private static async logError(taskId: string, error: TaskError): Promise<void> {
    try {
      await prisma.taskLog.create({
        data: {
          taskId,
          level: LogLevel.ERROR,
          message: error.message,
          details: JSON.stringify(error.details)
        }
      })
    } catch (logError) {
      console.error('记录错误日志失败:', logError)
    }
  }

  /**
   * 记录商品错误日志
   */
  private static async logProductError(
    taskId: string,
    productId: string,
    error: Error
  ): Promise<void> {
    try {
      await prisma.taskLog.create({
        data: {
          taskId,
          level: LogLevel.ERROR,
          message: `商品 ${productId} 处理失败: ${error.message}`,
          details: JSON.stringify({
            productId,
            error: error.message,
            stack: error.stack
          })
        }
      })
    } catch (logError) {
      console.error('记录商品错误日志失败:', logError)
    }
  }

  /**
   * 记录信息日志
   */
  private static async logInfo(taskId: string, message: string): Promise<void> {
    try {
      await prisma.taskLog.create({
        data: {
          taskId,
          level: LogLevel.INFO,
          message
        }
      })
    } catch (logError) {
      console.error('记录信息日志失败:', logError)
    }
  }

  /**
   * 记录严重错误日志
   */
  private static async logCriticalError(taskId: string, error: any): Promise<void> {
    try {
      await prisma.taskLog.create({
        data: {
          taskId,
          level: LogLevel.ERROR,
          message: '严重错误：错误处理过程中发生异常',
          details: JSON.stringify({
            error: error.message,
            stack: error.stack
          })
        }
      })
    } catch (logError) {
      console.error('记录严重错误日志失败:', logError)
    }
  }
}

// 导出单例实例
export const errorHandlingService = ErrorHandlingService