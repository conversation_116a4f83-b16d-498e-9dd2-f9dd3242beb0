/**
 * 监控模块导出
 */

export { ErrorHandlingService, errorHandlingService } from './ErrorHandlingService'
export { TaskMonitorService, taskMonitorService } from './TaskMonitorService'
export { taskMonitorService as taskMonitor } from './TaskMonitorService'
export { HealthCheckService, healthCheckService } from './HealthCheckService'
export { LogAggregationService, logAggregationService } from './LogAggregationService'

// 导出相关类型
export type {
  ErrorReport,
  ErrorPattern,
  ErrorSummary
} from './ErrorHandlingService'

// TaskMonitorService types are imported from @/types/batch

export type {
  HealthCheckResult,
  SystemHealth
} from './HealthCheckService'

export type {
  LogQuery,
  LogAggregation,
  LogPattern
} from './LogAggregationService'