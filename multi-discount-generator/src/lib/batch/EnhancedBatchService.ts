import { BatchExcelService } from './BatchExcelService'
import type { FormData, TimeSlot, ExcelRowData } from '@/types'

/**
 * 增强的批量处理服务
 * 支持更大数据量和更复杂的配置需求
 */
export class EnhancedBatchService {

  /**
   * 配置选项
   */
  private static readonly CONFIG = {
    MAX_PRODUCTS_PER_CHUNK: 100,    // 每批处理的商品数量
    MAX_MEMORY_USAGE: 500 * 1024 * 1024, // 最大内存使用量 (500MB)
    PROGRESS_UPDATE_INTERVAL: 50,    // 进度更新间隔
    ENABLE_PARALLEL_PROCESSING: true, // 启用并行处理
    MAX_CONCURRENT_WORKERS: 4        // 最大并发工作线程数
  }

  /**
   * 批量生成Excel数据 - 增强版
   */
  static async generateBatchExcelDataEnhanced(
    productIds: string[],
    formData: FormData,
    timeSlots: TimeSlot[],
    options: {
      onProgress?: (processed: number, total: number) => void
      onMemoryWarning?: (usage: number) => void
      enableOptimization?: boolean
    } = {}
  ): Promise<ExcelRowData[]> {
    const { onProgress, onMemoryWarning, enableOptimization = true } = options
    
    console.log(`开始批量生成，商品数量: ${productIds.length}, 时间段数量: ${timeSlots.length}`)
    
    const totalItems = productIds.length * timeSlots.length
    let processedItems = 0
    const allData: ExcelRowData[] = []

    // 内存监控
    const checkMemoryUsage = () => {
      if (typeof process !== 'undefined' && process.memoryUsage) {
        const usage = process.memoryUsage()
        if (usage.heapUsed > this.CONFIG.MAX_MEMORY_USAGE) {
          onMemoryWarning?.(usage.heapUsed)
          // 强制垃圾回收
          if (global.gc) {
            global.gc()
          }
        }
      }
    }

    try {
      // 分批处理商品
      for (let i = 0; i < productIds.length; i += this.CONFIG.MAX_PRODUCTS_PER_CHUNK) {
        const chunk = productIds.slice(i, i + this.CONFIG.MAX_PRODUCTS_PER_CHUNK)
        
        let chunkData: ExcelRowData[]
        
        if (enableOptimization && this.CONFIG.ENABLE_PARALLEL_PROCESSING && chunk.length > 10) {
          // 并行处理大批量数据
          chunkData = await this.processChunkParallel(chunk, formData, timeSlots)
        } else {
          // 串行处理小批量数据
          chunkData = await this.processChunkSerial(chunk, formData, timeSlots)
        }
        
        allData.push(...chunkData)
        processedItems += chunk.length * timeSlots.length

        // 更新进度
        if (onProgress && processedItems % this.CONFIG.PROGRESS_UPDATE_INTERVAL === 0) {
          onProgress(processedItems, totalItems)
        }

        // 内存检查
        checkMemoryUsage()

        // 让出控制权，避免阻塞
        await new Promise(resolve => setTimeout(resolve, 0))
      }

      // 最终进度更新
      onProgress?.(totalItems, totalItems)
      
      console.log(`批量生成完成，总记录数: ${allData.length}`)
      return allData

    } catch (error) {
      console.error('批量生成失败:', error)
      throw new Error(`批量生成失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 串行处理数据块
   */
  private static async processChunkSerial(
    productIds: string[],
    formData: FormData,
    timeSlots: TimeSlot[]
  ): Promise<ExcelRowData[]> {
    const chunkData: ExcelRowData[] = []

    for (const productId of productIds) {
      for (const timeSlot of timeSlots) {
        const rowData = this.createExcelRowData(productId, formData, timeSlot)
        chunkData.push(rowData)
      }
    }

    return chunkData
  }

  /**
   * 并行处理数据块
   */
  private static async processChunkParallel(
    productIds: string[],
    formData: FormData,
    timeSlots: TimeSlot[]
  ): Promise<ExcelRowData[]> {
    const workerCount = Math.min(this.CONFIG.MAX_CONCURRENT_WORKERS, productIds.length)
    const chunkSize = Math.ceil(productIds.length / workerCount)
    
    const promises = []
    
    for (let i = 0; i < workerCount; i++) {
      const start = i * chunkSize
      const end = Math.min(start + chunkSize, productIds.length)
      const workerProductIds = productIds.slice(start, end)
      
      promises.push(this.processChunkSerial(workerProductIds, formData, timeSlots))
    }

    const results = await Promise.all(promises)
    return results.flat()
  }

  /**
   * 创建Excel行数据
   */
  private static createExcelRowData(
    productId: string,
    formData: FormData,
    timeSlot: TimeSlot
  ): ExcelRowData {
    return {
      '活动类型': '多件多折',
      '商品ID': productId,
      '开始时间': this.formatDateTime(timeSlot.startTime),
      '结束时间': this.formatDateTime(timeSlot.endTime),
      '活动区域': this.formatRegion(formData.region),
      '满几件': formData.minQuantity.toString(),
      '折扣-打几折': formData.discount.toString(),
      '立减金额-多件总额下的扣减金额': ''
    }
  }

  /**
   * 格式化日期时间
   */
  private static formatDateTime(date: Date): string {
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false
    }).replace(/\//g, '-')
  }

  /**
   * 格式化区域
   */
  private static formatRegion(region: string | string[]): string {
    if (Array.isArray(region)) {
      return region.length === 0 ? '全国' : region.join(',')
    }
    return region || '全国'
  }

  /**
   * 智能时间生成
   * 根据配置复杂度选择最优的时间生成策略
   */
  static async generateOptimizedTimeSlots(
    config: {
      mode: 'continuous' | 'random'
      startDate: Date
      endDate: Date
      dailyStartTime: string
      dailyEndTime: string
      days?: number
      weekendFullDay?: boolean
      holidayFullDay?: boolean
      timeSlotCount?: number
      timeSlotDuration?: number
    }
  ): Promise<TimeSlot[]> {
    const { mode, startDate, endDate } = config

    try {
      if (mode === 'continuous') {
        return await this.generateContinuousTimeSlots(config)
      } else {
        return await this.generateRandomTimeSlots(config)
      }
    } catch (error) {
      console.error('时间生成失败:', error)
      throw new Error(`时间生成失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 生成连续时间段
   */
  private static async generateContinuousTimeSlots(config: any): Promise<TimeSlot[]> {
    const timeSlots: TimeSlot[] = []
    const { startDate, endDate, dailyStartTime, dailyEndTime, weekendFullDay, holidayFullDay } = config

    const currentDate = new Date(startDate)
    const endDateTime = new Date(endDate)

    while (currentDate <= endDateTime) {
      const dayOfWeek = currentDate.getDay()
      const isWeekend = dayOfWeek === 0 || dayOfWeek === 6
      
      // 检查是否为节假日（这里简化处理）
      const isHoliday = false // 实际应该调用节假日API

      let startTime: Date
      let endTime: Date

      if ((isWeekend && weekendFullDay) || (isHoliday && holidayFullDay)) {
        // 全天
        startTime = new Date(currentDate)
        startTime.setHours(0, 0, 0, 0)
        endTime = new Date(currentDate)
        endTime.setHours(23, 59, 59, 999)
      } else {
        // 指定时间段
        const [startHour, startMinute] = dailyStartTime.split(':').map(Number)
        const [endHour, endMinute] = dailyEndTime.split(':').map(Number)

        startTime = new Date(currentDate)
        startTime.setHours(startHour, startMinute, 0, 0)
        
        endTime = new Date(currentDate)
        endTime.setHours(endHour, endMinute, 59, 999)

        // 处理跨天情况
        if (endTime <= startTime) {
          endTime.setDate(endTime.getDate() + 1)
        }
      }

      timeSlots.push({ startTime, endTime })
      currentDate.setDate(currentDate.getDate() + 1)
    }

    return timeSlots
  }

  /**
   * 生成随机时间段
   */
  private static async generateRandomTimeSlots(config: any): Promise<TimeSlot[]> {
    const { startDate, endDate, timeSlotCount = 10, timeSlotDuration = 2 } = config
    const timeSlots: TimeSlot[] = []

    const totalDays = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24))
    const slotsPerDay = Math.ceil(timeSlotCount / totalDays)

    for (let day = 0; day < totalDays; day++) {
      const currentDate = new Date(startDate)
      currentDate.setDate(currentDate.getDate() + day)

      for (let slot = 0; slot < slotsPerDay && timeSlots.length < timeSlotCount; slot++) {
        const randomHour = Math.floor(Math.random() * 20) + 2 // 2-22点
        const randomMinute = Math.floor(Math.random() * 60)

        const startTime = new Date(currentDate)
        startTime.setHours(randomHour, randomMinute, 0, 0)

        const endTime = new Date(startTime)
        endTime.setHours(endTime.getHours() + timeSlotDuration)

        timeSlots.push({ startTime, endTime })
      }
    }

    // 按时间排序
    timeSlots.sort((a, b) => a.startTime.getTime() - b.startTime.getTime())

    return timeSlots.slice(0, timeSlotCount)
  }

  /**
   * 估算内存使用量
   */
  static estimateMemoryUsage(productCount: number, timeSlotCount: number): {
    estimatedMB: number
    isWithinLimit: boolean
    recommendation: string
  } {
    // 每条记录大约占用的内存（字节）
    const bytesPerRecord = 500
    const totalRecords = productCount * timeSlotCount
    const estimatedBytes = totalRecords * bytesPerRecord
    const estimatedMB = estimatedBytes / (1024 * 1024)

    const isWithinLimit = estimatedBytes < this.CONFIG.MAX_MEMORY_USAGE

    let recommendation = ''
    if (!isWithinLimit) {
      recommendation = `数据量较大，建议分批处理或减少时间段数量。当前估算: ${estimatedMB.toFixed(1)}MB`
    } else if (estimatedMB > 100) {
      recommendation = `数据量中等，建议启用优化模式。当前估算: ${estimatedMB.toFixed(1)}MB`
    } else {
      recommendation = `数据量较小，可以正常处理。当前估算: ${estimatedMB.toFixed(1)}MB`
    }

    return {
      estimatedMB: Math.round(estimatedMB * 10) / 10,
      isWithinLimit,
      recommendation
    }
  }
}
