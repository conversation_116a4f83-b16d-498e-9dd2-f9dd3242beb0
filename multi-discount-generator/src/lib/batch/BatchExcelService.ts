import * as XLSX from 'xlsx'
import { createWriteStream, WriteStream } from 'fs'
import { join } from 'path'
import { ExcelService } from '@/lib/excel'
import type { 
  ExcelRowData, 
  FormData, 
  TimeSlot 
} from '@/types'

/**
 * 批量Excel生成服务
 * 负责大批量数据的Excel文件生成，支持流式写入和内存优化
 */
export class BatchExcelService {
  private static config = {
    threshold: 100, // 超过100个商品使用批量处理
    chunkSize: 50, // 每次处理50个商品
    maxRetries: 3,
    timeoutPerItem: 30000 // 30秒超时
  }

  /**
   * 批量生成Excel数据
   * @param productIds 商品ID列表
   * @param formData 表单配置
   * @param timeSlots 时间段列表
   * @returns Promise<ExcelRowData[]>
   */
  static async generateBatchExcelData(
    productIds: string[],
    formData: FormData,
    timeSlots: TimeSlot[]
  ): Promise<ExcelRowData[]> {
    try {
      const allData: ExcelRowData[] = []
      
      // 分批处理商品
      for (let i = 0; i < productIds.length; i += this.config.chunkSize) {
        const chunk = productIds.slice(i, i + this.config.chunkSize)
        const chunkData = await this.processProductChunk(chunk, formData, timeSlots)
        allData.push(...chunkData)
        
        // 释放内存压力
        if (allData.length % 1000 === 0) {
          // 强制垃圾回收（如果可用）
          if (global.gc) {
            global.gc()
          }
        }
      }
      
      return allData
    } catch (error) {
      console.error('批量生成Excel数据失败:', error)
      throw new Error('批量生成Excel数据失败')
    }
  }

  /**
   * 处理商品批次
   * @param productIds 商品ID批次
   * @param formData 表单配置
   * @param timeSlots 时间段列表
   * @returns Promise<ExcelRowData[]>
   */
  private static async processProductChunk(
    productIds: string[],
    formData: FormData,
    timeSlots: TimeSlot[]
  ): Promise<ExcelRowData[]> {
    const chunkData: ExcelRowData[] = []
    
    for (const productId of productIds) {
      const productFormData = { ...formData, productId }
      const productData = ExcelService.generateExcelData(productFormData, timeSlots)
      chunkData.push(...productData)
    }
    
    return chunkData
  }

  /**
   * 生成批量Excel文件
   * @param data 数据数组
   * @param config 配置
   * @returns Promise<Buffer>
   */
  async generateBatchExcel(data: any[], config: any): Promise<Buffer> {
    try {
      // 创建工作簿
      const workbook = XLSX.utils.book_new()
      
      // 转换数据为工作表格式
      const worksheet = XLSX.utils.json_to_sheet(data)
      
      // 添加工作表到工作簿
      XLSX.utils.book_append_sheet(workbook, worksheet, '批量数据')
      
      // 生成Excel缓冲区
      const buffer = XLSX.write(workbook, { 
        type: 'buffer', 
        bookType: 'xlsx' 
      })
      
      return buffer
    } catch (error) {
      console.error('生成批量Excel失败:', error)
      throw new Error('生成批量Excel失败')
    }
  }

  /**
   * 解析Excel文件
   * @param filePath 文件路径
   * @returns Promise<any[]>
   */
  async parseExcelFile(filePath: string): Promise<any[]> {
    try {
      // 读取Excel文件
      const workbook = XLSX.readFile(filePath)
      
      // 获取第一个工作表
      const sheetName = workbook.SheetNames[0]
      const worksheet = workbook.Sheets[sheetName]
      
      // 转换为JSON数据
      const data = XLSX.utils.sheet_to_json(worksheet)
      
      return data
    } catch (error) {
      console.error('解析Excel文件失败:', error)
      throw new Error('解析Excel文件失败')
    }
  }

  /**
   * 流式生成Excel文件
   * @param productIds 商品ID列表
   * @param formData 表单配置
   * @param timeSlots 时间段列表
   * @param outputPath 输出文件路径
   * @param onProgress 进度回调
   * @returns Promise<string>
   */
  static async generateBatchExcelFile(
    productIds: string[],
    formData: FormData,
    timeSlots: TimeSlot[],
    outputPath: string,
    onProgress?: (processed: number, total: number) => void
  ): Promise<string> {
    try {
      // 创建工作簿
      const workbook = XLSX.utils.book_new()
      const worksheetData: any[][] = []
      
      // 添加标题行
      const headers = [
        '活动类型',
        '商品ID',
        '开始时间\n示例：\n2020-03-27 00:00:00',
        '结束时间\n示例：\n2020-03-27 23:59:59',
        '活动区域\n支持填写"全国"及5大区\n5大区：华中,西南/西北,华南,华东,华北',
        '满几件\n说明：表示满N件，件数只能在下拉框中选择',
        '折扣-打几折\n说明：表示打几折，折扣只能下拉选择',
        '立减金额-多件总额下的扣减金额\n表示扣减的金额，单位元，支持最多2位小数，与折扣只能择一生效'
      ]
      worksheetData.push(headers)
      
      let processedCount = 0
      
      // 分批处理商品
      for (let i = 0; i < productIds.length; i += this.config.chunkSize) {
        const chunk = productIds.slice(i, i + this.config.chunkSize)
        
        for (const productId of chunk) {
          const productFormData = { ...formData, productId }
          const productData = ExcelService.generateExcelData(productFormData, timeSlots)
          
          // 转换为数组格式
          productData.forEach(row => {
            worksheetData.push([
              row.活动类型,
              row.商品ID,
              row['开始时间\n示例：\n2020-03-27 00:00:00'],
              row['结束时间\n示例：\n2020-03-27 23:59:59'],
              row['活动区域\n支持填写"全国"及5大区\n5大区：华中,西南/西北,华南,华东,华北'],
              row['满几件\n说明：表示满N件，件数只能在下拉框中选择'],
              row['折扣-打几折\n说明：表示打几折，折扣只能下拉选择'],
              row['立减金额-多件总额下的扣减金额\n表示扣减的金额，单位元，支持最多2位小数，与折扣只能择一生效']
            ])
          })
          
          processedCount++
          
          // 调用进度回调
          if (onProgress) {
            onProgress(processedCount, productIds.length)
          }
        }
        
        // 定期清理内存
        if (processedCount % 100 === 0 && global.gc) {
          global.gc()
        }
      }
      
      // 创建工作表
      const worksheet = XLSX.utils.aoa_to_sheet(worksheetData)
      
      // 设置列宽
      worksheet['!cols'] = [
        { wch: 12 }, // 活动类型
        { wch: 15 }, // 商品ID
        { wch: 20 }, // 开始时间
        { wch: 20 }, // 结束时间
        { wch: 25 }, // 活动区域
        { wch: 15 }, // 满几件
        { wch: 15 }, // 折扣
        { wch: 20 }  // 立减金额
      ]
      
      // 设置行高
      worksheet['!rows'] = [
        { hpt: 60 }, // 标题行
        ...Array(worksheetData.length - 1).fill({ hpt: 20 })
      ]
      
      // 添加工作表到工作簿
      XLSX.utils.book_append_sheet(workbook, worksheet, '多件多折活动')
      
      // 保存文件
      XLSX.writeFile(workbook, outputPath, {
        bookType: 'xlsx',
        compression: true
      })
      
      return outputPath
    } catch (error) {
      console.error('流式生成Excel文件失败:', error)
      throw new Error('流式生成Excel文件失败')
    }
  }

  /**
   * 估算内存使用量
   * @param productCount 商品数量
   * @param timeSlotCount 时间段数量
   * @returns 估算的内存使用量（字节）
   */
  static estimateMemoryUsage(productCount: number, timeSlotCount: number): number {
    // 每行数据大约占用的内存（字节）
    const bytesPerRow = 200
    const totalRows = productCount * timeSlotCount
    return totalRows * bytesPerRow
  }

  /**
   * 检查是否应该使用分片处理
   * @param productCount 商品数量
   * @param timeSlotCount 时间段数量
   * @param availableMemory 可用内存（字节）
   * @returns 是否需要分片
   */
  static shouldUseSharting(
    productCount: number,
    timeSlotCount: number,
    availableMemory: number = 500 * 1024 * 1024 // 默认500MB
  ): boolean {
    const estimatedMemory = this.estimateMemoryUsage(productCount, timeSlotCount)
    return estimatedMemory > availableMemory * 0.8 // 使用80%内存阈值
  }

  /**
   * 获取批量处理配置
   */
  static getConfig() {
    return { ...this.config }
  }

  /**
   * 更新批量处理配置
   * @param newConfig 新配置
   */
  static updateConfig(newConfig: Partial<typeof this.config>): void {
    this.config = { ...this.config, ...newConfig }
  }

  /**
   * 清理临时数据
   */
  static cleanup(): void {
    // 强制垃圾回收
    if (global.gc) {
      global.gc()
    }
  }
}