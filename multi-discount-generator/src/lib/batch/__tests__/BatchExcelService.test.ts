import { BatchExcelService } from '../BatchExcelService'
import { TestDataGenerator } from '@/lib/testing/TestDataGenerator'
import { prisma } from '@/lib/prisma'

// Mock dependencies
jest.mock('@/lib/prisma', () => ({
  prisma: {
    task: {
      findUnique: jest.fn(),
      update: jest.fn()
    },
    taskLog: {
      create: jest.fn()
    },
    file: {
      create: jest.fn()
    }
  }
}))

jest.mock('xlsx')
jest.mock('fs/promises')
jest.mock('@/lib/time-generator')

describe('BatchExcelService', () => {
  let batchExcelService: BatchExcelService
  const mockPrisma = prisma as jest.Mocked<typeof prisma>

  beforeEach(() => {
    batchExcelService = new BatchExcelService()
    jest.clearAllMocks()
  })

  describe('generateBatchExcel', () => {
    it('should generate Excel file for batch processing', async () => {
      const taskId = 'task-123'
      const batchData = {
        products: ['PROD001', 'PROD002', 'PROD003'],
        config: TestDataGenerator.generateFormData({
          discount: 8.0,
          minQuantity: 2,
          timeMode: 'continuous'
        })
      }

      const mockTask = {
        id: taskId,
        name: '批量测试任务',
        status: 'RUNNING'
      }

      const mockFile = {
        id: 'file-123',
        filename: 'batch-excel-123.xlsx',
        path: '/uploads/batch-excel-123.xlsx'
      }

      mockPrisma.task.findUnique.mockResolvedValue(mockTask)
      mockPrisma.file.create.mockResolvedValue(mockFile)

      // Mock XLSX
      const XLSX = require('xlsx')
      XLSX.utils = {
        book_new: jest.fn().mockReturnValue({}),
        json_to_sheet: jest.fn().mockReturnValue({}),
        book_append_sheet: jest.fn()
      }
      XLSX.writeFile = jest.fn()

      // Mock time generator
      const timeGenerator = require('@/lib/time-generator')
      timeGenerator.generateTimeSlots = jest.fn().mockReturnValue([
        {
          startTime: '2024-01-01 10:00:00',
          endTime: '2024-01-01 12:00:00',
          date: '2024-01-01'
        }
      ])

      const result = await batchExcelService.generateBatchExcel(taskId, batchData)

      expect(result).toBe('/uploads/batch-excel-123.xlsx')
      expect(mockPrisma.task.findUnique).toHaveBeenCalledWith({
        where: { id: taskId }
      })
      expect(XLSX.utils.book_new).toHaveBeenCalled()
      expect(XLSX.writeFile).toHaveBeenCalled()
    })

    it('should throw error for non-existent task', async () => {
      const taskId = 'non-existent'
      const batchData = {
        products: ['PROD001'],
        config: TestDataGenerator.generateFormData()
      }

      mockPrisma.task.findUnique.mockResolvedValue(null)

      await expect(
        batchExcelService.generateBatchExcel(taskId, batchData)
      ).rejects.toThrow('任务不存在')
    })

    it('should handle empty product list', async () => {
      const taskId = 'task-123'
      const batchData = {
        products: [],
        config: TestDataGenerator.generateFormData()
      }

      const mockTask = {
        id: taskId,
        name: '空商品列表任务',
        status: 'RUNNING'
      }

      mockPrisma.task.findUnique.mockResolvedValue(mockTask)

      await expect(
        batchExcelService.generateBatchExcel(taskId, batchData)
      ).rejects.toThrow('商品列表不能为空')
    })

    it('should generate Excel with random time mode', async () => {
      const taskId = 'task-123'
      const batchData = {
        products: ['PROD001', 'PROD002'],
        config: TestDataGenerator.generateFormData({
          timeMode: 'random',
          timeSlotCount: 3,
          slotDuration: 2
        })
      }

      const mockTask = {
        id: taskId,
        name: '随机时间任务',
        status: 'RUNNING'
      }

      mockPrisma.task.findUnique.mockResolvedValue(mockTask)
      mockPrisma.file.create.mockResolvedValue({
        id: 'file-123',
        path: '/uploads/random-excel-123.xlsx'
      })

      const XLSX = require('xlsx')
      XLSX.utils = {
        book_new: jest.fn().mockReturnValue({}),
        json_to_sheet: jest.fn().mockReturnValue({}),
        book_append_sheet: jest.fn()
      }
      XLSX.writeFile = jest.fn()

      const timeGenerator = require('@/lib/time-generator')
      timeGenerator.generateRandomTimeSlots = jest.fn().mockReturnValue([
        {
          startTime: '2024-01-01 14:00:00',
          endTime: '2024-01-01 16:00:00',
          date: '2024-01-01'
        },
        {
          startTime: '2024-01-02 09:00:00',
          endTime: '2024-01-02 11:00:00',
          date: '2024-01-02'
        }
      ])

      const result = await batchExcelService.generateBatchExcel(taskId, batchData)

      expect(result).toBe('/uploads/random-excel-123.xlsx')
      expect(timeGenerator.generateRandomTimeSlots).toHaveBeenCalledWith(
        expect.any(Date),
        3,
        2
      )
    })
  })

  describe('generateSingleExcel', () => {
    it('should generate Excel file for single product', async () => {
      const productId = 'PROD001'
      const config = TestDataGenerator.generateFormData({
        productId,
        discount: 7.5,
        minQuantity: 1
      })

      const mockFile = {
        id: 'file-456',
        path: '/uploads/single-excel-456.xlsx'
      }

      mockPrisma.file.create.mockResolvedValue(mockFile)

      const XLSX = require('xlsx')
      XLSX.utils = {
        book_new: jest.fn().mockReturnValue({}),
        json_to_sheet: jest.fn().mockReturnValue({}),
        book_append_sheet: jest.fn()
      }
      XLSX.writeFile = jest.fn()

      const timeGenerator = require('@/lib/time-generator')
      timeGenerator.generateTimeSlots = jest.fn().mockReturnValue([
        {
          startTime: '2024-01-01 10:00:00',
          endTime: '2024-01-01 12:00:00',
          date: '2024-01-01'
        }
      ])

      const result = await batchExcelService.generateSingleExcel(config)

      expect(result).toBe('/uploads/single-excel-456.xlsx')
      expect(XLSX.utils.json_to_sheet).toHaveBeenCalled()
      expect(XLSX.writeFile).toHaveBeenCalled()
    })

    it('should handle regional configuration', async () => {
      const config = TestDataGenerator.generateFormData({
        region: {
          type: 'regional',
          regions: ['华东', '华南']
        }
      })

      mockPrisma.file.create.mockResolvedValue({
        id: 'file-789',
        path: '/uploads/regional-excel-789.xlsx'
      })

      const XLSX = require('xlsx')
      XLSX.utils = {
        book_new: jest.fn().mockReturnValue({}),
        json_to_sheet: jest.fn().mockReturnValue({}),
        book_append_sheet: jest.fn()
      }
      XLSX.writeFile = jest.fn()

      const timeGenerator = require('@/lib/time-generator')
      timeGenerator.generateTimeSlots = jest.fn().mockReturnValue([])

      await batchExcelService.generateSingleExcel(config)

      // 验证为每个区域生成了工作表
      expect(XLSX.utils.book_append_sheet).toHaveBeenCalledTimes(2)
    })
  })

  describe('validateBatchData', () => {
    it('should validate batch data successfully', () => {
      const validData = {
        products: ['PROD001', 'PROD002'],
        config: TestDataGenerator.generateFormData()
      }

      expect(() => {
        batchExcelService.validateBatchData(validData)
      }).not.toThrow()
    })

    it('should throw error for invalid product IDs', () => {
      const invalidData = {
        products: ['', 'INVALID@ID', 'TOOLONG-PRODUCT-ID-THAT-EXCEEDS-LIMIT'],
        config: TestDataGenerator.generateFormData()
      }

      expect(() => {
        batchExcelService.validateBatchData(invalidData)
      }).toThrow('发现格式不正确的商品ID')
    })

    it('should throw error for too many products', () => {
      const tooManyProducts = {
        products: Array.from({ length: 1001 }, (_, i) => `PROD${i.toString().padStart(4, '0')}`),
        config: TestDataGenerator.generateFormData()
      }

      expect(() => {
        batchExcelService.validateBatchData(tooManyProducts)
      }).toThrow('商品数量不能超过1000个')
    })

    it('should throw error for invalid discount', () => {
      const invalidDiscount = {
        products: ['PROD001'],
        config: TestDataGenerator.generateFormData({
          discount: 15.0 // 超出有效范围
        })
      }

      expect(() => {
        batchExcelService.validateBatchData(invalidDiscount)
      }).toThrow('折扣必须在5.0-10.0之间')
    })

    it('should throw error for invalid quantity', () => {
      const invalidQuantity = {
        products: ['PROD001'],
        config: TestDataGenerator.generateFormData({
          minQuantity: 0 // 无效数量
        })
      }

      expect(() => {
        batchExcelService.validateBatchData(invalidQuantity)
      }).toThrow('最小数量必须大于0')
    })
  })

  describe('getGenerationProgress', () => {
    it('should return generation progress', async () => {
      const taskId = 'task-123'
      
      const mockTask = {
        id: taskId,
        progress: 75,
        status: 'RUNNING'
      }

      mockPrisma.task.findUnique.mockResolvedValue(mockTask)

      const result = await batchExcelService.getGenerationProgress(taskId)

      expect(result).toEqual({
        progress: 75,
        status: 'RUNNING'
      })
    })

    it('should return null for non-existent task', async () => {
      const taskId = 'non-existent'
      mockPrisma.task.findUnique.mockResolvedValue(null)

      const result = await batchExcelService.getGenerationProgress(taskId)

      expect(result).toBeNull()
    })
  })

  describe('cancelGeneration', () => {
    it('should cancel Excel generation', async () => {
      const taskId = 'task-123'
      
      const mockTask = {
        id: taskId,
        status: 'RUNNING'
      }

      const mockUpdatedTask = {
        id: taskId,
        status: 'CANCELLED'
      }

      mockPrisma.task.findUnique.mockResolvedValue(mockTask)
      mockPrisma.task.update.mockResolvedValue(mockUpdatedTask)

      const result = await batchExcelService.cancelGeneration(taskId)

      expect(result.success).toBe(true)
      expect(mockPrisma.task.update).toHaveBeenCalledWith({
        where: { id: taskId },
        data: {
          status: 'CANCELLED',
          updatedAt: expect.any(Date)
        }
      })
    })

    it('should not cancel completed generation', async () => {
      const taskId = 'task-123'
      
      const mockTask = {
        id: taskId,
        status: 'COMPLETED'
      }

      mockPrisma.task.findUnique.mockResolvedValue(mockTask)

      const result = await batchExcelService.cancelGeneration(taskId)

      expect(result.success).toBe(false)
      expect(result.error).toContain('无法取消已完成的任务')
    })
  })
})