import { TaskQueue } from '../TaskQueue'
import { TestDataGenerator } from '@/lib/testing/TestDataGenerator'
import { prisma } from '@/lib/prisma'
import { TaskStatus } from '@prisma/client'

// Mock dependencies
jest.mock('@/lib/prisma', () => ({
  prisma: {
    task: {
      create: jest.fn(),
      findUnique: jest.fn(),
      findMany: jest.fn(),
      update: jest.fn(),
      count: jest.fn(),
      groupBy: jest.fn()
    },
    taskLog: {
      create: jest.fn()
    }
  }
}))

jest.mock('@/lib/performance/PerformanceMonitor', () => ({
  performanceMonitor: {
    recordMetric: jest.fn()
  }
}))

describe('TaskQueue', () => {
  let taskQueue: TaskQueue
  const mockPrisma = prisma as jest.Mocked<typeof prisma>

  beforeEach(() => {
    taskQueue = TaskQueue.getInstance()
    jest.clearAllMocks()
  })

  afterEach(async () => {
    await taskQueue.stop()
  })

  describe('createTask', () => {
    it('should create a new task successfully', async () => {
      const taskInput = TestDataGenerator.generateTaskInput({
        name: '测试任务',
        products: ['PROD001', 'PROD002']
      })

      const mockTask = {
        id: 'task-123',
        name: taskInput.name,
        type: taskInput.type,
        status: TaskStatus.PENDING,
        config: taskInput.config,
        priority: taskInput.priority,
        progress: 0,
        createdAt: new Date(),
        updatedAt: new Date()
      }

      mockPrisma.task.create.mockResolvedValue(mockTask)

      const result = await taskQueue.createTask(taskInput)

      expect(result).toEqual(mockTask)
      expect(mockPrisma.task.create).toHaveBeenCalledWith({
        data: {
          name: taskInput.name,
          type: taskInput.type,
          status: TaskStatus.PENDING,
          config: taskInput.config,
          priority: taskInput.priority,
          progress: 0
        }
      })
    })

    it('should throw error for invalid task input', async () => {
      const invalidTaskInput = {
        name: '',
        type: 'INVALID' as any,
        config: {},
        products: [],
        priority: -1
      }

      await expect(taskQueue.createTask(invalidTaskInput)).rejects.toThrow()
    })
  })

  describe('getTask', () => {
    it('should return task by id', async () => {
      const taskId = 'task-123'
      const mockTask = {
        id: taskId,
        name: '测试任务',
        status: TaskStatus.PENDING
      }

      mockPrisma.task.findUnique.mockResolvedValue(mockTask)

      const result = await taskQueue.getTask(taskId)

      expect(result).toEqual(mockTask)
      expect(mockPrisma.task.findUnique).toHaveBeenCalledWith({
        where: { id: taskId },
        include: {
          logs: {
            orderBy: { timestamp: 'desc' },
            take: 10
          }
        }
      })
    })

    it('should return null for non-existent task', async () => {
      const taskId = 'non-existent'
      mockPrisma.task.findUnique.mockResolvedValue(null)

      const result = await taskQueue.getTask(taskId)

      expect(result).toBeNull()
    })
  })

  describe('updateTaskStatus', () => {
    it('should update task status successfully', async () => {
      const taskId = 'task-123'
      const newStatus = TaskStatus.RUNNING
      const progress = 50

      const mockUpdatedTask = {
        id: taskId,
        status: newStatus,
        progress,
        updatedAt: new Date()
      }

      mockPrisma.task.update.mockResolvedValue(mockUpdatedTask)

      const result = await taskQueue.updateTaskStatus(taskId, newStatus, progress)

      expect(result).toEqual(mockUpdatedTask)
      expect(mockPrisma.task.update).toHaveBeenCalledWith({
        where: { id: taskId },
        data: {
          status: newStatus,
          progress,
          updatedAt: expect.any(Date)
        }
      })
    })
  })

  describe('getStats', () => {
    it('should return task statistics', async () => {
      const mockStats = [
        { status: TaskStatus.PENDING, _count: { status: 5 } },
        { status: TaskStatus.RUNNING, _count: { status: 2 } },
        { status: TaskStatus.COMPLETED, _count: { status: 10 } },
        { status: TaskStatus.FAILED, _count: { status: 1 } }
      ]

      mockPrisma.task.groupBy.mockResolvedValue(mockStats)

      const result = await taskQueue.getStats()

      expect(result).toEqual({
        pending: 5,
        running: 2,
        completed: 10,
        failed: 1,
        total: 18
      })
    })
  })

  describe('start and stop', () => {
    it('should start and stop task queue', async () => {
      expect(taskQueue.isRunning()).toBe(false)

      await taskQueue.start()
      expect(taskQueue.isRunning()).toBe(true)

      await taskQueue.stop()
      expect(taskQueue.isRunning()).toBe(false)
    })

    it('should not start if already running', async () => {
      await taskQueue.start()
      
      // 尝试再次启动应该不会有问题
      await taskQueue.start()
      expect(taskQueue.isRunning()).toBe(true)
    })
  })

  describe('pauseTask and resumeTask', () => {
    it('should pause and resume task', async () => {
      const taskId = 'task-123'
      
      const mockTask = {
        id: taskId,
        status: TaskStatus.RUNNING
      }

      const mockPausedTask = {
        id: taskId,
        status: TaskStatus.PAUSED
      }

      const mockResumedTask = {
        id: taskId,
        status: TaskStatus.RUNNING
      }

      mockPrisma.task.findUnique.mockResolvedValue(mockTask)
      mockPrisma.task.update
        .mockResolvedValueOnce(mockPausedTask)
        .mockResolvedValueOnce(mockResumedTask)

      // 暂停任务
      const pauseResult = await taskQueue.pauseTask(taskId)
      expect(pauseResult.status).toBe(TaskStatus.PAUSED)

      // 恢复任务
      const resumeResult = await taskQueue.resumeTask(taskId)
      expect(resumeResult.status).toBe(TaskStatus.RUNNING)
    })
  })

  describe('cancelTask', () => {
    it('should cancel task successfully', async () => {
      const taskId = 'task-123'
      
      const mockTask = {
        id: taskId,
        status: TaskStatus.RUNNING
      }

      const mockCancelledTask = {
        id: taskId,
        status: TaskStatus.CANCELLED
      }

      mockPrisma.task.findUnique.mockResolvedValue(mockTask)
      mockPrisma.task.update.mockResolvedValue(mockCancelledTask)

      const result = await taskQueue.cancelTask(taskId)

      expect(result.status).toBe(TaskStatus.CANCELLED)
      expect(mockPrisma.task.update).toHaveBeenCalledWith({
        where: { id: taskId },
        data: {
          status: TaskStatus.CANCELLED,
          updatedAt: expect.any(Date)
        }
      })
    })

    it('should not cancel completed task', async () => {
      const taskId = 'task-123'
      
      const mockTask = {
        id: taskId,
        status: TaskStatus.COMPLETED
      }

      mockPrisma.task.findUnique.mockResolvedValue(mockTask)

      await expect(taskQueue.cancelTask(taskId)).rejects.toThrow('无法取消已完成的任务')
    })
  })

  describe('listTasks', () => {
    it('should return paginated task list', async () => {
      const mockTasks = [
        { id: 'task-1', name: '任务1', status: TaskStatus.PENDING },
        { id: 'task-2', name: '任务2', status: TaskStatus.RUNNING }
      ]

      mockPrisma.task.findMany.mockResolvedValue(mockTasks)
      mockPrisma.task.count.mockResolvedValue(2)

      const result = await taskQueue.listTasks({
        page: 1,
        limit: 10,
        status: TaskStatus.PENDING
      })

      expect(result.tasks).toEqual(mockTasks)
      expect(result.total).toBe(2)
      expect(result.page).toBe(1)
      expect(result.limit).toBe(10)
    })

    it('should filter tasks by status', async () => {
      const status = TaskStatus.RUNNING
      
      await taskQueue.listTasks({ status })

      expect(mockPrisma.task.findMany).toHaveBeenCalledWith({
        where: { status },
        orderBy: { createdAt: 'desc' },
        skip: 0,
        take: 20,
        include: {
          logs: {
            orderBy: { timestamp: 'desc' },
            take: 5
          }
        }
      })
    })
  })

  describe('cleanup', () => {
    it('should cleanup completed tasks older than specified days', async () => {
      const retentionDays = 30
      const cutoffDate = new Date(Date.now() - retentionDays * 24 * 60 * 60 * 1000)

      mockPrisma.task.findMany.mockResolvedValue([
        { id: 'task-1', status: TaskStatus.COMPLETED },
        { id: 'task-2', status: TaskStatus.COMPLETED }
      ])

      const result = await taskQueue.cleanup(retentionDays)

      expect(mockPrisma.task.findMany).toHaveBeenCalledWith({
        where: {
          status: { in: [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED] },
          completedAt: { lt: expect.any(Date) }
        },
        select: { id: true }
      })

      expect(result.cleanedTasks).toBeGreaterThanOrEqual(0)
    })
  })
})