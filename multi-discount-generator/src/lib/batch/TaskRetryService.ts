import { prisma } from '@/lib/prisma'
import { TaskStatus } from '@prisma/client'
import { globalErrorHandler } from '@/lib/error/GlobalErrorHandler'
import { performanceMonitor } from '@/lib/performance/PerformanceMonitor'

interface RetryConfig {
  maxAttempts: number
  baseDelay: number
  maxDelay: number
  backoffMultiplier: number
  jitterEnabled: boolean
  retryableErrors: string[]
  nonRetryableErrors: string[]
}

interface RetryAttempt {
  attemptNumber: number
  timestamp: Date
  error: string
  duration: number
  nextRetryAt?: Date
}

/**
 * 任务重试服务
 * 处理任务执行失败后的重试逻辑
 */
export class TaskRetryService {
  private static instance: TaskRetryService
  private defaultConfig: RetryConfig = {
    maxAttempts: 3,
    baseDelay: 1000, // 1秒
    maxDelay: 300000, // 5分钟
    backoffMultiplier: 2,
    jitterEnabled: true,
    retryableErrors: [
      'connection',
      'timeout',
      'network',
      'temporary',
      'rate_limit',
      'service_unavailable'
    ],
    nonRetryableErrors: [
      'validation',
      'permission',
      'authentication',
      'not_found',
      'invalid_input',
      'malformed'
    ]
  }

  private retryTimers = new Map<string, NodeJS.Timeout>()

  private constructor() {}

  static getInstance(): TaskRetryService {
    if (!TaskRetryService.instance) {
      TaskRetryService.instance = new TaskRetryService()
    }
    return TaskRetryService.instance
  }

  /**
   * 处理任务失败，决定是否重试
   */
  async handleTaskFailure(
    taskId: string,
    error: Error,
    config?: Partial<RetryConfig>
  ): Promise<{
    shouldRetry: boolean
    nextRetryAt?: Date
    reason: string
  }> {
    const finalConfig = { ...this.defaultConfig, ...config }
    
    try {
      // 获取任务信息
      const task = await prisma.task.findUnique({
        where: { id: taskId },
        include: {
          logs: {
            where: { level: 'ERROR' },
            orderBy: { timestamp: 'desc' }
          }
        }
      })

      if (!task) {
        return {
          shouldRetry: false,
          reason: 'task_not_found'
        }
      }

      // 检查是否已达到最大重试次数
      const currentAttempts = this.getRetryAttempts(task.logs)
      if (currentAttempts.length >= finalConfig.maxAttempts) {
        await this.markTaskAsFinallyFailed(taskId, error, currentAttempts)
        return {
          shouldRetry: false,
          reason: 'max_attempts_reached'
        }
      }

      // 检查错误是否可重试
      const isRetryable = this.isErrorRetryable(error, finalConfig)
      if (!isRetryable) {
        await this.markTaskAsFinallyFailed(taskId, error, currentAttempts)
        return {
          shouldRetry: false,
          reason: 'non_retryable_error'
        }
      }

      // 计算下次重试时间
      const nextRetryAt = this.calculateNextRetryTime(
        currentAttempts.length + 1,
        finalConfig
      )

      // 记录重试尝试
      await this.recordRetryAttempt(taskId, error, currentAttempts.length + 1, nextRetryAt)

      // 调度重试
      await this.scheduleRetry(taskId, nextRetryAt)

      performanceMonitor.recordMetric('task_retry_scheduled', 1, 'count', {
        taskId,
        attempt: currentAttempts.length + 1,
        errorType: error.constructor.name
      })

      return {
        shouldRetry: true,
        nextRetryAt,
        reason: 'retry_scheduled'
      }

    } catch (handlingError) {
      console.error('处理任务失败时发生错误:', handlingError)
      
      await globalErrorHandler.handleError(handlingError, {
        taskId,
        timestamp: new Date(),
        additionalData: {
          originalError: error.message,
          source: 'task_retry_service'
        }
      })

      return {
        shouldRetry: false,
        reason: 'retry_handling_failed'
      }
    }
  }

  /**
   * 执行任务重试
   */
  async executeRetry(taskId: string): Promise<{
    success: boolean
    error?: Error
  }> {
    try {
      // 更新任务状态为重试中
      await prisma.task.update({
        where: { id: taskId },
        data: {
          status: TaskStatus.RUNNING,
          updatedAt: new Date()
        }
      })

      // 记录重试开始
      await prisma.taskLog.create({
        data: {
          taskId,
          level: 'INFO',
          message: '开始重试任务执行',
          timestamp: new Date(),
          metadata: {
            source: 'task_retry_service',
            action: 'retry_started'
          }
        }
      })

      performanceMonitor.recordMetric('task_retry_executed', 1, 'count', {
        taskId
      })

      // 这里应该调用实际的任务执行逻辑
      // 由于任务执行逻辑在TaskWorker中，我们需要通知TaskQueue重新处理这个任务
      
      return { success: true }

    } catch (error) {
      console.error('执行任务重试失败:', error)
      
      await prisma.taskLog.create({
        data: {
          taskId,
          level: 'ERROR',
          message: `重试执行失败: ${error.message}`,
          timestamp: new Date(),
          metadata: {
            source: 'task_retry_service',
            action: 'retry_failed',
            error: error.stack
          }
        }
      })

      return { success: false, error }
    }
  }

  /**
   * 取消任务重试
   */
  async cancelRetry(taskId: string): Promise<void> {
    const timer = this.retryTimers.get(taskId)
    if (timer) {
      clearTimeout(timer)
      this.retryTimers.delete(taskId)
    }

    await prisma.taskLog.create({
      data: {
        taskId,
        level: 'INFO',
        message: '任务重试已取消',
        timestamp: new Date(),
        metadata: {
          source: 'task_retry_service',
          action: 'retry_cancelled'
        }
      }
    })

    performanceMonitor.recordMetric('task_retry_cancelled', 1, 'count', {
      taskId
    })
  }

  /**
   * 获取任务重试统计
   */
  async getRetryStats(timeWindow: number = 86400000): Promise<{
    totalRetries: number
    successfulRetries: number
    failedRetries: number
    retriesByAttempt: Record<number, number>
    retriesByError: Record<string, number>
    averageRetryDelay: number
    successRate: number
  }> {
    const startTime = new Date(Date.now() - timeWindow)
    
    // 获取重试相关的日志
    const retryLogs = await prisma.taskLog.findMany({
      where: {
        timestamp: { gte: startTime },
        metadata: {
          path: ['source'],
          equals: 'task_retry_service'
        }
      }
    })

    const totalRetries = retryLogs.filter(log => 
      (log.metadata as any)?.action === 'retry_started'
    ).length

    const successfulRetries = retryLogs.filter(log => 
      (log.metadata as any)?.action === 'retry_successful'
    ).length

    const failedRetries = retryLogs.filter(log => 
      (log.metadata as any)?.action === 'retry_failed'
    ).length

    // 按重试次数分组
    const retriesByAttempt: Record<number, number> = {}
    retryLogs.forEach(log => {
      const attempt = (log.metadata as any)?.attempt
      if (attempt) {
        retriesByAttempt[attempt] = (retriesByAttempt[attempt] || 0) + 1
      }
    })

    // 按错误类型分组
    const retriesByError: Record<string, number> = {}
    retryLogs.forEach(log => {
      const errorType = (log.metadata as any)?.errorType
      if (errorType) {
        retriesByError[errorType] = (retriesByError[errorType] || 0) + 1
      }
    })

    // 计算平均重试延迟
    const retryDelays = retryLogs
      .filter(log => (log.metadata as any)?.retryDelay)
      .map(log => (log.metadata as any).retryDelay)
    
    const averageRetryDelay = retryDelays.length > 0
      ? retryDelays.reduce((sum, delay) => sum + delay, 0) / retryDelays.length
      : 0

    const successRate = totalRetries > 0 ? (successfulRetries / totalRetries) * 100 : 0

    return {
      totalRetries,
      successfulRetries,
      failedRetries,
      retriesByAttempt,
      retriesByError,
      averageRetryDelay,
      successRate
    }
  }

  /**
   * 清理过期的重试定时器
   */
  cleanup(): void {
    this.retryTimers.forEach((timer, taskId) => {
      clearTimeout(timer)
    })
    this.retryTimers.clear()
  }

  // 私有方法

  private getRetryAttempts(logs: any[]): RetryAttempt[] {
    return logs
      .filter(log => (log.metadata as any)?.source === 'task_retry_service')
      .map(log => ({
        attemptNumber: (log.metadata as any)?.attempt || 1,
        timestamp: log.timestamp,
        error: log.message,
        duration: (log.metadata as any)?.duration || 0,
        nextRetryAt: (log.metadata as any)?.nextRetryAt 
          ? new Date((log.metadata as any).nextRetryAt)
          : undefined
      }))
      .sort((a, b) => a.attemptNumber - b.attemptNumber)
  }

  private isErrorRetryable(error: Error, config: RetryConfig): boolean {
    const errorMessage = error.message.toLowerCase()
    
    // 检查非重试错误
    for (const nonRetryableError of config.nonRetryableErrors) {
      if (errorMessage.includes(nonRetryableError.toLowerCase())) {
        return false
      }
    }

    // 检查可重试错误
    for (const retryableError of config.retryableErrors) {
      if (errorMessage.includes(retryableError.toLowerCase())) {
        return true
      }
    }

    // 默认情况下，未知错误可以重试
    return true
  }

  private calculateNextRetryTime(attemptNumber: number, config: RetryConfig): Date {
    let delay = config.baseDelay * Math.pow(config.backoffMultiplier, attemptNumber - 1)
    
    // 限制最大延迟
    delay = Math.min(delay, config.maxDelay)
    
    // 添加抖动以避免雷群效应
    if (config.jitterEnabled) {
      const jitter = delay * 0.1 * Math.random()
      delay += jitter
    }
    
    return new Date(Date.now() + delay)
  }

  private async recordRetryAttempt(
    taskId: string,
    error: Error,
    attemptNumber: number,
    nextRetryAt: Date
  ): Promise<void> {
    await prisma.taskLog.create({
      data: {
        taskId,
        level: 'WARN',
        message: `任务执行失败，将在 ${nextRetryAt.toISOString()} 进行第 ${attemptNumber} 次重试`,
        timestamp: new Date(),
        metadata: {
          source: 'task_retry_service',
          action: 'retry_scheduled',
          attempt: attemptNumber,
          error: error.message,
          errorType: error.constructor.name,
          nextRetryAt: nextRetryAt.toISOString(),
          retryDelay: nextRetryAt.getTime() - Date.now()
        }
      }
    })
  }

  private async scheduleRetry(taskId: string, retryAt: Date): Promise<void> {
    const delay = retryAt.getTime() - Date.now()
    
    if (delay <= 0) {
      // 立即重试
      await this.executeRetry(taskId)
      return
    }

    // 清除之前的定时器
    const existingTimer = this.retryTimers.get(taskId)
    if (existingTimer) {
      clearTimeout(existingTimer)
    }

    // 设置新的定时器
    const timer = setTimeout(async () => {
      this.retryTimers.delete(taskId)
      await this.executeRetry(taskId)
    }, delay)

    this.retryTimers.set(taskId, timer)
  }

  private async markTaskAsFinallyFailed(
    taskId: string,
    error: Error,
    attempts: RetryAttempt[]
  ): Promise<void> {
    await prisma.task.update({
      where: { id: taskId },
      data: {
        status: TaskStatus.FAILED,
        error: `任务最终失败，已重试 ${attempts.length} 次: ${error.message}`,
        completedAt: new Date(),
        updatedAt: new Date()
      }
    })

    await prisma.taskLog.create({
      data: {
        taskId,
        level: 'ERROR',
        message: `任务最终失败，已达到最大重试次数 (${attempts.length})`,
        timestamp: new Date(),
        metadata: {
          source: 'task_retry_service',
          action: 'final_failure',
          totalAttempts: attempts.length,
          finalError: error.message,
          errorType: error.constructor.name,
          attempts: attempts.map(a => ({
            attempt: a.attemptNumber,
            timestamp: a.timestamp,
            error: a.error
          }))
        }
      }
    })

    performanceMonitor.recordMetric('task_final_failure', 1, 'count', {
      taskId,
      totalAttempts: attempts.length,
      errorType: error.constructor.name
    })
  }
}

// 导出单例实例
export const taskRetryService = TaskRetryService.getInstance()