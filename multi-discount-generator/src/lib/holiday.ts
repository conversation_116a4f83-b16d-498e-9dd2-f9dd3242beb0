import { format, parseISO } from 'date-fns'
import type { HolidayInfo, HolidayCache } from '@/types/holiday'
import { 
  DEFAULT_HOLIDAYS_2025,
  HOLIDAY_CACHE_CONFIG,
  HOLIDAY_API_CONFIG 
} from '@/constants/holiday'

/**
 * 节假日处理模块
 * 负责中国法定节假日的查询和缓存
 */

// 内存缓存
let holidayCache: HolidayCache = {}

/**
 * 检查指定日期是否为中国法定节假日
 * @param date 要检查的日期
 * @returns 是否为节假日
 */
export function isChineseHoliday(date: Date): boolean {
  try {
    const dateStr = format(date, 'yyyy-MM-dd')
    const year = date.getFullYear()
    
    // 从缓存获取节假日数据
    const holidays = getHolidaysForYear(year)
    
    return holidays.some(holiday => holiday.date === dateStr && holiday.isOffDay)
  } catch (error) {
    console.warn('节假日检查失败，默认为工作日:', error)
    return false
  }
}

/**
 * 获取指定日期的节假日信息
 * @param date 要查询的日期
 * @returns 节假日信息或null
 */
export function getHolidayInfo(date: Date): HolidayInfo | null {
  try {
    const dateStr = format(date, 'yyyy-MM-dd')
    const year = date.getFullYear()
    
    const holidays = getHolidaysForYear(year)
    
    return holidays.find(holiday => holiday.date === dateStr) || null
  } catch (error) {
    console.warn('节假日信息获取失败:', error)
    return null
  }
}

/**
 * 获取指定年份的节假日数据
 * @param year 年份
 * @returns 节假日数组
 */
function getHolidaysForYear(year: number): HolidayInfo[] {
  // 检查缓存
  if (holidayCache[year]) {
    return holidayCache[year]
  }

  // 检查是否在浏览器环境
  if (typeof window !== 'undefined') {
    // 浏览器环境：使用默认数据
    if (year === 2025) {
      const defaultHolidays = DEFAULT_HOLIDAYS_2025.map(h => ({ ...h }))
      holidayCache[year] = defaultHolidays
      return defaultHolidays
    }
    
    // 其他年份返回空数组
    holidayCache[year] = []
    return []
  }

  // 服务端环境：尝试从chinese-holidays库获取数据
  try {
    const chineseHolidays = require('chinese-holidays')
    const holidays = chineseHolidays.getHolidays(year)
    
    const formattedHolidays: HolidayInfo[] = holidays.map((holiday: any) => ({
      date: holiday.date,
      name: holiday.name,
      isOffDay: holiday.isOffDay
    }))

    // 缓存数据
    holidayCache[year] = formattedHolidays
    
    return formattedHolidays
  } catch (error) {
    console.warn(`chinese-holidays库获取${year}年数据失败，使用默认数据:`, error)
    
    // 使用默认数据（目前只有2025年）
    if (year === 2025) {
      const defaultHolidays = DEFAULT_HOLIDAYS_2025.map(h => ({ ...h }))
      holidayCache[year] = defaultHolidays
      return defaultHolidays
    }
    
    // 其他年份返回空数组
    holidayCache[year] = []
    return []
  }
}

/**
 * 预加载指定年份的节假日数据
 * @param years 要预加载的年份数组
 */
export async function preloadHolidays(years: number[]): Promise<void> {
  const promises = years.map(year => {
    return new Promise<void>((resolve) => {
      try {
        getHolidaysForYear(year)
        resolve()
      } catch (error) {
        console.warn(`预加载${year}年节假日数据失败:`, error)
        resolve()
      }
    })
  })

  await Promise.all(promises)
}

/**
 * 清除节假日缓存
 * @param year 要清除的年份，不传则清除所有
 */
export function clearHolidayCache(year?: number): void {
  if (year) {
    delete holidayCache[year]
  } else {
    holidayCache = {}
  }
}

/**
 * 获取缓存状态
 * @returns 缓存信息
 */
export function getCacheStatus(): {
  cachedYears: number[]
  totalHolidays: number
} {
  const cachedYears = Object.keys(holidayCache).map(Number).sort()
  const totalHolidays = Object.values(holidayCache).reduce(
    (total, holidays) => total + holidays.length, 
    0
  )

  return {
    cachedYears,
    totalHolidays
  }
}

/**
 * 从API获取节假日数据（备用方案）
 * @param year 年份
 * @returns Promise<HolidayInfo[]>
 */
async function fetchHolidaysFromAPI(year: number): Promise<HolidayInfo[]> {
  // 这里可以集成第三方节假日API
  // 目前返回空数组，表示API不可用
  return new Promise((resolve) => {
    setTimeout(() => {
      console.warn(`节假日API暂不可用，年份: ${year}`)
      resolve([])
    }, HOLIDAY_API_CONFIG.TIMEOUT)
  })
}

/**
 * 验证节假日数据格式
 * @param holidays 节假日数据
 * @returns 是否有效
 */
function validateHolidayData(holidays: any[]): boolean {
  if (!Array.isArray(holidays)) {
    return false
  }

  return holidays.every(holiday => 
    typeof holiday.date === 'string' &&
    typeof holiday.name === 'string' &&
    typeof holiday.isOffDay === 'boolean' &&
    /^\d{4}-\d{2}-\d{2}$/.test(holiday.date)
  )
}

/**
 * 获取指定日期范围内的所有节假日
 * @param startDate 开始日期
 * @param endDate 结束日期
 * @returns 节假日数组
 */
export function getHolidaysInRange(startDate: Date, endDate: Date): HolidayInfo[] {
  const holidays: HolidayInfo[] = []
  const startYear = startDate.getFullYear()
  const endYear = endDate.getFullYear()

  // 获取涉及年份的所有节假日
  for (let year = startYear; year <= endYear; year++) {
    const yearHolidays = getHolidaysForYear(year)
    holidays.push(...yearHolidays)
  }

  // 过滤日期范围
  const startDateStr = format(startDate, 'yyyy-MM-dd')
  const endDateStr = format(endDate, 'yyyy-MM-dd')

  return holidays.filter(holiday => 
    holiday.date >= startDateStr && holiday.date <= endDateStr
  )
}

/**
 * 检查指定日期是否为工作日
 * @param date 要检查的日期
 * @returns 是否为工作日
 */
export function isWorkday(date: Date): boolean {
  const dayOfWeek = date.getDay() // 0=周日, 6=周六
  
  // 周末不是工作日
  if (dayOfWeek === 0 || dayOfWeek === 6) {
    return false
  }
  
  // 节假日不是工作日
  if (isChineseHoliday(date)) {
    return false
  }
  
  return true
}

/**
 * 获取下一个工作日
 * @param date 起始日期
 * @returns 下一个工作日
 */
export function getNextWorkday(date: Date): Date {
  let nextDay = new Date(date)
  nextDay.setDate(nextDay.getDate() + 1)
  
  while (!isWorkday(nextDay)) {
    nextDay.setDate(nextDay.getDate() + 1)
  }
  
  return nextDay
}