import * as XLSX from 'xlsx'
import { format } from 'date-fns'
import type { FormData, TimeSlot, ExcelRowData, RegionType } from '@/types'
import { 
  EXCEL_HEADERS, 
  EXCEL_COLUMN_WIDTHS, 
  EXCEL_CONFIG, 
  DEFAULT_VALUES,
  TIME_FORMAT 
} from '@/constants'

/**
 * Excel处理服务类
 * 负责Excel文件的生成、导出和格式化
 */
export class ExcelService {
  /**
   * 生成Excel工作簿
   * @param formData 表单数据
   * @param timeSlots 时间段数组
   * @returns Excel工作簿对象
   */
  static generateWorkbook(formData: FormData, timeSlots: TimeSlot[]): XLSX.WorkBook {
    try {
      // 每个时间段生成一条记录
      const rows = timeSlots.map(slot => [
        DEFAULT_VALUES.ACTIVITY_TYPE, // 活动类型：固定"多件多折"
        formData.productId, // 商品ID
        format(slot.startTime, TIME_FORMAT), // 开始时间
        format(slot.endTime, TIME_FORMAT), // 结束时间
        this.formatRegion(formData.region), // 活动区域
        formData.minQuantity, // 满几件
        formData.discount, // 折扣
        '' // 立减金额：空值
      ])

      // 创建工作表，包含完整的列标题
      const headers = Array.from(EXCEL_HEADERS)
      const worksheet = XLSX.utils.aoa_to_sheet([headers, ...rows])

      // 设置列宽
      worksheet['!cols'] = [...EXCEL_COLUMN_WIDTHS]

      // 设置行高（为多行标题预留空间）
      worksheet['!rows'] = [
        { hpt: 60 }, // 标题行高度
        ...rows.map(() => ({ hpt: 20 })) // 数据行高度
      ]

      // 创建工作簿
      const workbook = XLSX.utils.book_new()
      XLSX.utils.book_append_sheet(workbook, worksheet, EXCEL_CONFIG.SHEET_NAME)

      return workbook
    } catch (error) {
      console.error('Excel工作簿生成失败:', error)
      throw new Error('Excel工作簿生成失败，请检查数据格式')
    }
  }

  /**
   * 导出Excel文件到本地
   * @param workbook Excel工作簿
   * @param filename 文件名（不含扩展名）
   */
  static exportToFile(workbook: XLSX.WorkBook, filename: string): void {
    try {
      // 确保文件名以.xlsx结尾
      const xlsxFilename = filename.endsWith(EXCEL_CONFIG.FILE_EXTENSION) 
        ? filename 
        : `${filename}${EXCEL_CONFIG.FILE_EXTENSION}`

      // 强制XLSX格式导出
      XLSX.writeFile(workbook, xlsxFilename, {
        bookType: EXCEL_CONFIG.BOOK_TYPE,
        compression: EXCEL_CONFIG.COMPRESSION
      })
    } catch (error) {
      console.error('Excel文件导出失败:', error)
      throw new Error('Excel文件导出失败，请重试')
    }
  }

  /**
   * 生成Excel数据数组（用于预览）
   * @param formData 表单数据
   * @param timeSlots 时间段数组
   * @returns Excel行数据数组
   */
  static generateExcelData(formData: FormData, timeSlots: TimeSlot[]): ExcelRowData[] {
    try {
      return timeSlots.map(slot => ({
        活动类型: DEFAULT_VALUES.ACTIVITY_TYPE,
        商品ID: formData.productId,
        '开始时间\n示例：\n2020-03-27 00:00:00': format(slot.startTime, TIME_FORMAT),
        '结束时间\n示例：\n2020-03-27 23:59:59': format(slot.endTime, TIME_FORMAT),
        '活动区域\n支持填写"全国"及5大区\n5大区：华中,西南/西北,华南,华东,华北': this.formatRegion(formData.region),
        '满几件\n说明：表示满N件，件数只能在下拉框中选择': formData.minQuantity,
        '折扣-打几折\n说明：表示打几折，折扣只能下拉选择': formData.discount,
        '立减金额-多件总额下的扣减金额\n表示扣减的金额，单位元，支持最多2位小数，与折扣只能择一生效': ''
      }))
    } catch (error) {
      console.error('Excel数据生成失败:', error)
      throw new Error('Excel数据生成失败，请检查输入数据')
    }
  }

  /**
   * 格式化区域信息
   * @param region 区域类型
   * @returns 格式化后的区域字符串
   */
  private static formatRegion(region: RegionType): string {
    if (region.type === 'national') {
      return '全国'
    }
    // 多个大区用逗号分隔
    return region.regions.join(',')
  }

  /**
   * 生成导出文件名
   * @param productId 商品ID
   * @param timestamp 时间戳（可选）
   * @returns 文件名
   */
  static generateFileName(productId: string, timestamp?: Date): string {
    const time = timestamp || new Date()
    const timeStr = format(time, 'yyyyMMdd_HHmmss')
    return `多件多折_${productId}_${timeStr}`
  }

  /**
   * 验证Excel数据完整性
   * @param data Excel行数据数组
   * @returns 验证结果
   */
  static validateExcelData(data: ExcelRowData[]): {
    isValid: boolean
    errors: string[]
    warnings: string[]
  } {
    const errors: string[] = []
    const warnings: string[] = []

    if (!data || data.length === 0) {
      errors.push('没有可导出的数据')
      return { isValid: false, errors, warnings }
    }

    data.forEach((row, index) => {
      const rowNum = index + 1

      // 验证必填字段
      if (!row.活动类型) {
        errors.push(`第${rowNum}行：活动类型不能为空`)
      }
      if (!row.商品ID) {
        errors.push(`第${rowNum}行：商品ID不能为空`)
      }

      // 验证时间格式
      const startTimeKey = '开始时间\n示例：\n2020-03-27 00:00:00'
      const endTimeKey = '结束时间\n示例：\n2020-03-27 23:59:59'
      
      if (!row[startTimeKey]) {
        errors.push(`第${rowNum}行：开始时间不能为空`)
      } else if (!/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/.test(row[startTimeKey])) {
        errors.push(`第${rowNum}行：开始时间格式不正确`)
      }

      if (!row[endTimeKey]) {
        errors.push(`第${rowNum}行：结束时间不能为空`)
      } else if (!/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/.test(row[endTimeKey])) {
        errors.push(`第${rowNum}行：结束时间格式不正确`)
      }

      // 验证数值范围
      const quantityKey = '满几件\n说明：表示满N件，件数只能在下拉框中选择'
      const discountKey = '折扣-打几折\n说明：表示打几折，折扣只能下拉选择'
      
      if (row[quantityKey] < 1 || row[quantityKey] > 10) {
        errors.push(`第${rowNum}行：满件数量必须在1-10之间`)
      }

      if (row[discountKey] < 5.0 || row[discountKey] > 9.9) {
        errors.push(`第${rowNum}行：折扣必须在5.0-9.9之间`)
      }

      // 检查时间跨天情况
      try {
        const startTime = new Date(row[startTimeKey])
        const endTime = new Date(row[endTimeKey])
        
        if (startTime.getTime() > endTime.getTime()) {
          warnings.push(`第${rowNum}行：检测到跨天时间段`)
        }
      } catch (error) {
        // 时间解析错误已在上面处理
      }
    })

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    }
  }

  /**
   * 生成批量处理的Excel工作簿
   * @param allData 所有商品的Excel数据
   * @returns Excel工作簿对象
   */
  static generateBatchWorkbook(allData: ExcelRowData[]): XLSX.WorkBook {
    try {
      // 创建工作表，包含完整的列标题
      const headers = Array.from(EXCEL_HEADERS)
      const rows = allData.map(row => [
        row.活动类型,
        row.商品ID,
        row['开始时间\n示例：\n2020-03-27 00:00:00'],
        row['结束时间\n示例：\n2020-03-27 23:59:59'],
        row['活动区域\n支持填写"全国"及5大区\n5大区：华中,西南/西北,华南,华东,华北'],
        row['满几件\n说明：表示满N件，件数只能在下拉框中选择'],
        row['折扣-打几折\n说明：表示打几折，折扣只能下拉选择'],
        row['立减金额-多件总额下的扣减金额\n表示扣减的金额，单位元，支持最多2位小数，与折扣只能择一生效']
      ])

      const worksheet = XLSX.utils.aoa_to_sheet([headers, ...rows])

      // 设置列宽
      worksheet['!cols'] = [...EXCEL_COLUMN_WIDTHS]

      // 设置行高（为多行标题预留空间）
      worksheet['!rows'] = [
        { hpt: 60 }, // 标题行高度
        ...rows.map(() => ({ hpt: 20 })) // 数据行高度
      ]

      // 创建工作簿
      const workbook = XLSX.utils.book_new()
      XLSX.utils.book_append_sheet(workbook, worksheet, EXCEL_CONFIG.SHEET_NAME)

      return workbook
    } catch (error) {
      console.error('批量Excel工作簿生成失败:', error)
      throw new Error('批量Excel工作簿生成失败，请检查数据格式')
    }
  }

  /**
   * 保存工作簿到文件系统
   * @param workbook Excel工作簿
   * @param filePath 文件路径
   */
  static saveWorkbook(workbook: XLSX.WorkBook, filePath: string): void {
    try {
      XLSX.writeFile(workbook, filePath, {
        bookType: EXCEL_CONFIG.BOOK_TYPE,
        compression: EXCEL_CONFIG.COMPRESSION
      })
    } catch (error) {
      console.error('保存Excel文件失败:', error)
      throw new Error('保存Excel文件失败')
    }
  }

  /**
   * 从Excel文件读取商品ID列表
   * @param filePath 文件路径
   * @returns Promise<string[]>
   */
  static async readProductIdsFromFile(filePath: string): Promise<string[]> {
    try {
      const workbook = XLSX.readFile(filePath)
      const sheetName = workbook.SheetNames[0]
      const worksheet = workbook.Sheets[sheetName]
      
      // 转换为JSON数据
      const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 })
      
      // 假设第一列是商品ID
      const productIds: string[] = []
      for (let i = 1; i < jsonData.length; i++) { // 跳过标题行
        const row = jsonData[i] as any[]
        if (row[0]) {
          productIds.push(String(row[0]).trim())
        }
      }
      
      // 去重
      return [...new Set(productIds)].filter(id => id.length > 0)
    } catch (error) {
      console.error('读取商品ID失败:', error)
      throw new Error('读取商品ID失败，请检查文件格式')
    }
  }

  /**
   * 从上传的文件中读取商品ID列表
   * @param file 上传的文件
   * @returns Promise<string[]>
   */
  static async readProductIdsFromUploadedFile(file: File): Promise<string[]> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      
      reader.onload = (e) => {
        try {
          const data = new Uint8Array(e.target?.result as ArrayBuffer)
          const workbook = XLSX.read(data, { type: 'array' })
          
          // 读取第一个工作表
          const sheetName = workbook.SheetNames[0]
          const worksheet = workbook.Sheets[sheetName]
          
          // 转换为JSON数据
          const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 })
          
          // 提取商品ID（假设在第一列）
          const productIds: string[] = []
          for (let i = 1; i < jsonData.length; i++) { // 跳过标题行
            const row = jsonData[i] as any[]
            if (row[0]) {
              productIds.push(String(row[0]).trim())
            }
          }
          
          // 去重并过滤空值
          const uniqueIds = [...new Set(productIds)].filter(id => id.length > 0)
          resolve(uniqueIds)
        } catch (error) {
          reject(new Error('读取商品ID失败，请检查文件格式'))
        }
      }
      
      reader.onerror = () => {
        reject(new Error('文件读取失败'))
      }
      
      reader.readAsArrayBuffer(file)
    })
  }

  /**
   * 验证批量上传文件格式
   * @param file 上传的文件
   * @returns Promise<{isValid: boolean, errors: string[], productCount: number}>
   */
  static async validateBatchUploadFile(file: File): Promise<{
    isValid: boolean
    errors: string[]
    productCount: number
  }> {
    const errors: string[] = []
    let productCount = 0

    try {
      // 检查文件类型
      if (!file.name.match(/\.(xlsx|xls|csv)$/i)) {
        errors.push('文件格式不支持，请上传Excel或CSV文件')
        return { isValid: false, errors, productCount }
      }

      // 检查文件大小（限制10MB）
      if (file.size > 10 * 1024 * 1024) {
        errors.push('文件大小超过10MB限制')
        return { isValid: false, errors, productCount }
      }

      // 读取商品ID
      const productIds = await this.readProductIdsFromUploadedFile(file)
      productCount = productIds.length

      if (productCount === 0) {
        errors.push('文件中没有找到有效的商品ID')
      } else if (productCount > 1000) {
        errors.push('商品数量超过1000个限制')
      }

      // 验证商品ID格式
      const invalidIds = productIds.filter(id => !/^[A-Za-z0-9_-]+$/.test(id))
      if (invalidIds.length > 0) {
        errors.push(`发现${invalidIds.length}个格式不正确的商品ID`)
      }

    } catch (error) {
      errors.push('文件解析失败，请检查文件格式')
    }

    return {
      isValid: errors.length === 0,
      errors,
      productCount
    }
  }

  /**
   * 从Excel文件读取数据（用于模板验证）
   * @param file 文件对象
   * @returns Promise<ExcelRowData[]>
   */
  static async readExcelFile(file: File): Promise<ExcelRowData[]> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      
      reader.onload = (e) => {
        try {
          const data = new Uint8Array(e.target?.result as ArrayBuffer)
          const workbook = XLSX.read(data, { type: 'array' })
          
          // 读取第一个工作表
          const sheetName = workbook.SheetNames[0]
          const worksheet = workbook.Sheets[sheetName]
          
          // 转换为JSON数据
          const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 })
          
          // 解析为ExcelRowData格式
          const headers = jsonData[0] as string[]
          const rows = jsonData.slice(1) as any[][]
          
          const excelData: ExcelRowData[] = rows.map(row => {
            const rowData: any = {}
            headers.forEach((header, index) => {
              rowData[header] = row[index] || ''
            })
            return rowData as ExcelRowData
          })
          
          resolve(excelData)
        } catch (error) {
          reject(new Error('Excel文件读取失败，请检查文件格式'))
        }
      }
      
      reader.onerror = () => {
        reject(new Error('文件读取失败'))
      }
      
      reader.readAsArrayBuffer(file)
    })
  }
}