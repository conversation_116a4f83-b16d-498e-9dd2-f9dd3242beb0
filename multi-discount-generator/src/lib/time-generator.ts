import { 
  addDays, 
  addHours, 
  addMinutes, 
  format, 
  getDay, 
  isSameDay,
  startOfDay,
  endOfDay,
  setHours,
  setMinutes,
  setSeconds,
  isAfter,
  isBefore
} from 'date-fns'
import { toZonedTime, fromZonedTime } from 'date-fns-tz'
import type { 
  TimeSlot, 
  ContinuousConfig, 
  RandomConfig
} from '@/types'
import type { 
  HolidayInfo, 
  TimeSlotGenerationResult,
  WorkdayType 
} from '@/types/holiday'
import { 
  TIME_CONSTANTS, 
  TIME_FORMAT,
  CHINA_TIMEZONE 
} from '@/constants'
import { 
  CHINA_TIME_CONFIG,
  DEFAULT_HOLIDAYS_2025 
} from '@/constants/holiday'
import { isChineseHoliday, getHolidayInfo } from './holiday'

/**
 * 时间生成器类
 * 负责生成连续时间段和随机时间段
 */
export class TimeGenerator {
  /**
   * 生成连续时间段（优化版）
   * @param config 连续时间配置
   * @returns 时间段生成结果
   */
  static generateContinuousTimeSlots(config: ContinuousConfig): TimeSlotGenerationResult {
    const slots: TimeSlot[] = []
    const warnings: string[] = []
    const errors: string[] = []

    try {
      const { startDate, startTime, endTime, days, weekendFullDay } = config

      for (let i = 0; i < days; i++) {
        const currentDate = addDays(startDate, i)
        const dayOfWeek = getDay(currentDate) // 0=周日, 6=周六
        
        // 检查是否为节假日
        let isHoliday = false
        let holidayInfo: HolidayInfo | null = null
        
        try {
          isHoliday = isChineseHoliday(currentDate)
          if (isHoliday) {
            holidayInfo = getHolidayInfo(currentDate)
          }
        } catch (error) {
          warnings.push(`第${i + 1}天节假日检查失败，使用周末判断`)
        }

        const isWeekendOrHoliday = dayOfWeek === TIME_CONSTANTS.SUNDAY || 
                                   dayOfWeek === TIME_CONSTANTS.SATURDAY || 
                                   isHoliday

        if (isWeekendOrHoliday && weekendFullDay) {
          // 周末/节假日特殊处理
          const timeSlot = this.generateWeekendOrHolidaySlot(
            currentDate,
            dayOfWeek,
            startTime,
            endTime,
            slots,
            holidayInfo,
            isHoliday
          )
          
          if (timeSlot) {
            slots.push(timeSlot)
          }
        } else {
          // 工作日时间段 - 需要检查下一天是否为节假日/周末
          const nextDate = addDays(currentDate, 1)
          let isNextDayHoliday = false
          try {
            isNextDayHoliday = isChineseHoliday(nextDate)
          } catch (error) {
            warnings.push(`第${i + 1}天下一天节假日检查失败，默认为工作日`)
          }
          const nextDayOfWeek = getDay(nextDate)
          const isNextDayWeekend = nextDayOfWeek === TIME_CONSTANTS.SATURDAY || nextDayOfWeek === TIME_CONSTANTS.SUNDAY

          const workdaySlot = this.generateWorkdaySlot(
            currentDate,
            startTime,
            endTime,
            isNextDayHoliday || isNextDayWeekend,
            weekendFullDay
          )
          slots.push(workdaySlot)
        }
      }

      return {
        slots: slots.map(slot => ({
          ...slot,
          type: this.determineSlotType(slot.startTime),
          isHoliday: isChineseHoliday(slot.startTime),
          holidayName: getHolidayInfo(slot.startTime)?.name
        })),
        warnings,
        errors
      }
    } catch (error) {
      errors.push(`时间生成失败: ${error instanceof Error ? error.message : '未知错误'}`)
      return { slots: [], warnings, errors }
    }
  }

  /**
   * 生成周末或节假日时间段
   */
  private static generateWeekendOrHolidaySlot(
    currentDate: Date,
    dayOfWeek: number,
    startTime: Date,
    endTime: Date,
    existingSlots: TimeSlot[],
    holidayInfo: HolidayInfo | null,
    isHoliday: boolean
  ): TimeSlot | null {
    if (isHoliday) {
      // 法定节假日处理
      const prevSlot = existingSlots[existingSlots.length - 1]

      // 如果前一天的时间段延续到今天，需要从前一天结束时间开始
      if (prevSlot && isSameDay(prevSlot.endTime, currentDate)) {
        return {
          startTime: prevSlot.endTime,
          endTime: endOfDay(currentDate)
        }
      } else {
        // 节假日全天
        return {
          startTime: startOfDay(currentDate),
          endTime: endOfDay(currentDate)
        }
      }
    } else if (dayOfWeek === TIME_CONSTANTS.FRIDAY) {
      // 周五按正常工作日时间
      return this.generateWorkdaySlot(currentDate, startTime, endTime, false, false)
    } else if (dayOfWeek === TIME_CONSTANTS.SATURDAY) {
      // 周六：从前一天结束时间延续到23:59:59
      const prevSlot = existingSlots[existingSlots.length - 1]
      if (prevSlot && isSameDay(prevSlot.endTime, currentDate)) {
        return {
          startTime: prevSlot.endTime,
          endTime: endOfDay(currentDate)
        }
      } else {
        return {
          startTime: startOfDay(currentDate),
          endTime: endOfDay(currentDate)
        }
      }
    } else if (dayOfWeek === TIME_CONSTANTS.SUNDAY) {
      // 周日：00:00:00到周一结束时间
      const mondayEnd = this.setTimeToDate(addDays(currentDate, 1), endTime)
      return {
        startTime: startOfDay(currentDate),
        endTime: mondayEnd
      }
    } else {
      // 其他情况（不应该到达这里）
      return {
        startTime: startOfDay(currentDate),
        endTime: endOfDay(currentDate)
      }
    }
  }

  /**
   * 生成工作日时间段
   */
  private static generateWorkdaySlot(
    date: Date,
    startTime: Date,
    endTime: Date,
    isNextDaySpecial: boolean = false,
    weekendFullDay: boolean = false
  ): TimeSlot {
    const dayStart = this.setTimeToDate(date, startTime)

    // 检查是否跨天
    const isNextDay = this.isTimeAfter(endTime, startTime)

    if (isNextDay && isNextDaySpecial && weekendFullDay) {
      // 如果跨天且下一天是节假日/周末，则截断到当天23:59:59
      const dayEnd = endOfDay(date)
      return { startTime: dayStart, endTime: dayEnd }
    } else {
      // 正常处理（包括跨天）
      const dayEnd = isNextDay
        ? this.setTimeToDate(addDays(date, 1), endTime) // 跨天处理
        : this.setTimeToDate(date, endTime)

      return { startTime: dayStart, endTime: dayEnd }
    }
  }

  /**
   * 生成随机时间段
   * @param config 随机时间配置
   * @returns 时间段生成结果
   */
  static generateRandomTimeSlots(config: RandomConfig): TimeSlotGenerationResult {
    const slots: TimeSlot[] = []
    const warnings: string[] = []
    const errors: string[] = []

    try {
      const { startTime, slotCount, slotDuration } = config
      let currentEnd = startTime

      for (let i = 0; i < slotCount; i++) {
        // 随机生成下一个开始时间（在前一个结束时间之后）
        const randomGap = Math.floor(
          Math.random() * 
          (TIME_CONSTANTS.MAX_RANDOM_GAP_MINUTES - TIME_CONSTANTS.MIN_RANDOM_GAP_MINUTES + 1)
        ) + TIME_CONSTANTS.MIN_RANDOM_GAP_MINUTES

        const slotStart = addMinutes(currentEnd, randomGap)
        const slotEnd = addHours(slotStart, slotDuration)

        slots.push({
          startTime: slotStart,
          endTime: slotEnd
        })

        currentEnd = slotEnd

        // 检查时间段是否合理
        if (i > 0) {
          const prevSlot = slots[i - 1]
          if (isAfter(prevSlot.endTime, slotStart)) {
            warnings.push(`第${i + 1}个时间段与前一个时间段重叠`)
          }
        }
      }

      return {
        slots: slots.map(slot => ({
          ...slot,
          type: this.determineSlotType(slot.startTime),
          isHoliday: isChineseHoliday(slot.startTime),
          holidayName: getHolidayInfo(slot.startTime)?.name
        })),
        warnings,
        errors
      }
    } catch (error) {
      errors.push(`随机时间生成失败: ${error instanceof Error ? error.message : '未知错误'}`)
      return { slots: [], warnings, errors }
    }
  }

  /**
   * 将时间设置到指定日期
   */
  private static setTimeToDate(date: Date, timeSource: Date): Date {
    return setSeconds(
      setMinutes(
        setHours(date, timeSource.getHours()),
        timeSource.getMinutes()
      ),
      timeSource.getSeconds()
    )
  }

  /**
   * 判断时间是否晚于另一个时间（仅比较时分秒）
   */
  private static isTimeAfter(time1: Date, time2: Date): boolean {
    const t1 = time1.getHours() * 3600 + time1.getMinutes() * 60 + time1.getSeconds()
    const t2 = time2.getHours() * 3600 + time2.getMinutes() * 60 + time2.getSeconds()
    return t1 > t2
  }

  /**
   * 确定时间段类型
   */
  private static determineSlotType(date: Date): WorkdayType {
    const dayOfWeek = getDay(date)
    
    if (isChineseHoliday(date)) {
      return 'holiday'
    } else if (dayOfWeek === TIME_CONSTANTS.SUNDAY || dayOfWeek === TIME_CONSTANTS.SATURDAY) {
      return 'weekend'
    } else {
      return 'workday'
    }
  }

  /**
   * 验证时间配置
   */
  static validateTimeConfig(config: ContinuousConfig | RandomConfig): {
    isValid: boolean
    errors: string[]
  } {
    const errors: string[] = []

    if ('days' in config) {
      // 连续模式验证
      if (config.days < 1 || config.days > 365) {
        errors.push('天数必须在1-365之间')
      }
      
      if (!config.startTime || !config.endTime) {
        errors.push('开始时间和结束时间不能为空')
      }
    } else {
      // 随机模式验证
      if (config.slotCount < 1 || config.slotCount > 50) {
        errors.push('时间段数量必须在1-50之间')
      }
      
      if (config.slotDuration !== 1 && config.slotDuration !== 2) {
        errors.push('时间段长度必须为1小时或2小时')
      }
      
      if (!config.startTime) {
        errors.push('起始时间不能为空')
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }

  /**
   * 格式化时间段为字符串（用于显示）
   */
  static formatTimeSlot(slot: TimeSlot): string {
    const start = format(slot.startTime, TIME_FORMAT)
    const end = format(slot.endTime, TIME_FORMAT)
    return `${start} - ${end}`
  }

  /**
   * 检查时间段是否跨天
   */
  static isCrossDaySlot(slot: TimeSlot): boolean {
    return !isSameDay(slot.startTime, slot.endTime)
  }

  /**
   * 获取时间段持续时间（小时）
   */
  static getSlotDurationHours(slot: TimeSlot): number {
    const diffMs = slot.endTime.getTime() - slot.startTime.getTime()
    return diffMs / (1000 * 60 * 60)
  }

  /**
   * 转换为中国时区
   */
  static toChineseTime(date: Date): Date {
    return toZonedTime(date, CHINA_TIMEZONE)
  }

  /**
   * 从中国时区转换为UTC
   */
  static fromChineseTime(date: Date): Date {
    return fromZonedTime(date, CHINA_TIMEZONE)
  }
}