/**
 * 模板验证服务
 * 验证模板格式、数据完整性和合法性
 */
export class TemplateValidator {
  
  /**
   * 验证结果接口
   */
  interface ValidationResult {
    isValid: boolean
    errors: string[]
    warnings: string[]
    summary: {
      totalRows: number
      validRows: number
      errorRows: number
      warningRows: number
    }
  }

  /**
   * 验证规则配置
   */
  static readonly VALIDATION_RULES = {
    PRODUCT_ID: {
      required: true,
      pattern: /^[A-Za-z0-9_-]+$/,
      maxLength: 50,
      message: '商品ID只能包含字母、数字、下划线和横线，长度不超过50个字符'
    },
    ACTIVITY_TYPE: {
      required: false,
      allowedValues: ['多件多折', '满减', '打折', '直降', '立减'],
      defaultValue: '多件多折'
    },
    TIME_FORMAT: {
      pattern: /^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/,
      message: '时间格式必须为 YYYY-MM-DD HH:mm:ss'
    },
    DATE_FORMAT: {
      pattern: /^\d{4}-\d{2}-\d{2}$/,
      message: '日期格式必须为 YYYY-MM-DD'
    },
    REGION: {
      allowedValues: ['全国', '华中', '西南', '西北', '华南', '华东', '华北'],
      defaultValue: '全国'
    },
    MIN_QUANTITY: {
      min: 1,
      max: 99,
      defaultValue: 1
    },
    DISCOUNT: {
      min: 0.1,
      max: 9.9,
      defaultValue: 8.0
    }
  }

  /**
   * 验证模板数据
   */
  static validateTemplateData(
    data: any[], 
    columns: string[], 
    format: string = 'standard'
  ): ValidationResult {
    const result: ValidationResult = {
      isValid: true,
      errors: [],
      warnings: [],
      summary: {
        totalRows: data.length,
        validRows: 0,
        errorRows: 0,
        warningRows: 0
      }
    }

    // 验证列结构
    const columnValidation = this.validateColumns(columns, format)
    result.errors.push(...columnValidation.errors)
    result.warnings.push(...columnValidation.warnings)

    // 验证数据行
    data.forEach((row, index) => {
      const rowValidation = this.validateRow(row, columns, format, index + 1)
      
      if (rowValidation.errors.length > 0) {
        result.errors.push(...rowValidation.errors)
        result.summary.errorRows++
      } else {
        result.summary.validRows++
      }

      if (rowValidation.warnings.length > 0) {
        result.warnings.push(...rowValidation.warnings)
        result.summary.warningRows++
      }
    })

    // 验证数据一致性
    const consistencyValidation = this.validateDataConsistency(data, columns)
    result.errors.push(...consistencyValidation.errors)
    result.warnings.push(...consistencyValidation.warnings)

    result.isValid = result.errors.length === 0

    return result
  }

  /**
   * 验证列结构
   */
  private static validateColumns(columns: string[], format: string): {
    errors: string[]
    warnings: string[]
  } {
    const errors: string[] = []
    const warnings: string[] = []

    // 必需列检查
    const requiredColumns = ['商品ID']
    
    if (format === 'standard') {
      requiredColumns.push('开始时间', '结束时间')
    } else if (format === 'batch') {
      requiredColumns.push('开始日期', '结束日期')
    }

    for (const column of requiredColumns) {
      if (!columns.includes(column)) {
        errors.push(`缺少必需列: ${column}`)
      }
    }

    // 推荐列检查
    const recommendedColumns = ['活动类型', '活动区域', '满几件', '折扣']
    for (const column of recommendedColumns) {
      if (!columns.includes(column)) {
        warnings.push(`建议添加列: ${column}`)
      }
    }

    return { errors, warnings }
  }

  /**
   * 验证单行数据
   */
  private static validateRow(
    row: any, 
    columns: string[], 
    format: string, 
    rowNumber: number
  ): {
    errors: string[]
    warnings: string[]
  } {
    const errors: string[] = []
    const warnings: string[] = []

    // 验证商品ID
    const productId = row['商品ID']
    if (!productId || typeof productId !== 'string') {
      errors.push(`第${rowNumber}行: 商品ID不能为空`)
    } else if (!this.VALIDATION_RULES.PRODUCT_ID.pattern.test(productId)) {
      errors.push(`第${rowNumber}行: ${this.VALIDATION_RULES.PRODUCT_ID.message}`)
    } else if (productId.length > this.VALIDATION_RULES.PRODUCT_ID.maxLength) {
      errors.push(`第${rowNumber}行: 商品ID长度不能超过${this.VALIDATION_RULES.PRODUCT_ID.maxLength}个字符`)
    }

    // 验证活动类型
    const activityType = row['活动类型']
    if (activityType && !this.VALIDATION_RULES.ACTIVITY_TYPE.allowedValues.includes(activityType)) {
      warnings.push(`第${rowNumber}行: 活动类型 "${activityType}" 不在推荐值中，将使用默认值`)
    }

    // 验证时间格式
    if (format === 'standard') {
      const startTime = row['开始时间']
      const endTime = row['结束时间']
      
      if (startTime && !this.VALIDATION_RULES.TIME_FORMAT.pattern.test(startTime)) {
        errors.push(`第${rowNumber}行: 开始时间格式错误，${this.VALIDATION_RULES.TIME_FORMAT.message}`)
      }
      
      if (endTime && !this.VALIDATION_RULES.TIME_FORMAT.pattern.test(endTime)) {
        errors.push(`第${rowNumber}行: 结束时间格式错误，${this.VALIDATION_RULES.TIME_FORMAT.message}`)
      }

      // 验证时间逻辑
      if (startTime && endTime) {
        const start = new Date(startTime)
        const end = new Date(endTime)
        if (start >= end) {
          errors.push(`第${rowNumber}行: 开始时间必须早于结束时间`)
        }
      }
    } else if (format === 'batch') {
      const startDate = row['开始日期']
      const endDate = row['结束日期']
      
      if (startDate && !this.VALIDATION_RULES.DATE_FORMAT.pattern.test(startDate)) {
        errors.push(`第${rowNumber}行: 开始日期格式错误，${this.VALIDATION_RULES.DATE_FORMAT.message}`)
      }
      
      if (endDate && !this.VALIDATION_RULES.DATE_FORMAT.pattern.test(endDate)) {
        errors.push(`第${rowNumber}行: 结束日期格式错误，${this.VALIDATION_RULES.DATE_FORMAT.message}`)
      }

      // 验证日期逻辑
      if (startDate && endDate) {
        const start = new Date(startDate)
        const end = new Date(endDate)
        if (start > end) {
          errors.push(`第${rowNumber}行: 开始日期必须早于或等于结束日期`)
        }
      }
    }

    // 验证区域
    const region = row['活动区域']
    if (region && !this.VALIDATION_RULES.REGION.allowedValues.includes(region)) {
      warnings.push(`第${rowNumber}行: 活动区域 "${region}" 不在标准值中`)
    }

    // 验证满几件
    const minQuantity = row['满几件']
    if (minQuantity) {
      const qty = parseInt(minQuantity)
      if (isNaN(qty) || qty < this.VALIDATION_RULES.MIN_QUANTITY.min || qty > this.VALIDATION_RULES.MIN_QUANTITY.max) {
        errors.push(`第${rowNumber}行: 满几件必须是${this.VALIDATION_RULES.MIN_QUANTITY.min}-${this.VALIDATION_RULES.MIN_QUANTITY.max}之间的整数`)
      }
    }

    // 验证折扣
    const discount = row['折扣']
    if (discount) {
      const disc = parseFloat(discount)
      if (isNaN(disc) || disc < this.VALIDATION_RULES.DISCOUNT.min || disc > this.VALIDATION_RULES.DISCOUNT.max) {
        errors.push(`第${rowNumber}行: 折扣必须是${this.VALIDATION_RULES.DISCOUNT.min}-${this.VALIDATION_RULES.DISCOUNT.max}之间的数值`)
      }
    }

    return { errors, warnings }
  }

  /**
   * 验证数据一致性
   */
  private static validateDataConsistency(data: any[], columns: string[]): {
    errors: string[]
    warnings: string[]
  } {
    const errors: string[] = []
    const warnings: string[] = []

    // 检查商品ID重复
    const productIds = data.map(row => row['商品ID']).filter(Boolean)
    const duplicateIds = productIds.filter((id, index) => productIds.indexOf(id) !== index)
    const uniqueDuplicates = [...new Set(duplicateIds)]
    
    if (uniqueDuplicates.length > 0) {
      warnings.push(`发现重复的商品ID: ${uniqueDuplicates.join(', ')}`)
    }

    // 检查数据完整性
    const emptyRows = data.filter(row => 
      Object.values(row).every(value => !value || value.toString().trim() === '')
    ).length

    if (emptyRows > 0) {
      warnings.push(`发现${emptyRows}行空数据`)
    }

    // 检查时间范围合理性
    const timeColumns = ['开始时间', '结束时间', '开始日期', '结束日期']
    const hasTimeData = timeColumns.some(col => columns.includes(col))
    
    if (hasTimeData) {
      const currentYear = new Date().getFullYear()
      const futureLimit = currentYear + 2
      const pastLimit = currentYear - 1

      data.forEach((row, index) => {
        timeColumns.forEach(col => {
          const timeValue = row[col]
          if (timeValue) {
            const date = new Date(timeValue)
            const year = date.getFullYear()
            
            if (year < pastLimit || year > futureLimit) {
              warnings.push(`第${index + 1}行: ${col} 的年份(${year})可能不合理`)
            }
          }
        })
      })
    }

    return { errors, warnings }
  }

  /**
   * 快速验证文件格式
   */
  static quickValidateFile(file: File): {
    isValid: boolean
    errors: string[]
  } {
    const errors: string[] = []

    // 检查文件大小
    if (file.size > 10 * 1024 * 1024) { // 10MB
      errors.push('文件大小不能超过10MB')
    }

    // 检查文件类型
    const allowedTypes = [
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-excel',
      'text/csv',
      'application/csv'
    ]

    if (!allowedTypes.includes(file.type)) {
      errors.push('不支持的文件类型，请上传Excel或CSV文件')
    }

    // 检查文件名
    if (!file.name || file.name.trim() === '') {
      errors.push('文件名不能为空')
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }
}
