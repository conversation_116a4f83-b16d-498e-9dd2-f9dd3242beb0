import { prisma } from '@/lib/prisma'
import { performanceMonitor } from '@/lib/performance/PerformanceMonitor'
import { errorHandlingService } from '@/lib/monitoring/ErrorHandlingService'

export interface ErrorContext {
  userId?: string
  taskId?: string
  requestId?: string
  userAgent?: string
  ip?: string
  url?: string
  method?: string
  timestamp: Date
  stackTrace?: string
  additionalData?: Record<string, any>
}

export interface ErrorRecoveryAction {
  type: 'retry' | 'fallback' | 'skip' | 'abort'
  maxAttempts?: number
  delay?: number
  fallbackValue?: any
  condition?: (error: Error, context: ErrorContext) => boolean
}

/**
 * 全局错误处理器
 * 统一处理应用中的所有错误，提供错误恢复机制
 */
export class GlobalErrorHandler {
  private static instance: GlobalErrorHandler
  private errorRecoveryStrategies = new Map<string, ErrorRecoveryAction>()
  private circuitBreakers = new Map<string, {
    failures: number
    lastFailure: Date
    isOpen: boolean
    threshold: number
    timeout: number
  }>()

  private constructor() {
    this.initializeDefaultStrategies()
    // 在Edge Runtime中跳过全局处理器设置
    if (typeof process !== 'undefined' && process.on) {
      this.setupGlobalHandlers()
    }
  }

  static getInstance(): GlobalErrorHandler {
    if (!GlobalErrorHandler.instance) {
      GlobalErrorHandler.instance = new GlobalErrorHandler()
    }
    return GlobalErrorHandler.instance
  }

  /**
   * 处理错误
   */
  async handleError(
    error: Error,
    context: ErrorContext,
    recoveryKey?: string
  ): Promise<{
    handled: boolean
    recovered: boolean
    action?: string
    result?: any
  }> {
    try {
      // 记录错误
      await this.logError(error, context)

      // 更新错误统计
      this.updateErrorMetrics(error, context)

      // 检查熔断器
      if (recoveryKey && this.isCircuitOpen(recoveryKey)) {
        return {
          handled: true,
          recovered: false,
          action: 'circuit_breaker_open'
        }
      }

      // 尝试错误恢复
      const recoveryResult = await this.attemptRecovery(error, context, recoveryKey)

      // 更新熔断器状态
      if (recoveryKey) {
        this.updateCircuitBreaker(recoveryKey, recoveryResult.recovered)
      }

      return recoveryResult

    } catch (handlingError) {
      console.error('错误处理器本身发生错误:', handlingError)
      
      // 记录处理器错误
      await this.logError(handlingError, {
        ...context,
        additionalData: {
          ...context.additionalData,
          originalError: error.message,
          handlerError: true
        }
      })

      return {
        handled: false,
        recovered: false,
        action: 'handler_failed'
      }
    }
  }

  /**
   * 注册错误恢复策略
   */
  registerRecoveryStrategy(key: string, action: ErrorRecoveryAction): void {
    this.errorRecoveryStrategies.set(key, action)
  }

  /**
   * 配置熔断器
   */
  configureCircuitBreaker(
    key: string,
    threshold: number = 5,
    timeout: number = 60000
  ): void {
    this.circuitBreakers.set(key, {
      failures: 0,
      lastFailure: new Date(0),
      isOpen: false,
      threshold,
      timeout
    })
  }

  /**
   * 创建错误上下文
   */
  createErrorContext(
    req?: any,
    additionalData?: Record<string, any>
  ): ErrorContext {
    return {
      userId: req?.user?.id,
      requestId: req?.id || this.generateRequestId(),
      userAgent: req?.headers?.['user-agent'],
      ip: req?.ip || req?.connection?.remoteAddress,
      url: req?.url,
      method: req?.method,
      timestamp: new Date(),
      additionalData
    }
  }

  /**
   * 包装异步函数以提供错误处理
   */
  wrapAsync<T extends any[], R>(
    fn: (...args: T) => Promise<R>,
    recoveryKey?: string,
    contextFactory?: (...args: T) => ErrorContext
  ): (...args: T) => Promise<R> {
    return async (...args: T): Promise<R> => {
      try {
        return await fn(...args)
      } catch (error) {
        const context = contextFactory 
          ? contextFactory(...args)
          : this.createErrorContext(undefined, { functionName: fn.name })

        const result = await this.handleError(error, context, recoveryKey)
        
        if (result.recovered && result.result !== undefined) {
          return result.result
        }
        
        throw error
      }
    }
  }

  /**
   * 包装同步函数以提供错误处理
   */
  wrapSync<T extends any[], R>(
    fn: (...args: T) => R,
    recoveryKey?: string,
    contextFactory?: (...args: T) => ErrorContext
  ): (...args: T) => R {
    return (...args: T): R => {
      try {
        return fn(...args)
      } catch (error) {
        const context = contextFactory 
          ? contextFactory(...args)
          : this.createErrorContext(undefined, { functionName: fn.name })

        // 对于同步函数，我们只能记录错误，不能进行异步恢复
        this.handleError(error, context, recoveryKey).catch(console.error)
        
        throw error
      }
    }
  }

  /**
   * 获取错误统计
   */
  async getErrorStats(timeWindow: number = 3600000): Promise<{
    totalErrors: number
    errorsByType: Record<string, number>
    errorsBySource: Record<string, number>
    recoveryStats: {
      attempted: number
      successful: number
      failed: number
      successRate: number
    }
    circuitBreakerStats: Array<{
      key: string
      isOpen: boolean
      failures: number
      lastFailure: Date
    }>
  }> {
    const startTime = new Date(Date.now() - timeWindow)
    
    // 从数据库获取错误统计
    const errorLogs = await prisma.taskLog.findMany({
      where: {
        level: 'ERROR',
        timestamp: { gte: startTime }
      }
    })

    const totalErrors = errorLogs.length
    
    // 按错误类型分组
    const errorsByType: Record<string, number> = {}
    const errorsBySource: Record<string, number> = {}
    
    errorLogs.forEach(log => {
      const metadata = log.metadata as any
      const errorType = metadata?.errorType || 'Unknown'
      const source = metadata?.source || 'Unknown'
      
      errorsByType[errorType] = (errorsByType[errorType] || 0) + 1
      errorsBySource[source] = (errorsBySource[source] || 0) + 1
    })

    // 恢复统计（从性能监控中获取）
    const recoveryMetrics = performanceMonitor.getMetrics(timeWindow)
      .filter(m => m.name.startsWith('error_recovery_'))
    
    const attempted = recoveryMetrics
      .filter(m => m.name === 'error_recovery_attempted')
      .reduce((sum, m) => sum + m.value, 0)
    
    const successful = recoveryMetrics
      .filter(m => m.name === 'error_recovery_successful')
      .reduce((sum, m) => sum + m.value, 0)
    
    const failed = attempted - successful
    const successRate = attempted > 0 ? (successful / attempted) * 100 : 0

    // 熔断器统计
    const circuitBreakerStats = Array.from(this.circuitBreakers.entries())
      .map(([key, breaker]) => ({
        key,
        isOpen: breaker.isOpen,
        failures: breaker.failures,
        lastFailure: breaker.lastFailure
      }))

    return {
      totalErrors,
      errorsByType,
      errorsBySource,
      recoveryStats: {
        attempted,
        successful,
        failed,
        successRate
      },
      circuitBreakerStats
    }
  }

  // 私有方法

  private initializeDefaultStrategies(): void {
    // 数据库连接错误恢复
    this.registerRecoveryStrategy('database_connection', {
      type: 'retry',
      maxAttempts: 3,
      delay: 1000,
      condition: (error) => error.message.includes('connection')
    })

    // 文件操作错误恢复
    this.registerRecoveryStrategy('file_operation', {
      type: 'retry',
      maxAttempts: 2,
      delay: 500,
      condition: (error) => error.message.includes('ENOENT') || error.message.includes('EACCES')
    })

    // 网络请求错误恢复
    this.registerRecoveryStrategy('network_request', {
      type: 'retry',
      maxAttempts: 3,
      delay: 2000,
      condition: (error) => error.message.includes('timeout') || error.message.includes('ECONNRESET')
    })

    // 任务执行错误恢复
    this.registerRecoveryStrategy('task_execution', {
      type: 'retry',
      maxAttempts: 2,
      delay: 5000,
      condition: (error, context) => {
        // 只对特定类型的任务错误进行重试
        return !error.message.includes('validation') && 
               !error.message.includes('permission')
      }
    })
  }

  private setupGlobalHandlers(): void {
    // 处理未捕获的异常
    if (typeof process !== 'undefined' && process.on) {
      process.on('uncaughtException', async (error) => {
        console.error('未捕获的异常:', error)
        
        await this.handleError(error, {
          timestamp: new Date(),
          additionalData: { type: 'uncaughtException' }
        })
        
        // 给系统一些时间来记录错误
        setTimeout(() => {
          process.exit(1)
        }, 1000)
      })

      process.on('unhandledRejection', async (reason, promise) => {
        console.error('未处理的Promise拒绝:', reason)
        
        const error = reason instanceof Error ? reason : new Error(String(reason))
        
        await this.handleError(error, {
          timestamp: new Date(),
          additionalData: { 
            type: 'unhandledRejection',
            promise: promise.toString()
          }
        })
      })
    }
  }

  private async logError(error: Error, context: ErrorContext): Promise<void> {
    try {
      // 记录到数据库
      await prisma.taskLog.create({
        data: {
          taskId: context.taskId,
          level: 'ERROR',
          message: error.message,
          timestamp: context.timestamp,
          metadata: {
            errorType: error.constructor.name,
            stackTrace: error.stack,
            source: 'global_error_handler',
            context: {
              userId: context.userId,
              requestId: context.requestId,
              userAgent: context.userAgent,
              ip: context.ip,
              url: context.url,
              method: context.method,
              ...context.additionalData
            }
          }
        }
      })

      // 发送到错误处理服务
      await errorHandlingService.reportError(error, {
        source: 'global_handler',
        context: context.additionalData,
        severity: this.determineErrorSeverity(error),
        timestamp: context.timestamp
      })

    } catch (loggingError) {
      console.error('记录错误失败:', loggingError)
    }
  }

  private updateErrorMetrics(error: Error, context: ErrorContext): void {
    performanceMonitor.recordMetric('error_total', 1, 'count', {
      errorType: error.constructor.name,
      source: context.additionalData?.source || 'unknown'
    })
  }

  private async attemptRecovery(
    error: Error,
    context: ErrorContext,
    recoveryKey?: string
  ): Promise<{
    handled: boolean
    recovered: boolean
    action?: string
    result?: any
  }> {
    if (!recoveryKey) {
      return { handled: true, recovered: false, action: 'no_recovery_strategy' }
    }

    const strategy = this.errorRecoveryStrategies.get(recoveryKey)
    if (!strategy) {
      return { handled: true, recovered: false, action: 'strategy_not_found' }
    }

    // 检查恢复条件
    if (strategy.condition && !strategy.condition(error, context)) {
      return { handled: true, recovered: false, action: 'condition_not_met' }
    }

    performanceMonitor.recordMetric('error_recovery_attempted', 1, 'count', {
      strategy: recoveryKey,
      errorType: error.constructor.name
    })

    try {
      switch (strategy.type) {
        case 'retry':
          return await this.handleRetry(error, context, strategy)
        
        case 'fallback':
          return {
            handled: true,
            recovered: true,
            action: 'fallback',
            result: strategy.fallbackValue
          }
        
        case 'skip':
          return {
            handled: true,
            recovered: true,
            action: 'skip',
            result: null
          }
        
        case 'abort':
          return {
            handled: true,
            recovered: false,
            action: 'abort'
          }
        
        default:
          return { handled: true, recovered: false, action: 'unknown_strategy' }
      }
    } catch (recoveryError) {
      performanceMonitor.recordMetric('error_recovery_failed', 1, 'count', {
        strategy: recoveryKey,
        errorType: error.constructor.name
      })

      return { handled: true, recovered: false, action: 'recovery_failed' }
    }
  }

  private async handleRetry(
    error: Error,
    context: ErrorContext,
    strategy: ErrorRecoveryAction
  ): Promise<{
    handled: boolean
    recovered: boolean
    action?: string
    result?: any
  }> {
    const maxAttempts = strategy.maxAttempts || 1
    const delay = strategy.delay || 0

    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      if (attempt > 1 && delay > 0) {
        await new Promise(resolve => setTimeout(resolve, delay * attempt))
      }

      try {
        // 这里需要重新执行原始操作
        // 由于我们无法直接重新执行，我们返回一个标记让调用者处理
        performanceMonitor.recordMetric('error_recovery_successful', 1, 'count', {
          attempt,
          errorType: error.constructor.name
        })

        return {
          handled: true,
          recovered: true,
          action: 'retry_successful',
          result: { shouldRetry: true, attempt }
        }
      } catch (retryError) {
        if (attempt === maxAttempts) {
          throw retryError
        }
      }
    }

    return { handled: true, recovered: false, action: 'retry_exhausted' }
  }

  private isCircuitOpen(key: string): boolean {
    const breaker = this.circuitBreakers.get(key)
    if (!breaker) return false

    if (breaker.isOpen) {
      const timeSinceLastFailure = Date.now() - breaker.lastFailure.getTime()
      if (timeSinceLastFailure > breaker.timeout) {
        breaker.isOpen = false
        breaker.failures = 0
      }
    }

    return breaker.isOpen
  }

  private updateCircuitBreaker(key: string, success: boolean): void {
    const breaker = this.circuitBreakers.get(key)
    if (!breaker) return

    if (success) {
      breaker.failures = 0
      breaker.isOpen = false
    } else {
      breaker.failures++
      breaker.lastFailure = new Date()
      
      if (breaker.failures >= breaker.threshold) {
        breaker.isOpen = true
      }
    }
  }

  private determineErrorSeverity(error: Error): 'low' | 'medium' | 'high' | 'critical' {
    const message = error.message.toLowerCase()
    
    if (message.includes('database') || message.includes('connection')) {
      return 'critical'
    }
    
    if (message.includes('permission') || message.includes('unauthorized')) {
      return 'high'
    }
    
    if (message.includes('validation') || message.includes('invalid')) {
      return 'medium'
    }
    
    return 'low'
  }

  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }
}

// 导出单例实例
export const globalErrorHandler = GlobalErrorHandler.getInstance()