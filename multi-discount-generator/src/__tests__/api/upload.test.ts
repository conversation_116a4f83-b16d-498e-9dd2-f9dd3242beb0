import { createMocks } from 'node-mocks-http'
import handler from '@/pages/api/upload'
import { FileUploadService } from '@/lib/upload/FileUploadService'
import { TestDataGenerator } from '@/lib/testing/TestDataGenerator'

// Mock dependencies
jest.mock('@/lib/upload/FileUploadService')
jest.mock('multer')

describe('/api/upload', () => {
  const mockFileUploadService = FileUploadService as jest.Mocked<typeof FileUploadService>

  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('should upload file successfully', async () => {
    const mockFile = {
      buffer: Buffer.from('test,data\nPROD001,商品1'),
      originalname: 'test.csv',
      mimetype: 'text/csv',
      size: 100
    }

    const mockUploadResult = {
      fileId: 'file-123',
      path: '/uploads/test.csv',
      size: 100
    }

    mockFileUploadService.uploadFile.mockResolvedValue(mockUploadResult)

    // Mock multer middleware
    const multer = require('multer')
    const mockMulter = {
      single: jest.fn().mockReturnValue((req: any, res: any, next: any) => {
        req.file = mockFile
        next()
      })
    }
    multer.mockReturnValue(mockMulter)

    const { req, res } = createMocks({
      method: 'POST',
      headers: {
        'content-type': 'multipart/form-data'
      }
    })

    // Simulate file attachment
    req.file = mockFile

    await handler(req, res)

    expect(res._getStatusCode()).toBe(200)
    
    const responseData = JSON.parse(res._getData())
    expect(responseData.success).toBe(true)
    expect(responseData.data.fileId).toBe('file-123')
    expect(mockFileUploadService.uploadFile).toHaveBeenCalledWith(mockFile)
  })

  it('should return 400 when no file provided', async () => {
    const { req, res } = createMocks({
      method: 'POST',
      headers: {
        'content-type': 'multipart/form-data'
      }
    })

    // No file attached
    req.file = undefined

    await handler(req, res)

    expect(res._getStatusCode()).toBe(400)
    
    const responseData = JSON.parse(res._getData())
    expect(responseData.success).toBe(false)
    expect(responseData.error).toContain('未选择文件')
  })

  it('should handle upload error', async () => {
    const mockFile = {
      buffer: Buffer.from('invalid content'),
      originalname: 'invalid.txt',
      mimetype: 'text/plain',
      size: 100
    }

    mockFileUploadService.uploadFile.mockRejectedValue(new Error('文件格式不支持'))

    const multer = require('multer')
    const mockMulter = {
      single: jest.fn().mockReturnValue((req: any, res: any, next: any) => {
        req.file = mockFile
        next()
      })
    }
    multer.mockReturnValue(mockMulter)

    const { req, res } = createMocks({
      method: 'POST',
      headers: {
        'content-type': 'multipart/form-data'
      }
    })

    req.file = mockFile

    await handler(req, res)

    expect(res._getStatusCode()).toBe(500)
    
    const responseData = JSON.parse(res._getData())
    expect(responseData.success).toBe(false)
    expect(responseData.error).toContain('文件上传失败')
  })

  it('should return 405 for non-POST method', async () => {
    const { req, res } = createMocks({
      method: 'GET'
    })

    await handler(req, res)

    expect(res._getStatusCode()).toBe(405)
  })

  it('should handle multer errors', async () => {
    const multer = require('multer')
    const mockMulter = {
      single: jest.fn().mockReturnValue((req: any, res: any, next: any) => {
        const error = new Error('文件太大')
        error.code = 'LIMIT_FILE_SIZE'
        next(error)
      })
    }
    multer.mockReturnValue(mockMulter)

    const { req, res } = createMocks({
      method: 'POST',
      headers: {
        'content-type': 'multipart/form-data'
      }
    })

    await handler(req, res)

    expect(res._getStatusCode()).toBe(400)
    
    const responseData = JSON.parse(res._getData())
    expect(responseData.success).toBe(false)
    expect(responseData.error).toContain('文件大小超过限制')
  })

  it('should parse uploaded file', async () => {
    const mockFile = {
      buffer: Buffer.from('ID,名称\nPROD001,商品1\nPROD002,商品2'),
      originalname: 'products.csv',
      mimetype: 'text/csv',
      size: 200
    }

    const mockUploadResult = {
      fileId: 'file-123',
      path: '/uploads/products.csv',
      size: 200
    }

    const mockParseResult = {
      products: ['PROD001', 'PROD002'],
      summary: {
        total: 2,
        valid: 2,
        duplicates: 0,
        invalid: 0
      }
    }

    mockFileUploadService.uploadFile.mockResolvedValue(mockUploadResult)
    mockFileUploadService.parseProductFile.mockResolvedValue(mockParseResult)

    const multer = require('multer')
    const mockMulter = {
      single: jest.fn().mockReturnValue((req: any, res: any, next: any) => {
        req.file = mockFile
        next()
      })
    }
    multer.mockReturnValue(mockMulter)

    const { req, res } = createMocks({
      method: 'POST',
      headers: {
        'content-type': 'multipart/form-data'
      },
      body: {
        parse: 'true'
      }
    })

    req.file = mockFile

    await handler(req, res)

    expect(res._getStatusCode()).toBe(200)
    
    const responseData = JSON.parse(res._getData())
    expect(responseData.success).toBe(true)
    expect(responseData.data.products).toHaveLength(2)
    expect(responseData.data.summary.valid).toBe(2)
    expect(mockFileUploadService.parseProductFile).toHaveBeenCalledWith('file-123')
  })

  it('should handle parse error', async () => {
    const mockFile = {
      buffer: Buffer.from('invalid csv content'),
      originalname: 'invalid.csv',
      mimetype: 'text/csv',
      size: 100
    }

    const mockUploadResult = {
      fileId: 'file-123',
      path: '/uploads/invalid.csv',
      size: 100
    }

    mockFileUploadService.uploadFile.mockResolvedValue(mockUploadResult)
    mockFileUploadService.parseProductFile.mockRejectedValue(new Error('文件解析失败'))

    const multer = require('multer')
    const mockMulter = {
      single: jest.fn().mockReturnValue((req: any, res: any, next: any) => {
        req.file = mockFile
        next()
      })
    }
    multer.mockReturnValue(mockMulter)

    const { req, res } = createMocks({
      method: 'POST',
      headers: {
        'content-type': 'multipart/form-data'
      },
      body: {
        parse: 'true'
      }
    })

    req.file = mockFile

    await handler(req, res)

    expect(res._getStatusCode()).toBe(500)
    
    const responseData = JSON.parse(res._getData())
    expect(responseData.success).toBe(false)
    expect(responseData.error).toContain('文件解析失败')
  })
})