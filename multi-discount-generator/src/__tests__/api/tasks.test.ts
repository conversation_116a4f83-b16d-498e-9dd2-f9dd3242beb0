import { createMocks } from 'node-mocks-http'
import handler from '@/pages/api/tasks/create'
import listHandler from '@/pages/api/tasks/list'
import { TestDataGenerator } from '@/lib/testing/TestDataGenerator'
import { prisma } from '@/lib/prisma'

// Mock dependencies
jest.mock('@/lib/prisma', () => ({
  prisma: {
    task: {
      create: jest.fn(),
      findMany: jest.fn(),
      count: jest.fn(),
      findUnique: jest.fn(),
      update: jest.fn()
    },
    taskLog: {
      create: jest.fn()
    }
  }
}))

jest.mock('@/lib/batch/TaskQueue', () => ({
  TaskQueue: {
    getInstance: jest.fn().mockReturnValue({
      createTask: jest.fn(),
      listTasks: jest.fn(),
      getTask: jest.fn(),
      updateTaskStatus: jest.fn()
    })
  }
}))

describe('/api/tasks/create', () => {
  const mockPrisma = prisma as jest.Mocked<typeof prisma>

  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('should create task successfully', async () => {
    const taskInput = TestDataGenerator.generateTaskInput({
      name: 'API测试任务',
      products: ['PROD001', 'PROD002']
    })

    const mockTask = {
      id: 'task-123',
      name: taskInput.name,
      type: taskInput.type,
      status: 'PENDING',
      config: taskInput.config,
      priority: taskInput.priority,
      progress: 0,
      createdAt: new Date(),
      updatedAt: new Date()
    }

    const { TaskQueue } = require('@/lib/batch/TaskQueue')
    const mockTaskQueue = TaskQueue.getInstance()
    mockTaskQueue.createTask.mockResolvedValue(mockTask)

    const { req, res } = createMocks({
      method: 'POST',
      body: taskInput
    })

    await handler(req, res)

    expect(res._getStatusCode()).toBe(200)
    
    const responseData = JSON.parse(res._getData())
    expect(responseData.success).toBe(true)
    expect(responseData.data.id).toBe('task-123')
    expect(mockTaskQueue.createTask).toHaveBeenCalledWith(taskInput)
  })

  it('should return 400 for invalid input', async () => {
    const invalidInput = {
      name: '', // 空名称
      type: 'INVALID',
      config: {},
      products: [],
      priority: -1
    }

    const { req, res } = createMocks({
      method: 'POST',
      body: invalidInput
    })

    await handler(req, res)

    expect(res._getStatusCode()).toBe(400)
    
    const responseData = JSON.parse(res._getData())
    expect(responseData.success).toBe(false)
    expect(responseData.error).toContain('验证失败')
  })

  it('should return 405 for non-POST method', async () => {
    const { req, res } = createMocks({
      method: 'GET'
    })

    await handler(req, res)

    expect(res._getStatusCode()).toBe(405)
  })

  it('should handle task creation error', async () => {
    const taskInput = TestDataGenerator.generateTaskInput()

    const { TaskQueue } = require('@/lib/batch/TaskQueue')
    const mockTaskQueue = TaskQueue.getInstance()
    mockTaskQueue.createTask.mockRejectedValue(new Error('创建任务失败'))

    const { req, res } = createMocks({
      method: 'POST',
      body: taskInput
    })

    await handler(req, res)

    expect(res._getStatusCode()).toBe(500)
    
    const responseData = JSON.parse(res._getData())
    expect(responseData.success).toBe(false)
    expect(responseData.error).toContain('创建任务失败')
  })
})

describe('/api/tasks/list', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('should list tasks successfully', async () => {
    const mockTasks = [
      {
        id: 'task-1',
        name: '任务1',
        status: 'PENDING',
        createdAt: new Date()
      },
      {
        id: 'task-2',
        name: '任务2',
        status: 'RUNNING',
        createdAt: new Date()
      }
    ]

    const mockResult = {
      tasks: mockTasks,
      total: 2,
      page: 1,
      limit: 20
    }

    const { TaskQueue } = require('@/lib/batch/TaskQueue')
    const mockTaskQueue = TaskQueue.getInstance()
    mockTaskQueue.listTasks.mockResolvedValue(mockResult)

    const { req, res } = createMocks({
      method: 'GET',
      query: {
        page: '1',
        limit: '20'
      }
    })

    await listHandler(req, res)

    expect(res._getStatusCode()).toBe(200)
    
    const responseData = JSON.parse(res._getData())
    expect(responseData.success).toBe(true)
    expect(responseData.data.tasks).toHaveLength(2)
    expect(responseData.data.total).toBe(2)
  })

  it('should filter tasks by status', async () => {
    const mockResult = {
      tasks: [
        {
          id: 'task-1',
          name: '运行中任务',
          status: 'RUNNING',
          createdAt: new Date()
        }
      ],
      total: 1,
      page: 1,
      limit: 20
    }

    const { TaskQueue } = require('@/lib/batch/TaskQueue')
    const mockTaskQueue = TaskQueue.getInstance()
    mockTaskQueue.listTasks.mockResolvedValue(mockResult)

    const { req, res } = createMocks({
      method: 'GET',
      query: {
        status: 'RUNNING'
      }
    })

    await listHandler(req, res)

    expect(mockTaskQueue.listTasks).toHaveBeenCalledWith({
      page: 1,
      limit: 20,
      status: 'RUNNING'
    })
  })

  it('should handle pagination parameters', async () => {
    const { TaskQueue } = require('@/lib/batch/TaskQueue')
    const mockTaskQueue = TaskQueue.getInstance()
    mockTaskQueue.listTasks.mockResolvedValue({
      tasks: [],
      total: 0,
      page: 2,
      limit: 10
    })

    const { req, res } = createMocks({
      method: 'GET',
      query: {
        page: '2',
        limit: '10'
      }
    })

    await listHandler(req, res)

    expect(mockTaskQueue.listTasks).toHaveBeenCalledWith({
      page: 2,
      limit: 10
    })
  })

  it('should return 405 for non-GET method', async () => {
    const { req, res } = createMocks({
      method: 'POST'
    })

    await listHandler(req, res)

    expect(res._getStatusCode()).toBe(405)
  })

  it('should handle list error', async () => {
    const { TaskQueue } = require('@/lib/batch/TaskQueue')
    const mockTaskQueue = TaskQueue.getInstance()
    mockTaskQueue.listTasks.mockRejectedValue(new Error('获取任务列表失败'))

    const { req, res } = createMocks({
      method: 'GET'
    })

    await listHandler(req, res)

    expect(res._getStatusCode()).toBe(500)
    
    const responseData = JSON.parse(res._getData())
    expect(responseData.success).toBe(false)
    expect(responseData.error).toContain('获取任务列表失败')
  })
})