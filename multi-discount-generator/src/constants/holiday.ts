import type { TimeCalculationConfig } from '@/types/holiday'

// 中国时区配置
export const CHINA_TIME_CONFIG: TimeCalculationConfig = {
  timezone: 'Asia/Shanghai',
  useHolidays: true,
  fallbackToWeekend: true
}

// 中国法定节假日名称（用于显示）
export const CHINESE_HOLIDAYS = {
  NEW_YEAR: '元旦',
  SPRING_FESTIVAL: '春节',
  TOMB_SWEEPING: '清明节',
  LABOR_DAY: '劳动节',
  DRAGON_BOAT: '端午节',
  MID_AUTUMN: '中秋节',
  NATIONAL_DAY: '国庆节'
} as const

// 节假日缓存配置
export const HOLIDAY_CACHE_CONFIG = {
  MAX_CACHE_YEARS: 5, // 最多缓存5年的数据
  CACHE_KEY_PREFIX: 'chinese_holidays_',
  CACHE_EXPIRY_DAYS: 30 // 缓存30天后过期
} as const

// 节假日API配置（备用方案）
export const HOLIDAY_API_CONFIG = {
  TIMEOUT: 5000, // 5秒超时
  RETRY_COUNT: 3,
  RETRY_DELAY: 1000 // 1秒重试间隔
} as const

// 默认节假日数据（当API失败时使用）
export const DEFAULT_HOLIDAYS_2025 = [
  { date: '2025-01-01', name: '元旦', isOffDay: true },
  { date: '2025-01-28', name: '春节', isOffDay: true },
  { date: '2025-01-29', name: '春节', isOffDay: true },
  { date: '2025-01-30', name: '春节', isOffDay: true },
  { date: '2025-01-31', name: '春节', isOffDay: true },
  { date: '2025-02-01', name: '春节', isOffDay: true },
  { date: '2025-02-02', name: '春节', isOffDay: true },
  { date: '2025-02-03', name: '春节', isOffDay: true },
  { date: '2025-04-05', name: '清明节', isOffDay: true },
  { date: '2025-04-06', name: '清明节', isOffDay: true },
  { date: '2025-04-07', name: '清明节', isOffDay: true },
  { date: '2025-05-01', name: '劳动节', isOffDay: true },
  { date: '2025-05-02', name: '劳动节', isOffDay: true },
  { date: '2025-05-03', name: '劳动节', isOffDay: true },
  { date: '2025-05-04', name: '劳动节', isOffDay: true },
  { date: '2025-05-05', name: '劳动节', isOffDay: true },
  { date: '2025-05-31', name: '端午节', isOffDay: true },
  { date: '2025-06-02', name: '端午节', isOffDay: true },
  { date: '2025-10-01', name: '国庆节', isOffDay: true },
  { date: '2025-10-02', name: '国庆节', isOffDay: true },
  { date: '2025-10-03', name: '国庆节', isOffDay: true },
  { date: '2025-10-04', name: '国庆节', isOffDay: true },
  { date: '2025-10-05', name: '国庆节', isOffDay: true },
  { date: '2025-10-06', name: '国庆节', isOffDay: true },
  { date: '2025-10-07', name: '国庆节', isOffDay: true },
  { date: '2025-10-08', name: '国庆节', isOffDay: true }
] as const