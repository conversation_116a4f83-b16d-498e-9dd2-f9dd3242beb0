import type { RegionalArea } from '@/types'

// 区域列表
export const REGIONAL_AREAS: RegionalArea[] = [
  '华中',
  '西南/西北', 
  '华南',
  '华东',
  '华北'
]

// 满件数量选项 (1-10)
export const QUANTITY_OPTIONS = Array.from({ length: 10 }, (_, i) => i + 1)

// 折扣范围 (5.0-9.9，步长0.1)
export const DISCOUNT_OPTIONS = Array.from(
  { length: 50 }, 
  (_, i) => Number((5.0 + i * 0.1).toFixed(1))
)

// 默认值
export const DEFAULT_VALUES = {
  DISCOUNT: 8.0,
  MIN_QUANTITY: 1,
  ACTIVITY_TYPE: '多件多折',
  WEEKEND_FULL_DAY: true,
  TIME_SLOT_COUNT: 5,
  SLOT_DURATION: 1 as const,
  DAYS: 30
} as const

// 快速天数选择选项
export const QUICK_DAYS_OPTIONS = [
  { value: 7, label: '一周 (7天)' },
  { value: 15, label: '半月 (15天)' },
  { value: 30, label: '一月 (30天)' },
  { value: 60, label: '两月 (60天)' },
  { value: 90, label: '三月 (90天)' }
] as const

// 时间格式
export const TIME_FORMAT = 'yyyy-MM-dd HH:mm:ss'
export const DATE_FORMAT = 'yyyy-MM-dd'

// 中国时区
export const CHINA_TIMEZONE = 'Asia/Shanghai'

// Excel相关常量
export const EXCEL_CONFIG = {
  FILE_EXTENSION: '.xlsx',
  SHEET_NAME: 'Sheet1',
  COMPRESSION: true,
  BOOK_TYPE: 'xlsx' as const
} as const

// Excel列标题（与原模板完全一致）
export const EXCEL_HEADERS = [
  '活动类型',
  '商品ID',
  '开始时间\n示例：\n2020-03-27 00:00:00',
  '结束时间\n示例：\n2020-03-27 23:59:59', 
  '活动区域\n支持填写"全国"及5大区\n5大区：华中,西南/西北,华南,华东,华北',
  '满几件\n说明：表示满N件，件数只能在下拉框中选择',
  '折扣-打几折\n说明：表示打几折，折扣只能下拉选择',
  '立减金额-多件总额下的扣减金额\n表示扣减的金额，单位元，支持最多2位小数，与折扣只能择一生效'
] as const

// Excel列宽配置
export const EXCEL_COLUMN_WIDTHS = [
  { wch: 12 }, // 活动类型
  { wch: 15 }, // 商品ID
  { wch: 25 }, // 开始时间
  { wch: 25 }, // 结束时间
  { wch: 30 }, // 活动区域
  { wch: 20 }, // 满几件
  { wch: 20 }, // 折扣
  { wch: 35 }  // 立减金额
] as const

// 时间相关常量
export const TIME_CONSTANTS = {
  WEEKEND_DAYS: [0, 6], // 周日=0, 周六=6
  FRIDAY: 5,
  SATURDAY: 6,
  SUNDAY: 0,
  MONDAY: 1,
  MIN_RANDOM_GAP_MINUTES: 30, // 随机时间段最小间隔
  MAX_RANDOM_GAP_MINUTES: 240, // 随机时间段最大间隔
  FULL_DAY_START: '00:00:00',
  FULL_DAY_END: '23:59:59'
} as const

// 验证规则常量
export const VALIDATION_RULES = {
  PRODUCT_ID: {
    MIN_LENGTH: 1,
    MAX_LENGTH: 50
  },
  DISCOUNT: {
    MIN: 5.0,
    MAX: 9.9,
    STEP: 0.1
  },
  QUANTITY: {
    MIN: 1,
    MAX: 10
  },
  DAYS: {
    MIN: 1,
    MAX: 365
  },
  TIME_SLOT_COUNT: {
    MIN: 1,
    MAX: 50
  }
} as const

// 错误消息
export const ERROR_MESSAGES = {
  REQUIRED_FIELD: '此字段为必填项',
  INVALID_PRODUCT_ID: '商品ID格式不正确',
  INVALID_DISCOUNT: '折扣必须在5.0-9.9之间，步长0.1',
  INVALID_QUANTITY: '满件数量必须在1-10之间',
  INVALID_TIME_RANGE: '结束时间必须晚于开始时间',
  INVALID_DAYS: '天数必须在1-365之间',
  REGION_REQUIRED: '请选择活动区域',
  TIME_GENERATION_FAILED: '时间生成失败，请检查配置',
  EXCEL_EXPORT_FAILED: 'Excel导出失败，请重试',
  HOLIDAY_API_FAILED: '节假日数据获取失败，使用默认配置'
} as const