import { Task, TaskLog, TaskStatus, TaskType } from '@prisma/client'

// 任务相关类型
export interface TaskWithRelations extends Task {
  logs: TaskLog[]
}

export interface CreateTaskInput {
  name: string
  type: TaskType
  config: Record<string, any>
  priority?: number
}

export interface TaskListOptions {
  page?: number
  limit?: number
  status?: TaskStatus
  type?: TaskType
  sortBy?: 'createdAt' | 'updatedAt' | 'priority'
  sortOrder?: 'asc' | 'desc'
}

export interface TaskStats {
  pending: number
  running: number
  completed: number
  failed: number
  paused: number
  cancelled: number
  total: number
}

export interface TaskProgress {
  taskId: string
  progress: number
  message?: string
  timestamp: Date
}

// 队列配置
export interface QueueConfig {
  maxConcurrentTasks: number
  retryAttempts: number
  retryDelay: number
  taskTimeout: number
  cleanupInterval: number
}

// 商品处理结果
export interface ProductProcessResult {
  productId: string
  success: boolean
  data?: any
  error?: string
}

// 批量处理配置
export interface BatchConfig {
  products: string[]
  templateConfig: {
    region: any
    minQuantity: number
    discount: number
    timeMode: 'continuous' | 'random'
    timeConfig: any
  }
  outputFormat: 'excel' | 'csv'
  fileName?: string
}

// 文件上传结果
export interface FileUploadResult {
  success: boolean
  fileName: string
  filePath: string
  fileSize: number
  productCount?: number
  errors?: string[]
}

// 批量导入配置
export interface BatchImportConfig {
  filePath: string
  validateOnly: boolean
  skipErrors: boolean
  batchSize: number
}

// WebSocket事件类型
export interface SocketEvents {
  'task:created': (task: TaskWithRelations) => void
  'task:updated': (task: TaskWithRelations) => void
  'task:progress': (progress: TaskProgress) => void
  'task:completed': (taskId: string) => void
  'task:failed': (taskId: string, error: string) => void
}

// API响应类型
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

export interface PaginatedResponse<T> {
  items: T[]
  total: number
  page: number
  limit: number
  totalPages: number
}

// 系统监控类型
export interface SystemMetrics {
  cpu: {
    usage: number
    cores: number
  }
  memory: {
    used: number
    total: number
    percentage: number
  }
  disk: {
    used: number
    total: number
    percentage: number
  }
  tasks: {
    active: number
    pending: number
    completed: number
    failed: number
  }
  uptime: number
  timestamp: Date
}

// 日志类型
export interface LogEntry {
  id: string
  level: 'info' | 'warn' | 'error' | 'debug'
  message: string
  timestamp: Date
  source?: string
  metadata?: Record<string, any>
}

export interface LogFilter {
  level?: string
  source?: string
  startDate?: Date
  endDate?: Date
  search?: string
  page?: number
  limit?: number
}

// 错误处理类型
export interface ErrorContext {
  taskId?: string
  userId?: string
  action: string
  timestamp: Date
  metadata?: Record<string, any>
}

export interface RetryConfig {
  maxAttempts: number
  delay: number
  backoff: 'linear' | 'exponential'
  maxDelay?: number
}

// 性能监控类型
export interface PerformanceMetric {
  name: string
  value: number
  unit: string
  timestamp: Date
  tags?: Record<string, string>
}

export interface PerformanceReport {
  period: {
    start: Date
    end: Date
  }
  metrics: {
    taskThroughput: number
    averageProcessingTime: number
    errorRate: number
    systemLoad: number
  }
  trends: {
    metric: string
    trend: 'up' | 'down' | 'stable'
    change: number
  }[]
}