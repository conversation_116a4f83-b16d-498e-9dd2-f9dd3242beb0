// 区域类型定义
export type RegionalArea = '华中' | '西南/西北' | '华南' | '华东' | '华北'

export type RegionType =
  | { type: 'national' }
  | { type: 'regional'; regions: RegionalArea[] }

// 时间段类型
export interface TimeSlot {
  startTime: Date
  endTime: Date
}

// 表单数据类型
export interface FormData {
  productId: string
  region: RegionType
  minQuantity: number // 1-10件下拉选择
  discount: number // 默认8.0折
  timeMode: 'continuous' | 'random'
  // 连续模式
  startDate: Date // 活动开始日期
  startTime: Date
  endTime: Date
  days: number
  weekendFullDay: boolean // 包含法定节假日
  // 随机模式
  randomStartTime: Date
  timeSlotCount: number
  slotDuration: 1 | 2 // 小时
}

// 连续时间配置
export interface ContinuousConfig {
  startDate: Date
  startTime: Date
  endTime: Date
  days: number
  weekendFullDay: boolean
}

// 随机时间配置
export interface RandomConfig {
  startTime: Date
  slotCount: number
  slotDuration: 1 | 2
}

// Excel行数据类型（与原模板完全一致）
export interface ExcelRowData {
  活动类型: string // 固定"多件多折"
  商品ID: string
  '开始时间\n示例：\n2020-03-27 00:00:00': string // 完整列标题
  '结束时间\n示例：\n2020-03-27 23:59:59': string // 完整列标题
  '活动区域\n支持填写"全国"及5大区\n5大区：华中,西南/西北,华南,华东,华北': string
  '满几件\n说明：表示满N件，件数只能在下拉框中选择': number
  '折扣-打几折\n说明：表示打几折，折扣只能下拉选择': number
  '立减金额-多件总额下的扣减金额\n表示扣减的金额，单位元，支持最多2位小数，与折扣只能择一生效': string
}

// Excel模板数据结构
export interface ExcelTemplate {
  columns: ExcelRowData[]
}

// 组件Props类型
export interface TemplateGeneratorProps {
  initialData?: Partial<FormData>
}

export interface RegionSelectorProps {
  value: RegionType
  onChange: (value: RegionType) => void
}

export interface TimeGeneratorProps {
  mode: 'continuous' | 'random'
  config: ContinuousConfig | RandomConfig
  onChange: (timeSlots: TimeSlot[]) => void
}

export interface PreviewTableProps {
  data: ExcelRowData[]
  onExport: () => void
}

// 错误类型
export interface ValidationError {
  field: string
  message: string
}

export interface AppError {
  type: 'validation' | 'network' | 'system'
  message: string
  details?: string
}

// 导出批处理相关类型
export * from './batch'