// 节假日相关类型定义

export interface HolidayInfo {
  date: string // YYYY-MM-DD格式
  name: string
  isOffDay: boolean // 是否为休息日
}

export interface HolidayYear {
  year: number
  holidays: HolidayInfo[]
}

export interface HolidayCache {
  [year: number]: HolidayInfo[]
}

// 工作日类型
export type WorkdayType = 'workday' | 'weekend' | 'holiday'

// 时间计算配置
export interface TimeCalculationConfig {
  timezone: string
  useHolidays: boolean
  fallbackToWeekend: boolean // 节假日API失败时是否降级到周末判断
}

// 时间段生成结果
export interface TimeSlotGenerationResult {
  slots: Array<{
    startTime: Date
    endTime: Date
    type: WorkdayType
    isHoliday?: boolean
    holidayName?: string
  }>
  warnings: string[]
  errors: string[]
}