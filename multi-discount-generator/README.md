# 多折扣模板生成器 - 批量处理系统

一个基于Next.js的多折扣模板生成器，支持批量处理、实时监控和系统管理功能。

## 功能特性

### 核心功能
- 🎯 多折扣模板生成
- 📊 批量Excel文件处理
- ⚡ 实时任务监控
- 📈 系统性能监控
- 🔍 日志聚合分析
- 🛡️ 健康检查系统

### 批量处理系统
- 📁 文件上传和解析（Excel/CSV）
- 🔄 任务队列管理
- 📊 实时进度跟踪
- 🔁 任务重试机制
- 📋 批量配置管理

### 监控和管理
- 📊 系统性能指标收集
- 📝 结构化日志记录
- 🔍 日志搜索和过滤
- 📈 性能趋势分析
- 🚨 健康状态检查

## 技术栈

- **前端**: Next.js 15, React 19, TypeScript, Tailwind CSS
- **后端**: Next.js API Routes, Prisma ORM
- **数据库**: SQLite (开发环境)
- **实时通信**: Socket.io
- **文件处理**: xlsx, multer
- **监控**: 自定义性能监控系统

## 快速开始

### 环境要求
- Node.js 18+
- npm 或 yarn

### 安装依赖
```bash
npm install
```

### 数据库设置
```bash
# 初始化数据库
npx prisma db push

# 填充种子数据
npm run db:seed
```

### 启动开发服务器
```bash
npm run dev
```

访问 http://localhost:3000 查看应用。

## 项目结构

```
src/
├── app/                    # Next.js App Router
│   ├── admin/             # 管理后台页面
│   └── batch/             # 批量处理页面
├── components/            # React组件
│   ├── admin/            # 管理组件
│   ├── batch/            # 批量处理组件
│   ├── forms/            # 表单组件
│   ├── tasks/            # 任务组件
│   ├── templates/        # 模板组件
│   └── ui/               # 基础UI组件
├── lib/                  # 核心库
│   ├── batch/           # 批量处理服务
│   ├── monitoring/      # 监控服务
│   ├── performance/     # 性能监控
│   ├── system/          # 系统管理
│   ├── testing/         # 测试工具
│   ├── upload/          # 文件上传
│   └── websocket/       # WebSocket服务
├── pages/api/           # API路由
├── types/               # TypeScript类型定义
└── constants/           # 常量定义
```

## API文档

### 任务管理
- `POST /api/tasks/create` - 创建任务
- `GET /api/tasks/[id]` - 获取任务详情
- `GET /api/tasks/list` - 获取任务列表
- `POST /api/tasks/[id]/pause` - 暂停任务
- `POST /api/tasks/[id]/resume` - 恢复任务
- `POST /api/tasks/[id]/cancel` - 取消任务

### 文件管理
- `POST /api/upload` - 上传文件
- `GET /api/download/[id]` - 下载文件
- `GET /api/preview/[id]` - 预览文件
- `POST /api/files/manage` - 文件管理

### 系统监控
- `GET /api/health` - 健康检查
- `GET /api/stats` - 系统统计
- `GET /api/logs` - 日志查询
- `GET /api/logs/aggregate` - 日志聚合
- `GET /api/logs/patterns` - 日志模式分析
- `GET /api/logs/export` - 日志导出
- `POST /api/logs/cleanup` - 日志清理

### 系统管理
- `POST /api/system/init` - 系统初始化
- `GET /api/system/init` - 获取系统状态
- `DELETE /api/system/init` - 关闭系统

## 测试

### 运行所有测试
```bash
npm test
```

### 运行特定类型的测试
```bash
# 单元测试
npm run test:unit

# 集成测试
npm run test:integration

# 性能测试
npm run test:performance

# 健康检查测试
npm run test:health
```

## 部署

### 构建生产版本
```bash
npm run build
```

### 启动生产服务器
```bash
npm start
```

### 环境变量
创建 `.env.local` 文件：

```env
# 数据库
DATABASE_URL="file:./dev.db"

# 应用配置
NODE_ENV=production
NEXT_PUBLIC_APP_URL=http://localhost:3000

# 文件上传
MAX_FILE_SIZE=10485760
UPLOAD_DIR=./uploads

# 监控配置
ENABLE_PERFORMANCE_MONITORING=true
LOG_RETENTION_DAYS=30
```

## 监控和运维

### 系统监控
- 访问 `/admin/monitor` 查看系统监控面板
- 实时查看CPU、内存、任务队列状态
- 性能指标趋势分析

### 日志管理
- 访问 `/admin/logs` 查看系统日志
- 支持日志搜索、过滤和导出
- 自动日志清理和归档

### 健康检查
- `GET /api/health` - 基础健康检查
- `GET /api/health?detailed=true` - 详细健康检查
- `GET /api/health?history=true` - 健康检查历史

## 开发指南

### 添加新的任务类型
1. 在 `src/types/batch.ts` 中定义任务类型
2. 在 `src/lib/batch/TaskWorker.ts` 中实现处理逻辑
3. 在前端添加相应的UI组件

### 添加新的监控指标
1. 使用 `performanceMonitor.recordMetric()` 记录指标
2. 在监控面板中添加显示逻辑
3. 配置告警规则（如需要）

### 自定义日志记录
```typescript
import { prisma } from '@/lib/prisma'

await prisma.taskLog.create({
  data: {
    taskId: 'task-id',
    level: 'INFO',
    message: '自定义日志消息',
    metadata: {
      source: 'custom-service',
      additionalData: 'value'
    }
  }
})
```

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查数据库文件权限
   - 运行 `npx prisma db push` 重新初始化

2. **文件上传失败**
   - 检查上传目录权限
   - 确认文件大小限制

3. **任务执行失败**
   - 查看任务日志详情
   - 检查系统资源使用情况

4. **WebSocket连接问题**
   - 检查防火墙设置
   - 确认端口未被占用

### 性能优化

1. **数据库优化**
   - 定期清理旧日志
   - 添加必要的索引
   - 使用连接池

2. **内存管理**
   - 监控内存使用情况
   - 及时清理临时文件
   - 优化大文件处理

3. **任务队列优化**
   - 调整并发数量
   - 优化任务优先级
   - 实现任务分片

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 更新日志

### v0.1.0 (2024-01-XX)
- ✨ 初始版本发布
- 🎯 多折扣模板生成功能
- 📊 批量处理系统
- 📈 系统监控和日志
- 🔍 健康检查系统
- 📝 完整的API文档
- 🧪 集成测试套件

## 支持

如有问题或建议，请：
1. 查看文档和FAQ
2. 搜索已有的Issues
3. 创建新的Issue描述问题
4. 联系维护团队

---

**注意**: 这是一个开发中的项目，某些功能可能还在完善中。建议在生产环境使用前进行充分测试。