// 检查模板文件详细内容
const XLSX = require('xlsx');
const path = require('path');

function checkTemplate() {
  console.log('🔍 检查模板文件详细内容...');
  
  try {
    // 读取测试模板
    const templatePath = path.join(__dirname, '正确格式批量生成模板.xlsx');
    console.log('📁 模板路径:', templatePath);
    
    const workbook = XLSX.readFile(templatePath);
    console.log('📊 工作表列表:', workbook.SheetNames);
    
    const sheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];
    
    // 获取工作表范围
    const range = XLSX.utils.decode_range(worksheet['!ref']);
    console.log('📏 工作表范围:', worksheet['!ref']);
    console.log('📏 行数范围:', range.s.r, '到', range.e.r);
    console.log('📏 列数范围:', range.s.c, '到', range.e.c);
    
    // 使用header: 1读取原始数据
    const rawData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
    console.log('📋 原始数据行数:', rawData.length);
    
    rawData.forEach((row, index) => {
      console.log(`第${index + 1}行原始数据:`, row);
    });
    
    console.log('---');
    
    // 使用默认设置读取
    const jsonData = XLSX.utils.sheet_to_json(worksheet);
    console.log('📋 JSON数据行数:', jsonData.length);
    
    jsonData.forEach((row, index) => {
      console.log(`第${index + 1}行JSON数据:`, JSON.stringify(row, null, 2));
    });
    
    // 检查特定单元格
    console.log('---');
    console.log('🔍 检查特定单元格:');
    for (let r = range.s.r; r <= range.e.r; r++) {
      for (let c = range.s.c; c <= range.e.c; c++) {
        const cellAddress = XLSX.utils.encode_cell({ r, c });
        const cell = worksheet[cellAddress];
        if (cell) {
          console.log(`${cellAddress}: ${cell.v} (类型: ${cell.t}, 格式: ${cell.z || 'none'})`);
        }
      }
    }
    
  } catch (error) {
    console.error('❌ 检查失败:', error);
  }
}

// 运行检查
checkTemplate();
