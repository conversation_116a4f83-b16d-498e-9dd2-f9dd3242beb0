// 测试批量导出API功能
const fs = require('fs');
const FormData = require('form-data');
const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));

const BASE_URL = 'http://localhost:3001';

async function testTemplateUpload() {
  console.log('🧪 测试模板上传功能...');
  
  try {
    // 检查测试文件是否存在
    const templateFile = '完整测试批量时间生成模板.xlsx';
    if (!fs.existsSync(templateFile)) {
      throw new Error(`测试文件不存在: ${templateFile}`);
    }

    // 创建FormData
    const formData = new FormData();
    formData.append('file', fs.createReadStream(templateFile));

    // 上传模板
    const response = await fetch(`${BASE_URL}/api/templates/upload`, {
      method: 'POST',
      body: formData
    });

    const result = await response.json();
    
    if (result.success) {
      console.log('✅ 模板上传成功');
      console.log(`   - 解析到 ${result.data.products.length} 个商品ID`);
      console.log(`   - 文件名: ${result.data.originalName}`);
      console.log(`   - 配置数量: ${result.data.configs.length}`);
      
      // 显示解析的商品ID
      console.log('   - 商品ID列表:', result.data.products.slice(0, 5).join(', ') + 
                  (result.data.products.length > 5 ? '...' : ''));
      
      return result.data;
    } else {
      throw new Error(result.error || '上传失败');
    }
  } catch (error) {
    console.error('❌ 模板上传失败:', error.message);
    return null;
  }
}

async function testBatchExport(templateData) {
  console.log('\n🧪 测试批量导出功能...');
  
  try {
    if (!templateData || !templateData.products) {
      throw new Error('没有有效的模板数据');
    }

    // 准备导出配置
    const exportConfig = {
      productIds: templateData.products.slice(0, 5), // 只测试前5个商品
      configs: {
        activityType: '多件多折',
        timeMode: '连续',
        startDate: '2024-01-15',
        endDate: '2024-01-21',
        dailyStartTime: '09:00:00',
        dailyEndTime: '21:00:00',
        region: '全国',
        minQuantity: 2,
        discount: 8,
        weekendFullDay: true,
        holidayFullDay: true
      }
    };

    console.log(`   - 测试商品数量: ${exportConfig.productIds.length}`);
    console.log(`   - 活动日期: ${exportConfig.configs.startDate} 到 ${exportConfig.configs.endDate}`);

    // 调用批量导出API
    const response = await fetch(`${BASE_URL}/api/batch/export`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(exportConfig)
    });

    if (response.ok) {
      // 保存导出的Excel文件
      const buffer = await response.arrayBuffer();
      const filename = `测试导出结果_${new Date().toISOString().slice(0, 19).replace(/[:-]/g, '')}.xlsx`;
      fs.writeFileSync(filename, Buffer.from(buffer));
      
      console.log('✅ 批量导出成功');
      console.log(`   - 导出文件: ${filename}`);
      console.log(`   - 文件大小: ${(buffer.byteLength / 1024).toFixed(2)} KB`);
      
      return filename;
    } else {
      const errorResult = await response.json();
      throw new Error(errorResult.error || '导出失败');
    }
  } catch (error) {
    console.error('❌ 批量导出失败:', error.message);
    return null;
  }
}

async function validateExportedFile(filename) {
  console.log('\n🧪 验证导出文件...');
  
  try {
    if (!filename || !fs.existsSync(filename)) {
      throw new Error('导出文件不存在');
    }

    const XLSX = require('xlsx');
    const workbook = XLSX.readFile(filename);
    
    console.log('✅ 文件验证成功');
    console.log(`   - 工作表数量: ${workbook.SheetNames.length}`);
    console.log(`   - 工作表名称: ${workbook.SheetNames.join(', ')}`);
    
    // 验证活动时间表工作表
    if (workbook.SheetNames.includes('活动时间表')) {
      const worksheet = workbook.Sheets['活动时间表'];
      const jsonData = XLSX.utils.sheet_to_json(worksheet);
      
      console.log(`   - 活动时间表记录数: ${jsonData.length}`);
      
      if (jsonData.length > 0) {
        console.log('   - 示例记录:', {
          商品ID: jsonData[0]['商品ID'],
          活动日期: jsonData[0]['活动日期'],
          开始时间: jsonData[0]['开始时间'],
          结束时间: jsonData[0]['结束时间']
        });
      }
    }
    
    // 验证统计汇总工作表
    if (workbook.SheetNames.includes('统计汇总')) {
      const summaryWorksheet = workbook.Sheets['统计汇总'];
      const summaryData = XLSX.utils.sheet_to_json(summaryWorksheet);
      
      console.log(`   - 统计汇总记录数: ${summaryData.length}`);
      
      if (summaryData.length > 0) {
        console.log('   - 统计信息:');
        summaryData.forEach(item => {
          console.log(`     ${item['统计项']}: ${item['数值']}`);
        });
      }
    }
    
    return true;
  } catch (error) {
    console.error('❌ 文件验证失败:', error.message);
    return false;
  }
}

async function runTests() {
  console.log('🚀 开始批量导出功能测试\n');
  
  // 测试1: 模板上传
  const templateData = await testTemplateUpload();
  if (!templateData) {
    console.log('\n❌ 测试失败: 模板上传失败');
    return;
  }
  
  // 测试2: 批量导出
  const exportedFile = await testBatchExport(templateData);
  if (!exportedFile) {
    console.log('\n❌ 测试失败: 批量导出失败');
    return;
  }
  
  // 测试3: 文件验证
  const isValid = await validateExportedFile(exportedFile);
  if (!isValid) {
    console.log('\n❌ 测试失败: 导出文件验证失败');
    return;
  }
  
  console.log('\n🎉 所有测试通过！');
  console.log('\n📋 测试总结:');
  console.log('✅ 模板上传功能正常');
  console.log('✅ 批量导出功能正常');
  console.log('✅ 导出文件格式正确');
  console.log('✅ 数据完整性验证通过');
  
  console.log('\n📁 生成的文件:');
  console.log(`   - 测试模板: 完整测试批量时间生成模板.xlsx`);
  console.log(`   - 导出结果: ${exportedFile}`);
}

// 运行测试
runTests().catch(error => {
  console.error('💥 测试运行失败:', error);
  process.exit(1);
});
