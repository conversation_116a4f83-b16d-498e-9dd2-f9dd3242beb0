// 创建90天活动的Excel模板
const XLSX = require('xlsx');
const path = require('path');

function create90DayTemplate() {
  console.log('🔧 创建90天活动模板...');
  
  // 计算90天后的日期
  const startDate = new Date('2025-08-05');
  const endDate = new Date(startDate);
  endDate.setDate(startDate.getDate() + 89); // 90天后 (包含开始日期)
  
  const formatDate = (date) => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  };
  
  console.log(`📅 开始日期: ${formatDate(startDate)}`);
  console.log(`📅 结束日期: ${formatDate(endDate)}`);
  console.log(`📊 总天数: 90天`);
  
  // 表头
  const headers = [
    '商品ID',
    '活动区域', 
    '满几件',
    '折扣',
    '时间模式',
    '开始日期',
    '结束日期',
    '每日开始时间',
    '每日结束时间',
    '周末全天',
    '节假日全天',
    '时间段数量',
    '时段时长',
    '备注'
  ];
  
  // 示例数据 - 90天连续活动
  const data = [
    [
      '709107945753',           // 商品ID
      '全国',                   // 活动区域
      '1',                      // 满几件
      '7.5',                    // 折扣
      '连续',                   // 时间模式
      formatDate(startDate),    // 开始日期
      formatDate(endDate),      // 结束日期 (90天后)
      '19:00:00',              // 每日开始时间
      '08:00:00',              // 每日结束时间 (跨天)
      '是',                     // 周末全天
      '是',                     // 节假日全天
      '5',                      // 时间段数量 (连续模式不使用)
      '1',                      // 时段时长 (连续模式不使用)
      '90天连续活动示例'        // 备注
    ]
  ];
  
  // 创建工作簿
  const workbook = XLSX.utils.book_new();
  
  // 创建工作表
  const worksheet = XLSX.utils.aoa_to_sheet([headers, ...data]);
  
  // 设置列宽
  const colWidths = [
    { wch: 15 }, // 商品ID
    { wch: 12 }, // 活动区域
    { wch: 8 },  // 满几件
    { wch: 8 },  // 折扣
    { wch: 10 }, // 时间模式
    { wch: 12 }, // 开始日期
    { wch: 12 }, // 结束日期
    { wch: 15 }, // 每日开始时间
    { wch: 15 }, // 每日结束时间
    { wch: 10 }, // 周末全天
    { wch: 10 }, // 节假日全天
    { wch: 12 }, // 时间段数量
    { wch: 10 }, // 时段时长
    { wch: 20 }  // 备注
  ];
  
  worksheet['!cols'] = colWidths;
  
  // 添加工作表到工作簿
  XLSX.utils.book_append_sheet(workbook, worksheet, '90天活动模板');
  
  // 保存文件
  const filename = '90天连续活动模板.xlsx';
  XLSX.writeFile(workbook, filename);
  
  console.log(`✅ 模板创建成功: ${filename}`);
  console.log('');
  console.log('📋 模板说明:');
  console.log('- 连续模式: 从2025-08-05到2025-11-02，共90天');
  console.log('- 每日时间: 19:00:00到次日08:00:00 (跨天活动)');
  console.log('- 周末和节假日: 全天活动');
  console.log('- 预期生成: 90个时间段 (每天一个)');
  console.log('');
  console.log('🎯 使用方法:');
  console.log('1. 上传此模板到批量生成页面');
  console.log('2. 点击"生成活动时间列表"');
  console.log('3. 到任务管理页面查看进度');
  console.log('4. 导出Excel文件，应包含90行活动时间数据');
}

// 运行创建
create90DayTemplate();
