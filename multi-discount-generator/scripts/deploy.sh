#!/bin/bash

# 部署脚本
# 用于自动化部署多折扣模板生成器

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查必要的工具
check_prerequisites() {
    log "检查部署前置条件..."
    
    # 检查Docker
    if ! command -v docker &> /dev/null; then
        error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    # 检查Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        error "Docker Compose未安装，请先安装Docker Compose"
        exit 1
    fi
    
    # 检查Node.js
    if ! command -v node &> /dev/null; then
        error "Node.js未安装，请先安装Node.js"
        exit 1
    fi
    
    success "前置条件检查通过"
}

# 环境配置
setup_environment() {
    log "设置部署环境..."
    
    # 创建必要的目录
    mkdir -p data uploads backups logs ssl
    
    # 设置权限
    chmod 755 data uploads backups logs
    
    # 创建环境变量文件（如果不存在）
    if [ ! -f .env.production ]; then
        log "创建生产环境配置文件..."
        cat > .env.production << EOF
NODE_ENV=production
DATABASE_URL=file:./data/production.db
NEXT_PUBLIC_APP_URL=http://localhost:3000
MAX_FILE_SIZE=10485760
UPLOAD_DIR=./uploads
ENABLE_PERFORMANCE_MONITORING=true
LOG_RETENTION_DAYS=30
REDIS_URL=redis://redis:6379
EOF
        warning "请检查并修改 .env.production 文件中的配置"
    fi
    
    success "环境设置完成"
}

# 构建应用
build_application() {
    log "构建应用..."
    
    # 安装依赖
    log "安装依赖..."
    npm ci --only=production
    
    # 生成Prisma客户端
    log "生成Prisma客户端..."
    npx prisma generate
    
    # 构建Next.js应用
    log "构建Next.js应用..."
    npm run build
    
    success "应用构建完成"
}

# 数据库迁移
migrate_database() {
    log "执行数据库迁移..."
    
    # 推送数据库schema
    npx prisma db push --force-reset
    
    # 执行种子数据
    if [ -f prisma/seed.ts ]; then
        log "执行种子数据..."
        npx prisma db seed
    fi
    
    success "数据库迁移完成"
}

# 启动服务
start_services() {
    log "启动服务..."
    
    # 停止现有服务
    docker-compose down
    
    # 构建并启动服务
    docker-compose up -d --build
    
    # 等待服务启动
    log "等待服务启动..."
    sleep 30
    
    # 检查服务状态
    check_services_health
    
    success "服务启动完成"
}

# 检查服务健康状态
check_services_health() {
    log "检查服务健康状态..."
    
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f http://localhost:3000/api/health > /dev/null 2>&1; then
            success "应用服务健康检查通过"
            break
        fi
        
        log "等待应用服务启动... (尝试 $attempt/$max_attempts)"
        sleep 10
        ((attempt++))
    done
    
    if [ $attempt -gt $max_attempts ]; then
        error "应用服务健康检查失败"
        docker-compose logs app
        exit 1
    fi
    
    # 检查其他服务
    if docker-compose ps | grep -q "Up"; then
        success "所有服务运行正常"
        docker-compose ps
    else
        error "部分服务启动失败"
        docker-compose ps
        exit 1
    fi
}

# 运行测试
run_tests() {
    log "运行部署后测试..."
    
    # 健康检查测试
    if curl -f http://localhost:3000/api/health; then
        success "健康检查测试通过"
    else
        error "健康检查测试失败"
        return 1
    fi
    
    # API测试
    if curl -f http://localhost:3000/api/stats; then
        success "API测试通过"
    else
        error "API测试失败"
        return 1
    fi
    
    success "部署后测试完成"
}

# 备份数据
backup_data() {
    log "备份现有数据..."
    
    local backup_dir="backups/$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$backup_dir"
    
    # 备份数据库
    if [ -f data/production.db ]; then
        cp data/production.db "$backup_dir/"
        success "数据库备份完成"
    fi
    
    # 备份上传文件
    if [ -d uploads ] && [ "$(ls -A uploads)" ]; then
        cp -r uploads "$backup_dir/"
        success "文件备份完成"
    fi
    
    success "数据备份完成: $backup_dir"
}

# 清理旧备份
cleanup_old_backups() {
    log "清理旧备份..."
    
    # 保留最近7天的备份
    find backups -type d -mtime +7 -exec rm -rf {} + 2>/dev/null || true
    
    success "旧备份清理完成"
}

# 显示部署信息
show_deployment_info() {
    echo ""
    echo "=========================================="
    echo "🎉 部署完成！"
    echo "=========================================="
    echo ""
    echo "📱 应用访问地址:"
    echo "   主页: http://localhost:3000"
    echo "   管理后台: http://localhost:3000/admin"
    echo "   批量处理: http://localhost:3000/batch"
    echo "   任务管理: http://localhost:3000/tasks"
    echo ""
    echo "📊 监控面板:"
    echo "   系统监控: http://localhost:3000/admin/monitor"
    echo "   日志查看: http://localhost:3000/admin/logs"
    echo "   Grafana: http://localhost:3001 (admin/admin123)"
    echo "   Prometheus: http://localhost:9090"
    echo ""
    echo "🔧 管理命令:"
    echo "   查看服务状态: docker-compose ps"
    echo "   查看日志: docker-compose logs -f app"
    echo "   重启服务: docker-compose restart"
    echo "   停止服务: docker-compose down"
    echo ""
    echo "📋 API端点:"
    echo "   健康检查: http://localhost:3000/api/health"
    echo "   系统统计: http://localhost:3000/api/stats"
    echo "   任务管理: http://localhost:3000/api/tasks"
    echo ""
    echo "🔍 故障排查:"
    echo "   如果服务无法访问，请检查防火墙设置"
    echo "   如果遇到权限问题，请检查文件夹权限"
    echo "   详细日志: docker-compose logs"
    echo ""
}

# 主函数
main() {
    local mode=${1:-"full"}
    
    echo "=========================================="
    echo "🚀 多折扣模板生成器部署脚本"
    echo "=========================================="
    echo ""
    
    case $mode in
        "check")
            check_prerequisites
            ;;
        "build")
            check_prerequisites
            build_application
            ;;
        "migrate")
            migrate_database
            ;;
        "start")
            start_services
            ;;
        "test")
            run_tests
            ;;
        "backup")
            backup_data
            ;;
        "full")
            check_prerequisites
            backup_data
            setup_environment
            build_application
            migrate_database
            start_services
            run_tests
            cleanup_old_backups
            show_deployment_info
            ;;
        *)
            echo "用法: $0 [check|build|migrate|start|test|backup|full]"
            echo ""
            echo "选项:"
            echo "  check   - 检查部署前置条件"
            echo "  build   - 构建应用"
            echo "  migrate - 执行数据库迁移"
            echo "  start   - 启动服务"
            echo "  test    - 运行部署后测试"
            echo "  backup  - 备份数据"
            echo "  full    - 完整部署流程（默认）"
            exit 1
            ;;
    esac
}

# 错误处理
trap 'error "部署过程中发生错误，请检查日志"; exit 1' ERR

# 执行主函数
main "$@"