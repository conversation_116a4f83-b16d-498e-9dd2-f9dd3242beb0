#!/usr/bin/env ts-node

/**
 * 增强的测试运行脚本
 * 支持运行不同类型的测试：单元测试、集成测试、端到端测试等
 */

import { spawn } from 'child_process';
import path from 'path';
import fs from 'fs';

interface TestConfig {
  name: string;
  command: string;
  args: string[];
  timeout?: number;
  env?: Record<string, string>;
  description?: string;
}

const testConfigs: Record<string, TestConfig> = {
  unit: {
    name: '单元测试',
    description: '测试单个函数和组件的功能',
    command: 'jest',
    args: ['--selectProjects', 'unit', '--coverage'],
    timeout: 60000
  },
  integration: {
    name: '集成测试',
    description: '测试模块间的交互和API功能',
    command: 'jest',
    args: ['--selectProjects', 'integration', '--runInBand'],
    timeout: 120000
  },
  e2e: {
    name: '端到端测试',
    description: '测试完整的用户工作流',
    command: 'jest',
    args: ['--selectProjects', 'e2e', '--runInBand'],
    timeout: 300000
  },
  all: {
    name: '所有测试',
    description: '运行所有类型的测试',
    command: 'jest',
    args: ['--selectProjects', 'unit', 'integration', 'e2e', '--coverage'],
    timeout: 600000
  },
  watch: {
    name: '监视模式',
    description: '监视文件变化并自动运行测试',
    command: 'jest',
    args: ['--watch', '--selectProjects', 'unit'],
    timeout: 0 // 无超时
  },
  coverage: {
    name: '覆盖率测试',
    description: '生成详细的代码覆盖率报告',
    command: 'jest',
    args: ['--coverage', '--coverageReporters=text', '--coverageReporters=html', '--coverageReporters=lcov'],
    timeout: 180000
  }
};

async function runTest(config: TestConfig): Promise<boolean> {
  return new Promise((resolve) => {
    console.log(`🧪 开始运行${config.name}...`);
    if (config.description) {
      console.log(`📝 ${config.description}`);
    }
    console.log(`🔧 命令: ${config.command} ${config.args.join(' ')}`);
    console.log('');

    const child = spawn(config.command, config.args, {
      stdio: 'inherit',
      env: { ...process.env, ...config.env },
      cwd: process.cwd()
    });

    const timeout = config.timeout && config.timeout > 0 ? setTimeout(() => {
      console.log(`⏰ ${config.name}超时，强制终止`);
      child.kill('SIGTERM');
      resolve(false);
    }, config.timeout) : null;

    child.on('close', (code) => {
      if (timeout) clearTimeout(timeout);

      if (code === 0) {
        console.log(`✅ ${config.name}通过`);
        resolve(true);
      } else {
        console.log(`❌ ${config.name}失败 (退出码: ${code})`);
        resolve(false);
      }
    });

    child.on('error', (error) => {
      if (timeout) clearTimeout(timeout);
      console.error(`❌ ${config.name}执行错误:`, error);
      resolve(false);
    });
  });
}

function printUsage() {
  console.log('🧪 测试运行器使用说明');
  console.log('');
  console.log('用法: npm run test:script [测试类型] [选项]');
  console.log('');
  console.log('可用的测试类型:');
  Object.entries(testConfigs).forEach(([type, config]) => {
    console.log(`  ${type.padEnd(12)} - ${config.description || config.name}`);
  });
  console.log('');
  console.log('选项:');
  console.log('  --help, -h     显示此帮助信息');
  console.log('  --list, -l     列出所有可用的测试类型');
  console.log('  --verbose, -v  显示详细输出');
  console.log('');
  console.log('示例:');
  console.log('  npm run test:script unit');
  console.log('  npm run test:script integration');
  console.log('  npm run test:script all');
}

async function checkPrerequisites(): Promise<boolean> {
  // 检查必要的文件和目录
  const requiredPaths = [
    'tests',
    'jest.config.js',
    'package.json'
  ];

  for (const requiredPath of requiredPaths) {
    if (!fs.existsSync(requiredPath)) {
      console.error(`❌ 缺少必要的文件或目录: ${requiredPath}`);
      return false;
    }
  }

  // 检查测试目录结构
  const testDirs = ['tests/unit', 'tests/integration', 'tests/e2e'];
  const missingDirs = testDirs.filter(dir => !fs.existsSync(dir));

  if (missingDirs.length > 0) {
    console.warn(`⚠️ 缺少测试目录: ${missingDirs.join(', ')}`);
    console.warn('某些测试类型可能无法运行');
  }

  return true;
}

async function main() {
  const args = process.argv.slice(2);

  // 处理帮助选项
  if (args.includes('--help') || args.includes('-h')) {
    printUsage();
    return;
  }

  // 处理列表选项
  if (args.includes('--list') || args.includes('-l')) {
    console.log('可用的测试类型:');
    Object.keys(testConfigs).forEach(type => {
      console.log(`  - ${type}`);
    });
    return;
  }

  const testType = args[0] || 'unit';
  const verbose = args.includes('--verbose') || args.includes('-v');

  if (verbose) {
    process.env.VERBOSE_TESTS = 'true';
  }

  console.log('🚀 测试运行器启动');
  console.log(`📋 测试类型: ${testType}`);
  console.log(`🔍 详细模式: ${verbose ? '开启' : '关闭'}`);
  console.log('');

  // 检查先决条件
  const prerequisitesOk = await checkPrerequisites();
  if (!prerequisitesOk) {
    console.error('❌ 先决条件检查失败');
    process.exit(1);
  }

  // 获取测试配置
  const config = testConfigs[testType];

  if (!config) {
    console.error(`❌ 未知的测试类型: ${testType}`);
    console.log('');
    printUsage();
    process.exit(1);
  }

  // 运行测试
  const startTime = Date.now();
  const result = await runTest(config);
  const duration = Date.now() - startTime;

  console.log('');
  console.log('📊 测试总结');
  console.log(`⏱️ 运行时间: ${(duration / 1000).toFixed(2)}秒`);
  console.log(`📋 测试类型: ${config.name}`);
  console.log(`🎯 结果: ${result ? '✅ 通过' : '❌ 失败'}`);

  process.exit(result ? 0 : 1);
}

if (require.main === module) {
  main().catch(error => {
    console.error('❌ 测试运行器错误:', error);
    process.exit(1);
  });
}