// 创建国庆节测试模板
const XLSX = require('xlsx');

function createHolidayTestTemplate() {
  console.log('🔧 创建国庆节测试模板...');
  
  // 测试2025年国庆节前后的时间处理
  // 2025年国庆节：10月1日-10月3日（周三-周五）
  const startDate = '2025-09-29'; // 周一
  const endDate = '2025-10-05';   // 周日，包含国庆节
  
  console.log(`📅 测试时间范围: ${startDate} 到 ${endDate}`);
  console.log('📊 包含时间段:');
  console.log('  - 2025-09-29 (周一) - 工作日');
  console.log('  - 2025-09-30 (周二) - 工作日，但下一天是国庆节');
  console.log('  - 2025-10-01 (周三) - 国庆节 🎉');
  console.log('  - 2025-10-02 (周四) - 国庆节 🎉');
  console.log('  - 2025-10-03 (周五) - 国庆节 🎉');
  console.log('  - 2025-10-04 (周六) - 周末');
  console.log('  - 2025-10-05 (周日) - 周末');
  console.log('');
  
  // 表头
  const headers = [
    '商品ID',
    '活动区域', 
    '满几件',
    '折扣',
    '时间模式',
    '开始日期',
    '结束日期',
    '每日开始时间',
    '每日结束时间',
    '周末全天',
    '节假日全天',
    '时间段数量',
    '时段时长',
    '备注'
  ];
  
  // 示例数据 - 跨天活动，包含国庆节
  const data = [
    [
      'HOLIDAY_TEST_001',       // 商品ID
      '全国',                   // 活动区域
      '1',                      // 满几件
      '7.5',                    // 折扣
      '连续',                   // 时间模式
      startDate,                // 开始日期
      endDate,                  // 结束日期
      '19:00:00',              // 每日开始时间
      '08:00:00',              // 每日结束时间 (跨天到次日8点)
      '是',                     // 周末全天
      '是',                     // 节假日全天
      '5',                      // 时间段数量 (连续模式不使用)
      '1',                      // 时段时长 (连续模式不使用)
      '国庆节时间处理测试'      // 备注
    ]
  ];
  
  // 创建工作簿
  const workbook = XLSX.utils.book_new();
  
  // 创建工作表
  const worksheet = XLSX.utils.aoa_to_sheet([headers, ...data]);
  
  // 设置列宽
  const colWidths = [
    { wch: 18 }, // 商品ID
    { wch: 12 }, // 活动区域
    { wch: 8 },  // 满几件
    { wch: 8 },  // 折扣
    { wch: 10 }, // 时间模式
    { wch: 12 }, // 开始日期
    { wch: 12 }, // 结束日期
    { wch: 15 }, // 每日开始时间
    { wch: 15 }, // 每日结束时间
    { wch: 10 }, // 周末全天
    { wch: 10 }, // 节假日全天
    { wch: 12 }, // 时间段数量
    { wch: 10 }, // 时段时长
    { wch: 20 }  // 备注
  ];
  
  worksheet['!cols'] = colWidths;
  
  // 添加工作表到工作簿
  XLSX.utils.book_append_sheet(workbook, worksheet, '国庆节测试');
  
  // 保存文件
  const filename = '国庆节时间处理测试.xlsx';
  XLSX.writeFile(workbook, filename);
  
  console.log(`✅ 测试模板创建成功: ${filename}`);
  console.log('');
  console.log('🎯 预期结果:');
  console.log('1. 9/29 19:00 - 9/30 08:00 (正常跨天)');
  console.log('2. 9/30 19:00 - 9/30 23:59 (截断，因为下一天是国庆节)');
  console.log('3. 10/1 00:00 - 10/1 23:59 (国庆节全天)');
  console.log('4. 10/2 00:00 - 10/2 23:59 (国庆节全天)');
  console.log('5. 10/3 00:00 - 10/3 23:59 (国庆节全天)');
  console.log('6. 10/4 00:00 - 10/4 23:59 (周六全天)');
  console.log('7. 10/5 00:00 - 10/6 08:00 (周日到周一)');
  console.log('');
  console.log('🔍 关键检查点:');
  console.log('- 9/30的时间段应该截断到23:59:59，不能延续到10/1');
  console.log('- 10/1-10/3应该是全天活动(00:00-23:59)');
  console.log('- 不应该出现时间重叠');
}

// 运行创建
createHolidayTestTemplate();
