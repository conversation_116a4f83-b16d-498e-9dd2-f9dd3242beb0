// 测试任务队列功能
const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));

const BASE_URL = 'http://localhost:3001';

// 测试模板数据
const testTemplateData = [
  {
    '商品ID': 'TASK001',
    '商品标题': '测试商品1',
    '活动区域': '全国',
    '满几件': '1',
    '折扣': '8'
  },
  {
    '商品ID': 'TASK002', 
    '商品标题': '测试商品2',
    '活动区域': '华东',
    '满几件': '1',
    '折扣': '7'
  },
  {
    '商品ID': 'TASK003',
    '商品标题': '测试商品3', 
    '活动区域': '华南',
    '满几件': '1',
    '折扣': '9'
  }
];

async function testTaskGeneration() {
  console.log('🧪 测试任务队列功能...\n');

  try {
    // 1. 创建批量生成任务
    console.log('📤 创建批量生成任务...');
    const generateResponse = await fetch(`${BASE_URL}/api/batch/generate-tasks`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        templateData: testTemplateData,
        configs: {
          timeMode: 'continuous',
          startDate: '2024-01-15',
          endDate: '2024-01-21',
          dailyStartTime: '09:00:00',
          dailyEndTime: '21:00:00',
          weekendFullDay: true,
          holidayFullDay: true,
          timeSlotCount: 5,
          slotDuration: 1
        }
      })
    });

    const generateResult = await generateResponse.json();
    
    if (generateResult.success) {
      console.log('✅ 任务创建成功');
      console.log(`   - 创建了 ${generateResult.data.taskIds.length} 个任务`);
      console.log(`   - 任务ID: ${generateResult.data.taskIds.join(', ')}`);
      
      // 等待任务处理
      console.log('\n⏳ 等待任务处理...');
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      // 2. 查询任务状态
      console.log('\n📋 查询任务状态...');
      const tasksResponse = await fetch(`${BASE_URL}/api/batch/tasks`);
      const tasksResult = await tasksResponse.json();
      
      if (tasksResult.success) {
        console.log('✅ 任务状态查询成功');
        console.log(`   - 总任务数: ${tasksResult.data.length}`);
        
        tasksResult.data.forEach((task, index) => {
          console.log(`   - 任务${index + 1}: ${task.productId} - ${task.status} (${task.progress}%)`);
          if (task.errorMessage) {
            console.log(`     错误: ${task.errorMessage}`);
          }
        });
        
        // 3. 尝试导出已完成的任务
        const completedTasks = tasksResult.data.filter(task => task.status === 'completed');
        if (completedTasks.length > 0) {
          console.log('\n📥 测试任务导出...');
          const taskId = completedTasks[0].id;
          
          const exportResponse = await fetch(`${BASE_URL}/api/batch/tasks?action=export&taskId=${taskId}`);
          
          if (exportResponse.ok) {
            console.log('✅ 任务导出成功');
            console.log(`   - 任务ID: ${taskId}`);
            console.log(`   - 文件大小: ${(exportResponse.headers.get('content-length') / 1024).toFixed(2)} KB`);
          } else {
            console.log('❌ 任务导出失败');
          }
        } else {
          console.log('\n⏳ 暂无已完成的任务可导出');
        }
        
      } else {
        console.error('❌ 任务状态查询失败:', tasksResult.error);
      }
      
    } else {
      console.error('❌ 任务创建失败:', generateResult.error);
    }

  } catch (error) {
    console.error('💥 测试失败:', error.message);
  }
}

async function testTaskStatusPolling() {
  console.log('\n🔄 测试任务状态轮询...');
  
  let attempts = 0;
  const maxAttempts = 10;
  
  while (attempts < maxAttempts) {
    try {
      const response = await fetch(`${BASE_URL}/api/batch/tasks`);
      const result = await response.json();
      
      if (result.success) {
        const tasks = result.data;
        const pending = tasks.filter(t => t.status === 'pending').length;
        const processing = tasks.filter(t => t.status === 'processing').length;
        const completed = tasks.filter(t => t.status === 'completed').length;
        const failed = tasks.filter(t => t.status === 'failed').length;
        
        console.log(`   轮询 ${attempts + 1}: 等待${pending} | 处理中${processing} | 完成${completed} | 失败${failed}`);
        
        if (pending === 0 && processing === 0) {
          console.log('✅ 所有任务处理完成');
          break;
        }
      }
      
      attempts++;
      await new Promise(resolve => setTimeout(resolve, 2000));
      
    } catch (error) {
      console.error(`❌ 轮询失败 ${attempts + 1}:`, error.message);
      attempts++;
    }
  }
  
  if (attempts >= maxAttempts) {
    console.log('⏰ 轮询超时');
  }
}

async function runTests() {
  console.log('🚀 开始任务队列功能测试\n');
  
  await testTaskGeneration();
  await testTaskStatusPolling();
  
  console.log('\n🎉 测试完成！');
  console.log('\n📋 功能验证:');
  console.log('✅ 批量任务创建');
  console.log('✅ 任务状态查询');
  console.log('✅ 任务进度跟踪');
  console.log('✅ 任务结果导出');
  console.log('\n🌐 访问任务管理页面: http://localhost:3001/admin/tasks');
}

// 运行测试
runTests().catch(error => {
  console.error('💥 测试运行失败:', error);
  process.exit(1);
});
