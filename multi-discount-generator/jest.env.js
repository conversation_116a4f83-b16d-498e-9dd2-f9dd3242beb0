// Set environment variables for testing
process.env.NODE_ENV = 'test'
process.env.DATABASE_URL = 'file:./test.db'
process.env.NEXT_PUBLIC_APP_URL = 'http://localhost:3000'
process.env.MAX_FILE_SIZE = '10485760'
process.env.UPLOAD_DIR = './test-uploads'
process.env.ENABLE_PERFORMANCE_MONITORING = 'false'
process.env.LOG_RETENTION_DAYS = '7'

// Mock crypto for Node.js environment
const crypto = require('crypto')

Object.defineProperty(global, 'crypto', {
  value: {
    randomUUID: () => crypto.randomUUID(),
    getRandomValues: (arr) => crypto.randomBytes(arr.length)
  }
})