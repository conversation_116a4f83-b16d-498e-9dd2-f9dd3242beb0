// 创建完整的测试Excel模板
const XLSX = require('xlsx');

// 创建测试数据 - 模拟真实的商品数据
const testData = [
  // 表头
  ['商品ID', '活动类型', '时间模式', '开始日期', '结束日期', '每日开始时间', '每日结束时间', '活动区域', '满几件', '折扣', '周末全天', '节假日全天'],
  
  // 测试数据 - 不同类型的商品
  ['TMALL001', '多件多折', '连续', '2024-01-15', '2024-01-21', '09:00:00', '21:00:00', '全国', '2', '8', '是', '是'],
  ['TMALL002', '多件多折', '连续', '2024-01-15', '2024-01-21', '10:00:00', '22:00:00', '全国', '3', '7', '否', '是'],
  ['TMALL003', '多件多折', '连续', '2024-01-15', '2024-01-21', '08:00:00', '20:00:00', '全国', '2', '9', '是', '否'],
  ['TMALL004', '多件多折', '连续', '2024-01-15', '2024-01-21', '09:30:00', '21:30:00', '全国', '4', '6', '否', '否'],
  ['TMALL005', '多件多折', '连续', '2024-01-15', '2024-01-21', '11:00:00', '23:00:00', '全国', '2', '8', '是', '是'],
  
  // 更多测试商品
  ['PROD001', '多件多折', '连续', '2024-01-15', '2024-01-21', '09:00:00', '21:00:00', '华东', '2', '8', '是', '是'],
  ['PROD002', '多件多折', '连续', '2024-01-15', '2024-01-21', '10:00:00', '22:00:00', '华南', '3', '7', '否', '是'],
  ['PROD003', '多件多折', '连续', '2024-01-15', '2024-01-21', '08:30:00', '20:30:00', '华北', '2', '9', '是', '否'],
  ['PROD004', '多件多折', '连续', '2024-01-15', '2024-01-21', '09:15:00', '21:15:00', '西南', '4', '6', '否', '否'],
  ['PROD005', '多件多折', '连续', '2024-01-15', '2024-01-21', '10:30:00', '22:30:00', '东北', '2', '8', '是', '是'],
  
  // 特殊配置的商品
  ['SPECIAL001', '多件多折', '连续', '2024-01-15', '2024-01-21', '00:00:00', '23:59:59', '全国', '1', '9', '是', '是'],
  ['SPECIAL002', '多件多折', '连续', '2024-01-15', '2024-01-21', '12:00:00', '18:00:00', '全国', '5', '5', '否', '否'],
];

// 创建工作簿
const workbook = XLSX.utils.book_new();
const worksheet = XLSX.utils.aoa_to_sheet(testData);

// 设置列宽
worksheet['!cols'] = [
  { wch: 15 }, // 商品ID
  { wch: 12 }, // 活动类型
  { wch: 10 }, // 时间模式
  { wch: 12 }, // 开始日期
  { wch: 12 }, // 结束日期
  { wch: 15 }, // 每日开始时间
  { wch: 15 }, // 每日结束时间
  { wch: 10 }, // 活动区域
  { wch: 8 },  // 满几件
  { wch: 8 },  // 折扣
  { wch: 10 }, // 周末全天
  { wch: 10 }  // 节假日全天
];

// 设置表头样式
const headerStyle = {
  font: { bold: true, color: { rgb: "FFFFFF" } },
  fill: { fgColor: { rgb: "4472C4" } },
  alignment: { horizontal: "center", vertical: "center" }
};

// 应用表头样式
const range = XLSX.utils.decode_range(worksheet['!ref']);
for (let col = range.s.c; col <= range.e.c; col++) {
  const cellAddress = XLSX.utils.encode_cell({ r: 0, c: col });
  if (!worksheet[cellAddress]) continue;
  worksheet[cellAddress].s = headerStyle;
}

// 添加工作表
XLSX.utils.book_append_sheet(workbook, worksheet, '批量时间生成模板');

// 创建说明工作表
const instructionData = [
  ['字段名称', '说明', '示例', '必填'],
  ['商品ID', '商品的唯一标识符', 'TMALL001, PROD001', '是'],
  ['活动类型', '活动的类型', '多件多折', '否'],
  ['时间模式', '时间安排模式', '连续', '否'],
  ['开始日期', '活动开始日期', '2024-01-15', '是'],
  ['结束日期', '活动结束日期', '2024-01-21', '是'],
  ['每日开始时间', '每天活动开始时间', '09:00:00', '否'],
  ['每日结束时间', '每天活动结束时间', '21:00:00', '否'],
  ['活动区域', '活动覆盖区域', '全国, 华东, 华南', '否'],
  ['满几件', '满足活动的最少件数', '2, 3, 4', '否'],
  ['折扣', '折扣力度(整数)', '8表示8折', '否'],
  ['周末全天', '周末是否全天活动', '是, 否', '否'],
  ['节假日全天', '节假日是否全天活动', '是, 否', '否']
];

const instructionWorksheet = XLSX.utils.aoa_to_sheet(instructionData);
instructionWorksheet['!cols'] = [
  { wch: 15 }, // 字段名称
  { wch: 30 }, // 说明
  { wch: 25 }, // 示例
  { wch: 8 }   // 必填
];

// 应用表头样式到说明工作表
const instrRange = XLSX.utils.decode_range(instructionWorksheet['!ref']);
for (let col = instrRange.s.c; col <= instrRange.e.c; col++) {
  const cellAddress = XLSX.utils.encode_cell({ r: 0, c: col });
  if (!instructionWorksheet[cellAddress]) continue;
  instructionWorksheet[cellAddress].s = headerStyle;
}

XLSX.utils.book_append_sheet(workbook, instructionWorksheet, '使用说明');

// 保存文件
const filename = '完整测试批量时间生成模板.xlsx';
XLSX.writeFile(workbook, filename);

console.log(`✅ 测试模板文件已创建: ${filename}`);
console.log('📊 包含数据:');
console.log(`   - ${testData.length - 1} 个测试商品`);
console.log('   - 完整的字段配置');
console.log('   - 使用说明工作表');
console.log('');
console.log('🔧 测试步骤:');
console.log('1. 启动开发服务器: npm run dev');
console.log('2. 访问: http://localhost:3002/batch/export');
console.log('3. 点击"上传模板"按钮');
console.log('4. 选择生成的Excel文件上传');
console.log('5. 配置活动参数');
console.log('6. 点击"生成活动时间表"');
console.log('7. 下载并查看生成的活动时间表格');
