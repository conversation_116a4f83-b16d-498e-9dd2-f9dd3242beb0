if(!self.define){let e,s={};const a=(a,c)=>(a=new URL(a+".js",c).href,s[a]||new Promise(s=>{if("document"in self){const e=document.createElement("script");e.src=a,e.onload=s,document.head.appendChild(e)}else e=a,importScripts(a),s()}).then(()=>{let e=s[a];if(!e)throw new Error(`Module ${a} didn’t register its module`);return e}));self.define=(c,n)=>{const t=e||("document"in self?document.currentScript.src:"")||location.href;if(s[t])return;let i={};const r=e=>a(e,t),f={module:{uri:t},exports:i,require:r};s[t]=Promise.all(c.map(e=>f[e]||r(e))).then(e=>(n(...e),i))}}define(["./workbox-4754cb34"],function(e){"use strict";importScripts(),self.skipWaiting(),e.clientsClaim(),e.precacheAndRoute([{url:"/_next/app-build-manifest.json",revision:"c751dc1639cd1d59fb6fb00a0ce87c4e"},{url:"/_next/dynamic-css-manifest.json",revision:"d751713988987e9331980363e24189ce"},{url:"/_next/static/1cCLre3UnycfDW3VETJnP/_buildManifest.js",revision:"b5fa51b9d9208442dc64b3dc544db76f"},{url:"/_next/static/1cCLre3UnycfDW3VETJnP/_ssgManifest.js",revision:"b6652df95db52feb4daf4eca35380933"},{url:"/_next/static/chunks/13b76428-a8cda74c313da51b.js",revision:"a8cda74c313da51b"},{url:"/_next/static/chunks/2170a4aa-46b3dc7aa3a02423.js",revision:"46b3dc7aa3a02423"},{url:"/_next/static/chunks/303-ff0a161ad5ae8aee.js",revision:"ff0a161ad5ae8aee"},{url:"/_next/static/chunks/4bd1b696-9911af18dede28aa.js",revision:"9911af18dede28aa"},{url:"/_next/static/chunks/605-6da2bebd9cc5dfc2.js",revision:"6da2bebd9cc5dfc2"},{url:"/_next/static/chunks/742-9acda9013c5c4bfc.js",revision:"9acda9013c5c4bfc"},{url:"/_next/static/chunks/821-3a520224195fd155.js",revision:"3a520224195fd155"},{url:"/_next/static/chunks/89-e80d8579c174b068.js",revision:"e80d8579c174b068"},{url:"/_next/static/chunks/964-7a8026291cea917c.js",revision:"7a8026291cea917c"},{url:"/_next/static/chunks/99-e92a338ebf5bd403.js",revision:"e92a338ebf5bd403"},{url:"/_next/static/chunks/aaea2bcf-a5233d302085fc8d.js",revision:"a5233d302085fc8d"},{url:"/_next/static/chunks/app/_not-found/page-3bddce8920f53c74.js",revision:"3bddce8920f53c74"},{url:"/_next/static/chunks/app/admin/logs/page-8b37474dcbf47526.js",revision:"8b37474dcbf47526"},{url:"/_next/static/chunks/app/admin/monitor/page-b4c1e2d22a8c671d.js",revision:"b4c1e2d22a8c671d"},{url:"/_next/static/chunks/app/admin/page-4069ab3c809676db.js",revision:"4069ab3c809676db"},{url:"/_next/static/chunks/app/admin/tasks/page-2da4387bfd9f550f.js",revision:"2da4387bfd9f550f"},{url:"/_next/static/chunks/app/batch/page-8585ddbfac95b6e7.js",revision:"8585ddbfac95b6e7"},{url:"/_next/static/chunks/app/layout-e003eb79d4e89b2f.js",revision:"e003eb79d4e89b2f"},{url:"/_next/static/chunks/app/page-beb9c6b46456c346.js",revision:"beb9c6b46456c346"},{url:"/_next/static/chunks/app/tasks/%5Bid%5D/page-2f92efd73260b4e5.js",revision:"2f92efd73260b4e5"},{url:"/_next/static/chunks/app/tasks/page-820984117fae474f.js",revision:"820984117fae474f"},{url:"/_next/static/chunks/framework-65ef2e26b6c3f974.js",revision:"65ef2e26b6c3f974"},{url:"/_next/static/chunks/main-39439de1e727cf8a.js",revision:"39439de1e727cf8a"},{url:"/_next/static/chunks/main-app-9ef0a831255b4dd7.js",revision:"9ef0a831255b4dd7"},{url:"/_next/static/chunks/pages/_app-9a33c34d98c288d7.js",revision:"9a33c34d98c288d7"},{url:"/_next/static/chunks/pages/_error-f47c779418c1e822.js",revision:"f47c779418c1e822"},{url:"/_next/static/chunks/polyfills-42372ed130431b0a.js",revision:"846118c33b2c0e922d7b3a7676f81f6f"},{url:"/_next/static/chunks/webpack-c2d3fb149d81fbaa.js",revision:"c2d3fb149d81fbaa"},{url:"/_next/static/css/b6c3a1f9c567cf50.css",revision:"b6c3a1f9c567cf50"},{url:"/_next/static/media/569ce4b8f30dc480-s.p.woff2",revision:"ef6cefb32024deac234e82f932a95cbd"},{url:"/_next/static/media/747892c23ea88013-s.woff2",revision:"a0761690ccf4441ace5cec893b82d4ab"},{url:"/_next/static/media/8d697b304b401681-s.woff2",revision:"cc728f6c0adb04da0dfcb0fc436a8ae5"},{url:"/_next/static/media/93f479601ee12b01-s.p.woff2",revision:"da83d5f06d825c5ae65b7cca706cb312"},{url:"/_next/static/media/9610d9e46709d722-s.woff2",revision:"7b7c0ef93df188a852344fc272fc096b"},{url:"/_next/static/media/ba015fad6dcf6784-s.woff2",revision:"8ea4f719af3312a055caf09f34c89a77"},{url:"/file.svg",revision:"d09f95206c3fa0bb9bd9fefabfd0ea71"},{url:"/globe.svg",revision:"2aaafa6a49b6563925fe440891e32717"},{url:"/manifest.json",revision:"e5ba140d133b99daca11183333fdf5fa"},{url:"/next.svg",revision:"8e061864f388b47f33a1c3780831193e"},{url:"/vercel.svg",revision:"c0af2f507b369b085b35ef4bbe3bcf1e"},{url:"/window.svg",revision:"a2760511c65806022ad20adf74370ff3"}],{ignoreURLParametersMatching:[]}),e.cleanupOutdatedCaches(),e.registerRoute("/",new e.NetworkFirst({cacheName:"start-url",plugins:[{cacheWillUpdate:async({request:e,response:s,event:a,state:c})=>s&&"opaqueredirect"===s.type?new Response(s.body,{status:200,statusText:"OK",headers:s.headers}):s}]}),"GET"),e.registerRoute(/^https:\/\/fonts\.(?:gstatic)\.com\/.*/i,new e.CacheFirst({cacheName:"google-fonts-webfonts",plugins:[new e.ExpirationPlugin({maxEntries:4,maxAgeSeconds:31536e3})]}),"GET"),e.registerRoute(/^https:\/\/fonts\.(?:googleapis)\.com\/.*/i,new e.StaleWhileRevalidate({cacheName:"google-fonts-stylesheets",plugins:[new e.ExpirationPlugin({maxEntries:4,maxAgeSeconds:604800})]}),"GET"),e.registerRoute(/\.(?:eot|otf|ttc|ttf|woff|woff2|font.css)$/i,new e.StaleWhileRevalidate({cacheName:"static-font-assets",plugins:[new e.ExpirationPlugin({maxEntries:4,maxAgeSeconds:604800})]}),"GET"),e.registerRoute(/\.(?:jpg|jpeg|gif|png|svg|ico|webp)$/i,new e.StaleWhileRevalidate({cacheName:"static-image-assets",plugins:[new e.ExpirationPlugin({maxEntries:64,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\/_next\/image\?url=.+$/i,new e.StaleWhileRevalidate({cacheName:"next-image",plugins:[new e.ExpirationPlugin({maxEntries:64,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\.(?:mp3|wav|ogg)$/i,new e.CacheFirst({cacheName:"static-audio-assets",plugins:[new e.RangeRequestsPlugin,new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\.(?:mp4)$/i,new e.CacheFirst({cacheName:"static-video-assets",plugins:[new e.RangeRequestsPlugin,new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\.(?:js)$/i,new e.StaleWhileRevalidate({cacheName:"static-js-assets",plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\.(?:css|less)$/i,new e.StaleWhileRevalidate({cacheName:"static-style-assets",plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\/_next\/data\/.+\/.+\.json$/i,new e.StaleWhileRevalidate({cacheName:"next-data",plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\.(?:json|xml|csv)$/i,new e.NetworkFirst({cacheName:"static-data-assets",plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(({url:e})=>{if(!(self.origin===e.origin))return!1;const s=e.pathname;return!s.startsWith("/api/auth/")&&!!s.startsWith("/api/")},new e.NetworkFirst({cacheName:"apis",networkTimeoutSeconds:10,plugins:[new e.ExpirationPlugin({maxEntries:16,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(({url:e})=>{if(!(self.origin===e.origin))return!1;return!e.pathname.startsWith("/api/")},new e.NetworkFirst({cacheName:"others",networkTimeoutSeconds:10,plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(({url:e})=>!(self.origin===e.origin),new e.NetworkFirst({cacheName:"cross-origin",networkTimeoutSeconds:10,plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:3600})]}),"GET")});
