# 部署和运维指南

## 概述

本文档提供多折扣模板生成器的完整部署和运维指南，包括环境配置、部署流程、监控设置和故障排除。

## 系统要求

### 硬件要求
- **CPU**: 2核心以上
- **内存**: 4GB以上（推荐8GB）
- **存储**: 20GB以上可用空间
- **网络**: 稳定的网络连接

### 软件要求
- **操作系统**: Linux (Ubuntu 20.04+, CentOS 7+) 或 macOS
- **Docker**: 20.10+
- **Docker Compose**: 1.29+
- **Node.js**: 18+ (用于构建)
- **Git**: 2.0+

## 快速部署

### 1. 克隆项目
```bash
git clone <repository-url>
cd multi-discount-generator
```

### 2. 一键部署
```bash
./scripts/deploy.sh
```

部署完成后，系统将自动显示访问地址和管理信息。

## 详细部署流程

### 1. 环境准备

#### 安装Docker和Docker Compose
```bash
# Ubuntu/Debian
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# 添加用户到docker组
sudo usermod -aG docker $USER
```

#### 配置防火墙
```bash
# 开放必要端口
sudo ufw allow 80
sudo ufw allow 443
sudo ufw allow 3000
sudo ufw allow 3001  # Grafana
sudo ufw allow 9090  # Prometheus
```

### 2. 项目配置

#### 环境变量配置
复制并编辑环境配置文件：
```bash
cp .env.example .env.production
```

编辑 `.env.production`：
```env
NODE_ENV=production
DATABASE_URL=file:./data/production.db
NEXT_PUBLIC_APP_URL=https://your-domain.com
MAX_FILE_SIZE=10485760
UPLOAD_DIR=./uploads
ENABLE_PERFORMANCE_MONITORING=true
LOG_RETENTION_DAYS=30
REDIS_URL=redis://redis:6379
```

#### SSL证书配置（可选）
如果使用HTTPS，将证书文件放置在 `ssl/` 目录：
```bash
mkdir -p ssl
# 复制证书文件
cp your-cert.pem ssl/cert.pem
cp your-key.pem ssl/key.pem
```

### 3. 数据库初始化

#### 创建数据库
```bash
# 推送数据库schema
npx prisma db push

# 执行种子数据
npx prisma db seed
```

### 4. 服务启动

#### 使用Docker Compose启动
```bash
# 构建并启动所有服务
docker-compose up -d --build

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

#### 验证部署
```bash
# 健康检查
curl http://localhost:3000/api/health

# 系统统计
curl http://localhost:3000/api/stats
```

## 服务架构

### 核心服务
- **app**: 主应用服务 (端口3000)
- **nginx**: 反向代理和负载均衡 (端口80/443)
- **redis**: 缓存服务 (端口6379)

### 监控服务
- **prometheus**: 指标收集 (端口9090)
- **grafana**: 监控面板 (端口3001)
- **filebeat**: 日志收集

### 数据持久化
- **app_data**: 数据库文件
- **app_uploads**: 上传文件
- **app_backups**: 备份文件
- **app_logs**: 应用日志

## 访问地址

### 主要功能
- **主页**: http://localhost:3000
- **管理后台**: http://localhost:3000/admin
- **批量处理**: http://localhost:3000/batch
- **任务管理**: http://localhost:3000/tasks

### 监控面板
- **系统监控**: http://localhost:3000/admin/monitor
- **日志查看**: http://localhost:3000/admin/logs
- **Grafana**: http://localhost:3001 (admin/admin123)
- **Prometheus**: http://localhost:9090

### API端点
- **健康检查**: http://localhost:3000/api/health
- **系统统计**: http://localhost:3000/api/stats
- **任务API**: http://localhost:3000/api/tasks
- **文件API**: http://localhost:3000/api/upload

## 运维管理

### 日常维护

#### 查看服务状态
```bash
# 查看所有服务状态
docker-compose ps

# 查看特定服务日志
docker-compose logs -f app
docker-compose logs -f nginx
docker-compose logs -f redis
```

#### 重启服务
```bash
# 重启所有服务
docker-compose restart

# 重启特定服务
docker-compose restart app
docker-compose restart nginx
```

#### 更新应用
```bash
# 拉取最新代码
git pull origin main

# 重新构建并启动
docker-compose up -d --build
```

### 数据备份

#### 自动备份
系统会自动创建备份，备份文件位于 `backups/` 目录。

#### 手动备份
```bash
# 使用部署脚本备份
./scripts/deploy.sh backup

# 手动备份数据库
cp data/production.db backups/manual_$(date +%Y%m%d_%H%M%S).db

# 备份上传文件
tar -czf backups/uploads_$(date +%Y%m%d_%H%M%S).tar.gz uploads/
```

#### 数据恢复
```bash
# 停止服务
docker-compose down

# 恢复数据库
cp backups/backup_file.db data/production.db

# 恢复上传文件
tar -xzf backups/uploads_backup.tar.gz

# 重启服务
docker-compose up -d
```

### 监控和告警

#### 系统监控
- **CPU使用率**: 通过Grafana面板监控
- **内存使用**: 实时监控内存消耗
- **磁盘空间**: 监控存储使用情况
- **网络流量**: 监控网络I/O

#### 应用监控
- **任务队列**: 监控任务执行状态
- **API响应时间**: 监控接口性能
- **错误率**: 监控系统错误情况
- **用户活动**: 监控用户使用情况

#### 日志管理
```bash
# 查看应用日志
docker-compose logs -f app

# 查看Nginx访问日志
docker-compose exec nginx tail -f /var/log/nginx/access.log

# 查看系统日志
journalctl -u docker -f
```

### 性能优化

#### 数据库优化
```bash
# 数据库清理
npx prisma db execute --file=scripts/cleanup.sql

# 重建索引
npx prisma db execute --sql="REINDEX;"
```

#### 缓存优化
```bash
# 清理Redis缓存
docker-compose exec redis redis-cli FLUSHALL

# 查看缓存使用情况
docker-compose exec redis redis-cli INFO memory
```

#### 文件清理
```bash
# 清理临时文件
find temp/ -type f -mtime +1 -delete

# 清理旧日志
find logs/ -name "*.log" -mtime +30 -delete

# 清理旧备份
find backups/ -type f -mtime +7 -delete
```

## 故障排除

### 常见问题

#### 1. 服务无法启动
```bash
# 检查端口占用
netstat -tlnp | grep :3000

# 检查Docker状态
sudo systemctl status docker

# 查看详细错误日志
docker-compose logs app
```

#### 2. 数据库连接失败
```bash
# 检查数据库文件权限
ls -la data/

# 重新生成Prisma客户端
npx prisma generate

# 重置数据库
npx prisma db push --force-reset
```

#### 3. 文件上传失败
```bash
# 检查上传目录权限
ls -la uploads/

# 检查磁盘空间
df -h

# 检查Nginx配置
docker-compose exec nginx nginx -t
```

#### 4. 内存不足
```bash
# 查看内存使用
free -h
docker stats

# 重启服务释放内存
docker-compose restart

# 清理Docker缓存
docker system prune -f
```

### 性能问题诊断

#### 1. 响应时间慢
```bash
# 检查系统负载
top
htop

# 检查数据库性能
# 访问 /admin/monitor 查看数据库统计

# 检查网络延迟
ping localhost
```

#### 2. 高CPU使用率
```bash
# 查看进程CPU使用
docker stats

# 检查应用性能指标
curl http://localhost:3000/api/stats?type=performance
```

#### 3. 内存泄漏
```bash
# 监控内存使用趋势
# 访问 Grafana 面板查看内存使用图表

# 重启应用释放内存
docker-compose restart app
```

## 安全配置

### 基础安全
- 定期更新系统和Docker
- 使用非root用户运行服务
- 配置防火墙规则
- 启用HTTPS

### 应用安全
- 定期更新依赖包
- 配置访问控制
- 启用日志审计
- 实施备份策略

### 网络安全
- 使用反向代理
- 配置速率限制
- 启用CORS保护
- 监控异常访问

## 扩展部署

### 负载均衡
```yaml
# docker-compose.yml 中添加多个应用实例
app1:
  build: .
  ports:
    - "3001:3000"

app2:
  build: .
  ports:
    - "3002:3000"
```

### 数据库集群
考虑使用PostgreSQL或MySQL集群替代SQLite以支持高并发。

### 容器编排
对于大规模部署，考虑使用Kubernetes进行容器编排。

## 联系支持

如遇到部署或运维问题，请：
1. 查看本文档的故障排除部分
2. 检查系统日志和应用日志
3. 访问项目GitHub仓库提交Issue
4. 联系技术支持团队

---

**注意**: 本文档会随着系统更新而持续更新，请定期查看最新版本。