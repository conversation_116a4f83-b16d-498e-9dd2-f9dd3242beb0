// 创建正确格式的批量生成模板
const XLSX = require('xlsx');
const path = require('path');

// 正确的模板数据 - 与首页FormData结构完全一致
const testData = [
  // 表头 - 与TemplateService.createBatchTemplate()一致
  [
    '商品ID',           // productId
    '活动区域',         // region (全国/华中/西南/西北/华南/华东/华北)
    '满几件',           // minQuantity (1-10)
    '折扣',             // discount (如8表示8折)
    '时间模式',         // timeMode (连续/随机)
    '开始日期',         // startDate
    '结束日期',         // endDate  
    '每日开始时间',     // dailyStartTime
    '每日结束时间',     // dailyEndTime
    '周末全天',         // weekendFullDay
    '节假日全天',       // holidayFullDay
    '时间段数量',       // timeSlotCount (随机模式用)
    '时段时长',         // slotDuration (随机模式用，1或2小时)
    '备注'              // 可选
  ],
  // 数据行 - 示例数据
  ['TEST001', '全国', '1', '8', '连续', '2024-01-15', '2024-01-21', '09:00:00', '21:00:00', '是', '是', '5', '1', '测试商品1'],
  ['TEST002', '华东', '1', '7', '连续', '2024-01-15', '2024-01-21', '10:00:00', '20:00:00', '是', '否', '5', '1', '测试商品2'],
  ['TEST003', '华南', '1', '9', '连续', '2024-01-16', '2024-01-22', '08:00:00', '22:00:00', '否', '是', '5', '1', '测试商品3'],
  ['TEST004', '华北', '1', '8.5', '随机', '2024-01-15', '2024-01-21', '09:00:00', '21:00:00', '是', '是', '7', '2', '测试商品4'],
  ['TEST005', '华中', '1', '7.5', '连续', '2024-01-17', '2024-01-23', '11:00:00', '19:00:00', '否', '否', '5', '1', '测试商品5'],
  ['TEST006', '西南/西北', '1', '9', '随机', '2024-01-15', '2024-01-21', '09:00:00', '21:00:00', '是', '是', '6', '1', '测试商品6'],
  ['TEST007', '全国', '1', '8', '连续', '2024-01-18', '2024-01-24', '10:00:00', '20:00:00', '是', '否', '5', '1', '测试商品7'],
  ['TEST008', '华东', '1', '7', '连续', '2024-01-15', '2024-01-21', '09:00:00', '21:00:00', '否', '是', '5', '1', '测试商品8'],
  ['TEST009', '华南', '1', '8.5', '随机', '2024-01-16', '2024-01-22', '08:00:00', '22:00:00', '是', '是', '8', '2', '测试商品9'],
  ['TEST010', '华北', '1', '9', '连续', '2024-01-15', '2024-01-21', '09:00:00', '21:00:00', '是', '否', '5', '1', '测试商品10']
];

function createCorrectTemplate() {
  console.log('📝 创建正确格式的批量生成模板...');
  
  try {
    // 创建工作簿
    const workbook = XLSX.utils.book_new();
    
    // 创建工作表
    const worksheet = XLSX.utils.aoa_to_sheet(testData);
    
    // 设置列宽
    worksheet['!cols'] = [
      { wch: 15 }, // 商品ID
      { wch: 12 }, // 活动区域
      { wch: 10 }, // 满几件
      { wch: 8 },  // 折扣
      { wch: 10 }, // 时间模式
      { wch: 12 }, // 开始日期
      { wch: 12 }, // 结束日期
      { wch: 15 }, // 每日开始时间
      { wch: 15 }, // 每日结束时间
      { wch: 10 }, // 周末全天
      { wch: 12 }, // 节假日全天
      { wch: 12 }, // 时间段数量
      { wch: 10 }, // 时段时长
      { wch: 20 }  // 备注
    ];
    
    // 添加工作表到工作簿
    XLSX.utils.book_append_sheet(workbook, worksheet, '批量生成模板');
    
    // 保存文件
    const filename = '正确格式批量生成模板.xlsx';
    const filepath = path.join(__dirname, filename);
    XLSX.writeFile(workbook, filepath);
    
    console.log('✅ 正确格式模板创建成功');
    console.log(`   - 文件名: ${filename}`);
    console.log(`   - 文件路径: ${filepath}`);
    console.log(`   - 数据行数: ${testData.length - 1} 行`);
    console.log(`   - 商品数量: ${testData.length - 1} 个`);
    console.log('');
    console.log('📋 模板字段说明:');
    console.log('   - 商品ID: 必填，如TEST001');
    console.log('   - 活动区域: 必填，支持全国/华中/西南/西北/华南/华东/华北');
    console.log('   - 满几件: 必填，1-10的整数');
    console.log('   - 折扣: 必填，如8表示8折');
    console.log('   - 时间模式: 连续/随机');
    console.log('   - 开始日期/结束日期: YYYY-MM-DD格式');
    console.log('   - 每日时间: HH:mm:ss格式');
    console.log('   - 周末全天/节假日全天: 是/否');
    console.log('   - 时间段数量: 随机模式时的时间段数');
    console.log('   - 时段时长: 1或2小时');
    console.log('');
    console.log('🌐 使用方法:');
    console.log('1. 访问: http://localhost:3001/batch/export');
    console.log('2. 上传此Excel文件');
    console.log('3. 查看表格数据预览（支持分页）');
    console.log('4. 点击"生成活动时间表"');
    console.log('5. 到任务管理页面查看进度和导出结果');
    console.log('');
    console.log('📊 测试数据特点:');
    console.log('   - 包含不同活动区域');
    console.log('   - 包含连续和随机时间模式');
    console.log('   - 包含不同的折扣设置');
    console.log('   - 包含不同的时间配置');
    
  } catch (error) {
    console.error('❌ 创建模板失败:', error);
  }
}

// 运行创建函数
createCorrectTemplate();
