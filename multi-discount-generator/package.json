{"name": "multi-discount-generator", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "format": "prettier --write .", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:unit": "jest --selectProjects unit", "test:integration": "jest --selectProjects integration", "test:e2e": "jest --selectProjects e2e", "test:all": "jest --selectProjects unit integration e2e", "test:cleanup": "ts-node tests/tools/cleanup.ts", "test:cleanup:dry": "ts-node tests/tools/cleanup.ts --dry-run --verbose", "test:generate-templates": "ts-node -e \"import('./tests/tools/template-generator').then(m => m.TemplateGenerator.generateAllTestTemplates())\"", "test:debug-templates": "ts-node -e \"import('./tests/tools/template-debugger').then(m => m.TemplateDebugger.debugAllTemplates('tests/fixtures/templates'))\"", "test:script": "ts-node scripts/run-tests.ts", "db:seed": "npx prisma db seed", "db:reset": "npx prisma db push --force-reset && npm run db:seed"}, "dependencies": {"@headlessui/react": "^2.2.6", "@heroicons/react": "^2.2.0", "@prisma/client": "^6.13.0", "@types/multer": "^2.0.0", "@types/socket.io": "^3.0.1", "chinese-holidays": "^1.7.0", "clsx": "^2.1.1", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "form-data": "^4.0.4", "lucide-react": "^0.536.0", "multer": "^2.0.2", "next": "15.4.5", "next-pwa": "^5.6.0", "node-fetch": "^3.3.2", "react": "19.1.0", "react-dom": "19.1.0", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "xlsx": "^0.18.5", "zod": "^4.0.14"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.1.4", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.1", "@types/jest": "^29.5.8", "@types/node": "^20.19.9", "@types/react": "^19", "@types/react-dom": "^19", "babel-jest": "^29.7.0", "eslint": "^9", "eslint-config-next": "15.4.5", "jest": "^29.7.0", "jest-environment-node": "^29.7.0", "jest-watch-typeahead": "^2.2.2", "node-mocks-http": "^1.13.0", "prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.6.14", "prisma": "^6.13.0", "tailwindcss": "^4", "typescript": "^5"}}