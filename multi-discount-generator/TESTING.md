# 测试规范文档

本文档详细说明了项目的测试架构、规范和最佳实践。

## 测试架构概览

项目采用分层测试架构，将测试分为三个主要层次：

```
测试金字塔
    /\
   /  \     E2E Tests (端到端测试)
  /____\    - 少量，覆盖关键用户流程
 /      \   
/________\  Integration Tests (集成测试)
\        /  - 中等数量，测试模块间交互
 \______/   
  \    /    Unit Tests (单元测试)
   \__/     - 大量，快速执行，高覆盖率
```

## 目录结构

```
tests/
├── unit/                    # 单元测试
│   ├── lib/                 # 库函数测试
│   ├── components/          # 组件测试
│   ├── api/                 # API路由测试
│   └── utils/               # 工具函数测试
├── integration/             # 集成测试
│   ├── api/                 # API集成测试
│   ├── database/            # 数据库集成测试
│   └── services/            # 服务集成测试
├── e2e/                     # 端到端测试
│   ├── pages/               # 页面功能测试
│   └── workflows/           # 完整工作流测试
├── fixtures/                # 测试数据和固定装置
│   ├── data/                # 测试数据文件
│   ├── templates/           # 测试模板文件
│   └── mocks/               # 模拟数据
├── utils/                   # 测试工具函数
│   ├── helpers.ts           # 测试辅助函数
│   ├── setup.ts             # 测试环境设置
│   └── teardown.ts          # 测试清理
├── mocks/                   # 全局模拟对象
│   ├── api.ts               # API模拟
│   ├── database.ts          # 数据库模拟
│   └── external.ts          # 外部服务模拟
└── tools/                   # 测试工具
    ├── template-generator.ts # 模板生成工具
    ├── template-debugger.ts  # 模板调试工具
    └── cleanup.ts            # 清理工具
```

## 测试类型详解

### 1. 单元测试 (Unit Tests)

**目的**: 测试单个函数、组件或模块的功能
**特点**: 快速执行，隔离性强，不依赖外部服务
**覆盖范围**: 
- 纯函数逻辑
- 组件渲染和交互
- 工具函数
- 数据转换

**示例**:
```typescript
// tests/unit/lib/time-parsing.test.ts
describe('时间解析功能', () => {
  it('应该正确转换Excel时间小数', () => {
    const result = excelTimeToTimeString(0.5);
    expect(result).toBe('12:00:00');
  });
});
```

### 2. 集成测试 (Integration Tests)

**目的**: 测试多个模块之间的交互
**特点**: 可能涉及数据库、API调用等
**覆盖范围**:
- API端点功能
- 数据库操作
- 服务间通信
- 文件处理

**示例**:
```typescript
// tests/integration/api/batch-export.integration.test.ts
describe('批量导出API集成测试', () => {
  it('应该成功上传Excel模板文件', async () => {
    const response = await fetch('/api/templates/upload', {
      method: 'POST',
      body: formData
    });
    expect(response.ok).toBe(true);
  });
});
```

### 3. 端到端测试 (E2E Tests)

**目的**: 测试完整的用户工作流
**特点**: 模拟真实用户操作，测试整个应用
**覆盖范围**:
- 完整的用户流程
- 跨页面交互
- 真实数据流
- 用户界面功能

## 命名规范

### 测试文件命名
- **单元测试**: `[模块名].test.ts`
- **集成测试**: `[功能名].integration.test.ts`
- **端到端测试**: `[工作流名].e2e.test.ts`

### 测试描述命名
- 使用中文描述，清晰表达测试意图
- 格式: `应该 + 预期行为`
- 示例: `应该正确解析Excel时间格式`

### 测试套件命名
- 使用功能模块名称
- 示例: `时间解析功能`、`批量导出API`

## 运行测试

### 基本命令

```bash
# 运行所有测试
npm test

# 运行单元测试
npm run test:unit

# 运行集成测试
npm run test:integration

# 运行端到端测试
npm run test:e2e

# 运行所有测试并生成覆盖率报告
npm run test:coverage

# 监视模式运行测试
npm run test:watch
```

### 高级命令

```bash
# 使用测试运行器
npm run test:script unit
npm run test:script integration --verbose
npm run test:script all

# 清理测试数据
npm run test:cleanup
npm run test:cleanup:dry  # 预览模式

# 生成测试模板
npm run test:generate-templates

# 调试测试模板
npm run test:debug-templates
```

## 测试配置

### Jest配置 (jest.config.js)
- 支持TypeScript
- 配置路径映射
- 设置覆盖率阈值
- 分项目配置不同测试类型

### 环境变量
- `NODE_ENV=test`: 测试环境标识
- `VERBOSE_TESTS=true`: 显示详细日志
- `TEST_DATABASE_URL`: 测试数据库连接

## 最佳实践

### 1. 测试编写原则
- **AAA模式**: Arrange (准备) → Act (执行) → Assert (断言)
- **单一职责**: 每个测试只验证一个功能点
- **独立性**: 测试之间不应相互依赖
- **可重复性**: 测试结果应该一致

### 2. 模拟和存根
- 使用Jest的模拟功能隔离外部依赖
- 为API调用、数据库操作创建模拟
- 使用fixtures提供一致的测试数据

### 3. 测试数据管理
- 使用fixtures管理测试数据
- 每次测试后清理数据
- 避免硬编码测试数据

### 4. 性能考虑
- 单元测试应该快速执行（< 1秒）
- 集成测试可以稍慢（< 30秒）
- 端到端测试允许更长时间（< 2分钟）

## 覆盖率要求

### 最低覆盖率阈值
- **语句覆盖率**: 70%
- **分支覆盖率**: 70%
- **函数覆盖率**: 70%
- **行覆盖率**: 70%

### 覆盖率报告
- 生成HTML报告: `coverage/lcov-report/index.html`
- 生成LCOV报告: `coverage/lcov.info`
- 控制台输出: 运行测试时显示

## 持续集成

### 测试流水线
1. 代码检查 (ESLint)
2. 类型检查 (TypeScript)
3. 单元测试
4. 集成测试
5. 覆盖率检查
6. 端到端测试（可选）

### 质量门禁
- 所有测试必须通过
- 覆盖率不能低于阈值
- 不能有ESLint错误

## 故障排除

### 常见问题
1. **测试超时**: 增加timeout设置或优化测试逻辑
2. **模拟失效**: 检查模拟配置和清理逻辑
3. **数据污染**: 确保测试间数据隔离
4. **路径问题**: 检查Jest路径映射配置

### 调试技巧
1. 使用`--verbose`查看详细输出
2. 使用`console.log`添加调试信息
3. 运行单个测试文件进行调试
4. 检查测试环境变量设置

## 贡献指南

### 添加新测试
1. 确定测试类型（单元/集成/端到端）
2. 选择合适的目录
3. 遵循命名规范
4. 编写清晰的测试描述
5. 添加必要的模拟和fixtures

### 维护现有测试
1. 定期更新测试数据
2. 重构重复的测试代码
3. 优化测试性能
4. 更新测试文档

---

**注意**: 本文档会随着项目发展持续更新，请定期查看最新版本。
