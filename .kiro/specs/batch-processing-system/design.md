# 批量处理和后台管理系统 - 设计文档

## 概述

基于现有的多件多折模板生成器，扩展批量处理功能。采用SQLite + Prisma的数据存储方案，实现异步任务队列、实时状态更新和完整的后台管理系统。

## 技术栈选择

### 数据库层
- **SQLite** - 轻量级本地数据库，零配置
- **Prisma ORM** - 类型安全的数据库访问层
- **Prisma Migrate** - 数据库迁移管理

### 后端服务
- **Next.js API Routes** - 服务端API接口
- **Bull Queue** - Redis任务队列（可选，本地可用内存队列）
- **Node.js Worker Threads** - 多线程处理支持
- **Socket.io** - 实时通信

### 前端扩展
- **React Query (TanStack Query)** - 服务端状态管理
- **Socket.io Client** - 实时状态更新
- **React Hook Form** - 表单处理
- **React Dropzone** - 文件上传

## 数据库设计

### 数据模型

```prisma
// prisma/schema.prisma
generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = "file:./dev.db"
}

model Task {
  id          String   @id @default(cuid())
  name        String   // 任务名称
  type        TaskType // 任务类型
  status      TaskStatus @default(PENDING)
  priority    Int      @default(0)
  
  // 任务配置
  config      Json     // 活动配置（区域、折扣等）
  
  // 进度信息
  totalItems  Int      @default(0)
  processedItems Int   @default(0)
  failedItems Int      @default(0)
  
  // 文件信息
  inputFile   String?  // 输入文件路径
  outputFile  String?  // 输出文件路径
  
  // 时间戳
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  startedAt   DateTime?
  completedAt DateTime?
  
  // 关联数据
  products    Product[]
  logs        TaskLog[]
  
  @@map("tasks")
}

model Product {
  id        String   @id @default(cuid())
  productId String   // 商品ID
  taskId    String   // 关联任务
  status    ProductStatus @default(PENDING)
  errorMessage String?
  
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  task      Task     @relation(fields: [taskId], references: [id], onDelete: Cascade)
  
  @@map("products")
}

model TaskLog {
  id        String   @id @default(cuid())
  taskId    String
  level     LogLevel
  message   String
  details   Json?
  
  createdAt DateTime @default(now())
  
  task      Task     @relation(fields: [taskId], references: [id], onDelete: Cascade)
  
  @@map("task_logs")
}

enum TaskType {
  SINGLE    // 单个商品
  BATCH     // 批量处理
}

enum TaskStatus {
  PENDING   // 等待中
  RUNNING   // 执行中
  COMPLETED // 已完成
  FAILED    // 失败
  CANCELLED // 已取消
}

enum ProductStatus {
  PENDING   // 等待处理
  PROCESSING // 处理中
  COMPLETED // 已完成
  FAILED    // 失败
}

enum LogLevel {
  INFO
  WARN
  ERROR
  DEBUG
}
```

## 系统架构

### 整体架构图

```mermaid
graph TB
    subgraph "前端层"
        A[单商品生成器] --> C[任务管理界面]
        B[批量上传界面] --> C
        C --> D[后台管理界面]
    end
    
    subgraph "API层"
        E[任务API] --> F[文件处理API]
        F --> G[WebSocket API]
    end
    
    subgraph "服务层"
        H[任务队列服务] --> I[Excel生成服务]
        I --> J[文件存储服务]
        J --> K[通知服务]
    end
    
    subgraph "数据层"
        L[(SQLite数据库)] --> M[文件系统]
    end
    
    A --> E
    B --> E
    C --> E
    D --> E
    E --> H
    H --> L
    G --> K
```

### 任务处理流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端
    participant A as API
    participant Q as 任务队列
    participant W as Worker
    participant D as 数据库
    participant S as Socket
    
    U->>F: 上传商品ID文件
    F->>A: POST /api/tasks/create
    A->>D: 创建任务记录
    A->>Q: 加入任务队列
    A->>F: 返回任务ID
    F->>U: 显示任务创建成功
    
    Q->>W: 分配任务给Worker
    W->>D: 更新任务状态为RUNNING
    W->>S: 广播状态更新
    S->>F: 推送状态更新
    F->>U: 显示处理中状态
    
    loop 处理每个商品
        W->>W: 生成Excel数据
        W->>D: 更新进度
        W->>S: 推送进度更新
    end
    
    W->>D: 保存输出文件
    W->>D: 更新任务状态为COMPLETED
    W->>S: 广播完成状态
    S->>F: 推送完成通知
    F->>U: 显示下载链接
```

## 核心服务设计

### 1. 任务队列服务

```typescript
// lib/queue/TaskQueue.ts
import { EventEmitter } from 'events'
import { PrismaClient } from '@prisma/client'

export class TaskQueue extends EventEmitter {
  private prisma: PrismaClient
  private workers: Map<string, Worker> = new Map()
  private maxConcurrency = 3
  private isProcessing = false

  constructor() {
    super()
    this.prisma = new PrismaClient()
  }

  async addTask(taskData: CreateTaskInput): Promise<string> {
    const task = await this.prisma.task.create({
      data: {
        ...taskData,
        status: 'PENDING'
      }
    })

    this.emit('taskAdded', task)
    this.processQueue()
    
    return task.id
  }

  private async processQueue(): Promise<void> {
    if (this.isProcessing || this.workers.size >= this.maxConcurrency) {
      return
    }

    this.isProcessing = true

    const pendingTask = await this.prisma.task.findFirst({
      where: { status: 'PENDING' },
      orderBy: [
        { priority: 'desc' },
        { createdAt: 'asc' }
      ]
    })

    if (pendingTask) {
      await this.executeTask(pendingTask)
    }

    this.isProcessing = false
    
    // 检查是否还有待处理任务
    const remainingTasks = await this.prisma.task.count({
      where: { status: 'PENDING' }
    })
    
    if (remainingTasks > 0) {
      setImmediate(() => this.processQueue())
    }
  }

  private async executeTask(task: Task): Promise<void> {
    const worker = new TaskWorker(task.id, this.prisma)
    this.workers.set(task.id, worker)

    worker.on('progress', (progress) => {
      this.emit('taskProgress', { taskId: task.id, progress })
    })

    worker.on('completed', () => {
      this.workers.delete(task.id)
      this.emit('taskCompleted', task.id)
      this.processQueue()
    })

    worker.on('failed', (error) => {
      this.workers.delete(task.id)
      this.emit('taskFailed', { taskId: task.id, error })
      this.processQueue()
    })

    await worker.start()
  }
}
```

### 2. 任务执行器

```typescript
// lib/workers/TaskWorker.ts
import { EventEmitter } from 'events'
import { PrismaClient } from '@prisma/client'
import { ExcelService } from '../excel'
import { TimeGenerator } from '../time-generator'

export class TaskWorker extends EventEmitter {
  private taskId: string
  private prisma: PrismaClient

  constructor(taskId: string, prisma: PrismaClient) {
    super()
    this.taskId = taskId
    this.prisma = prisma
  }

  async start(): Promise<void> {
    try {
      const task = await this.prisma.task.findUnique({
        where: { id: this.taskId },
        include: { products: true }
      })

      if (!task) {
        throw new Error('Task not found')
      }

      await this.updateTaskStatus('RUNNING')
      await this.logInfo('任务开始执行')

      const config = task.config as any
      const products = task.products

      // 生成时间段
      const timeSlots = await this.generateTimeSlots(config)
      
      // 处理每个商品
      for (let i = 0; i < products.length; i++) {
        const product = products[i]
        
        try {
          await this.processProduct(product, config, timeSlots)
          await this.updateProductStatus(product.id, 'COMPLETED')
          
          const progress = Math.round(((i + 1) / products.length) * 100)
          await this.updateProgress(i + 1, 0)
          this.emit('progress', progress)
          
        } catch (error) {
          await this.updateProductStatus(product.id, 'FAILED', error.message)
          await this.updateProgress(i + 1, 1)
          await this.logError(`商品 ${product.productId} 处理失败`, error)
        }
      }

      // 生成最终Excel文件
      const outputPath = await this.generateFinalExcel(task)
      
      await this.prisma.task.update({
        where: { id: this.taskId },
        data: {
          status: 'COMPLETED',
          outputFile: outputPath,
          completedAt: new Date()
        }
      })

      await this.logInfo('任务执行完成')
      this.emit('completed')

    } catch (error) {
      await this.updateTaskStatus('FAILED')
      await this.logError('任务执行失败', error)
      this.emit('failed', error)
    }
  }

  private async generateTimeSlots(config: any) {
    if (config.timeMode === 'continuous') {
      return TimeGenerator.generateContinuousTimeSlots(config)
    } else {
      return TimeGenerator.generateRandomTimeSlots(config)
    }
  }

  private async processProduct(product: any, config: any, timeSlots: any) {
    // 为单个商品生成Excel数据
    const formData = {
      ...config,
      productId: product.productId
    }
    
    return ExcelService.generateExcelData(formData, timeSlots.slots)
  }

  private async generateFinalExcel(task: any): Promise<string> {
    // 合并所有商品的Excel数据并生成文件
    const allData = await this.collectAllProductData(task)
    const workbook = ExcelService.generateBatchWorkbook(allData)
    const filename = `batch_${task.id}_${Date.now()}.xlsx`
    const outputPath = `uploads/output/${filename}`
    
    ExcelService.saveWorkbook(workbook, outputPath)
    return outputPath
  }

  // 辅助方法
  private async updateTaskStatus(status: string) {
    await this.prisma.task.update({
      where: { id: this.taskId },
      data: { status, updatedAt: new Date() }
    })
  }

  private async updateProgress(processed: number, failed: number) {
    await this.prisma.task.update({
      where: { id: this.taskId },
      data: { 
        processedItems: processed,
        failedItems: failed,
        updatedAt: new Date()
      }
    })
  }

  private async logInfo(message: string, details?: any) {
    await this.prisma.taskLog.create({
      data: {
        taskId: this.taskId,
        level: 'INFO',
        message,
        details: details ? JSON.stringify(details) : null
      }
    })
  }

  private async logError(message: string, error: any) {
    await this.prisma.taskLog.create({
      data: {
        taskId: this.taskId,
        level: 'ERROR',
        message,
        details: JSON.stringify({
          error: error.message,
          stack: error.stack
        })
      }
    })
  }
}
```

### 3. WebSocket实时通信

```typescript
// lib/socket/SocketManager.ts
import { Server as SocketIOServer } from 'socket.io'
import { Server as HTTPServer } from 'http'

export class SocketManager {
  private io: SocketIOServer

  constructor(server: HTTPServer) {
    this.io = new SocketIOServer(server, {
      cors: {
        origin: process.env.NODE_ENV === 'development' ? '*' : false
      }
    })

    this.setupEventHandlers()
  }

  private setupEventHandlers() {
    this.io.on('connection', (socket) => {
      console.log('Client connected:', socket.id)

      socket.on('joinTask', (taskId: string) => {
        socket.join(`task:${taskId}`)
      })

      socket.on('leaveTask', (taskId: string) => {
        socket.leave(`task:${taskId}`)
      })

      socket.on('disconnect', () => {
        console.log('Client disconnected:', socket.id)
      })
    })
  }

  // 广播任务状态更新
  broadcastTaskUpdate(taskId: string, update: any) {
    this.io.to(`task:${taskId}`).emit('taskUpdate', {
      taskId,
      ...update
    })
  }

  // 广播任务进度
  broadcastTaskProgress(taskId: string, progress: number) {
    this.io.to(`task:${taskId}`).emit('taskProgress', {
      taskId,
      progress
    })
  }

  // 广播任务完成
  broadcastTaskCompleted(taskId: string, outputFile: string) {
    this.io.to(`task:${taskId}`).emit('taskCompleted', {
      taskId,
      outputFile,
      downloadUrl: `/api/download/${taskId}`
    })
  }
}
```

## API设计

### 任务管理API

```typescript
// pages/api/tasks/create.ts
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    const { type, config, products } = req.body

    // 创建任务
    const task = await prisma.task.create({
      data: {
        name: `${type === 'BATCH' ? '批量' : '单个'}任务_${Date.now()}`,
        type,
        config,
        totalItems: products.length,
        products: {
          create: products.map((productId: string) => ({
            productId,
            status: 'PENDING'
          }))
        }
      }
    })

    // 添加到队列
    const taskQueue = TaskQueue.getInstance()
    await taskQueue.addTask(task.id)

    res.status(201).json({ taskId: task.id })
  } catch (error) {
    res.status(500).json({ error: error.message })
  }
}

// pages/api/tasks/[id].ts
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const { id } = req.query

  if (req.method === 'GET') {
    const task = await prisma.task.findUnique({
      where: { id: id as string },
      include: {
        products: true,
        logs: {
          orderBy: { createdAt: 'desc' },
          take: 50
        }
      }
    })

    if (!task) {
      return res.status(404).json({ error: 'Task not found' })
    }

    res.json(task)
  }
}

// pages/api/tasks/list.ts
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const { page = 1, limit = 20, status, type } = req.query

  const where: any = {}
  if (status) where.status = status
  if (type) where.type = type

  const tasks = await prisma.task.findMany({
    where,
    include: {
      _count: {
        select: {
          products: true,
          logs: true
        }
      }
    },
    orderBy: { createdAt: 'desc' },
    skip: (Number(page) - 1) * Number(limit),
    take: Number(limit)
  })

  const total = await prisma.task.count({ where })

  res.json({
    tasks,
    pagination: {
      page: Number(page),
      limit: Number(limit),
      total,
      pages: Math.ceil(total / Number(limit))
    }
  })
}
```

## 前端组件设计

### 批量上传组件

```typescript
// components/BatchUpload.tsx
export function BatchUpload() {
  const [file, setFile] = useState<File | null>(null)
  const [products, setProducts] = useState<string[]>([])
  const [isUploading, setIsUploading] = useState(false)

  const onDrop = useCallback((acceptedFiles: File[]) => {
    const file = acceptedFiles[0]
    setFile(file)
    parseProductFile(file)
  }, [])

  const parseProductFile = async (file: File) => {
    try {
      const data = await ExcelService.readExcelFile(file)
      const productIds = data.map(row => row['商品ID']).filter(Boolean)
      setProducts(productIds)
    } catch (error) {
      toast.error('文件解析失败')
    }
  }

  const handleSubmit = async (config: FormData) => {
    setIsUploading(true)
    try {
      const response = await fetch('/api/tasks/create', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          type: products.length > 100 ? 'BATCH' : 'SINGLE',
          config,
          products
        })
      })

      const { taskId } = await response.json()
      router.push(`/tasks/${taskId}`)
    } catch (error) {
      toast.error('任务创建失败')
    } finally {
      setIsUploading(false)
    }
  }

  return (
    <div className="space-y-6">
      <Dropzone onDrop={onDrop} accept={{ 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'] }}>
        {/* 文件上传UI */}
      </Dropzone>

      {products.length > 0 && (
        <div className="rounded-md bg-blue-50 p-4">
          <h3 className="font-medium">商品ID预览</h3>
          <p className="text-sm text-gray-600">共 {products.length} 个商品</p>
          <div className="mt-2 max-h-32 overflow-y-auto">
            {products.slice(0, 10).map((id, index) => (
              <span key={index} className="mr-2 inline-block rounded bg-white px-2 py-1 text-xs">
                {id}
              </span>
            ))}
            {products.length > 10 && <span className="text-xs text-gray-500">...</span>}
          </div>
        </div>
      )}

      {/* 配置表单 */}
      <TemplateConfigForm onSubmit={handleSubmit} loading={isUploading} />
    </div>
  )
}
```

这个设计提供了：

1. **完整的批量处理能力** - 支持大量商品ID的异步处理
2. **任务队列管理** - 智能的任务调度和并发控制
3. **实时状态更新** - WebSocket推送任务进度
4. **后台管理界面** - 完整的任务监控和管理
5. **数据持久化** - SQLite + Prisma的可靠存储
6. **可扩展架构** - 模块化设计，便于后续扩展

需要我继续创建具体的实现代码吗？