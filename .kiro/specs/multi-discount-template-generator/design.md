# 多件多折批量导入模板生成器 - 设计文档

## 概述

本系统是一个基于Next.js的现代化Web应用，用于生成多件多折活动的Excel批量导入模板。系统采用React Server Components和Client Components的混合架构，使用Tailwind CSS进行样式设计，SheetJS处理Excel文件操作，提供直观的用户界面和强大的时间生成算法。

## 技术栈选择

### 前端框架
- **Next.js 15** (App Router) - 提供服务端渲染、静态生成和优秀的开发体验
- **TypeScript** - 类型安全，提高代码质量和开发效率
- **React 18** - 现代化的组件开发和状态管理

### 样式和UI
- **Tailwind CSS 4** - 实用优先的CSS框架，快速构建响应式界面
- **Headless UI** - 无样式的可访问组件库
- **Lucide React** - 现代化的图标库

### 数据处理
- **SheetJS (xlsx)** - Excel文件读写和处理，强制XLSX格式导出
- **date-fns** - 日期时间处理和格式化
- **date-fns-tz** - 时区处理，支持中国时区
- **chinese-holidays** - 中国法定节假日计算
- **zod** - 运行时类型验证和数据校验

### 开发工具
- **ESLint + Prettier** - 代码质量和格式化
- **Husky + lint-staged** - Git hooks和代码检查

## 架构设计

### 目录结构
```
src/
├── app/                          # Next.js App Router
│   ├── globals.css              # 全局样式
│   ├── layout.tsx               # 根布局
│   ├── page.tsx                 # 首页
│   └── api/                     # API路由
├── components/                   # React组件
│   ├── ui/                      # 基础UI组件
│   ├── forms/                   # 表单组件
│   └── templates/               # 模板相关组件
├── lib/                         # 工具函数和配置
│   ├── utils.ts                 # 通用工具函数
│   ├── validations.ts           # 数据验证模式
│   ├── excel.ts                 # Excel处理逻辑
│   └── time-generator.ts        # 时间生成算法
├── types/                       # TypeScript类型定义
└── constants/                   # 常量定义
```

## 组件和接口

### 核心组件

#### 1. TemplateGenerator (主组件)
```typescript
interface TemplateGeneratorProps {
  initialData?: Partial<FormData>
}

interface FormData {
  productId: string
  region: RegionType
  minQuantity: number // 1-10件下拉选择
  discount: number // 默认8.0折
  timeMode: 'continuous' | 'random'
  // 连续模式
  startTime: Date
  endTime: Date
  days: number
  weekendFullDay: boolean // 包含法定节假日
  // 随机模式
  randomStartTime: Date
  timeSlotCount: number
  slotDuration: 1 | 2 // 小时
}
```

#### 2. RegionSelector (区域选择组件)
```typescript
interface RegionSelectorProps {
  value: RegionType
  onChange: (value: RegionType) => void
}

type RegionType = 
  | { type: 'national' }
  | { type: 'regional', regions: RegionalArea[] }

type RegionalArea = '华中' | '西南/西北' | '华南' | '华东' | '华北'
```

#### 3. TimeGenerator (时间生成组件)
```typescript
interface TimeGeneratorProps {
  mode: 'continuous' | 'random'
  config: ContinuousConfig | RandomConfig
  onChange: (timeSlots: TimeSlot[]) => void
}

interface TimeSlot {
  startTime: Date
  endTime: Date
}
```

#### 4. PreviewTable (预览表格组件)
```typescript
interface PreviewTableProps {
  data: ExcelRowData[]
  onExport: () => void
}

interface ExcelRowData {
  活动类型: string // 固定"多件多折"
  商品ID: string
  '开始时间\n示例：\n2020-03-27 00:00:00': string // 完整列标题
  '结束时间\n示例：\n2020-03-27 23:59:59': string // 完整列标题
  '活动区域\n支持填写"全国"及5大区\n5大区：华中,西南/西北,华南,华东,华北': string
  '满几件\n说明：表示满N件，件数只能在下拉框中选择': number
  '折扣-打几折\n说明：表示打几折，折扣只能下拉选择': number
  '立减金额-多件总额下的扣减金额\n表示扣减的金额，单位元，支持最多2位小数，与折扣只能择一生效': string
}
```

### 数据模型

#### Excel模板数据结构
```typescript
interface ExcelTemplate {
  columns: {
    活动类型: string           // 固定值 "多件打折"
    商品ID: string
    开始时间: string           // YYYY-MM-DD HH:mm:ss
    结束时间: string           // YYYY-MM-DD HH:mm:ss
    活动区域: string
    满几件: number             // 默认1
    折扣: number              // 5.0-9.9，步长0.1
    立减金额: string          // 空值
  }[]
}
```

#### 时间生成配置
```typescript
interface ContinuousConfig {
  startTime: Date
  endTime: Date
  days: number
  weekendFullDay: boolean
}

interface RandomConfig {
  startTime: Date
  slotCount: number
  slotDuration: 1 | 2
}
```

## 核心算法

### 连续时间生成算法（优化版）
```typescript
function generateContinuousTimeSlots(config: ContinuousConfig): TimeSlot[] {
  const slots: TimeSlot[] = []
  const { startTime, endTime, days, weekendFullDay } = config
  
  for (let i = 0; i < days; i++) {
    const currentDate = addDays(startTime, i)
    const dayOfWeek = getDay(currentDate) // 0=周日, 6=周六
    const isHoliday = isChineseHoliday(currentDate)
    const isWeekendOrHoliday = dayOfWeek === 0 || dayOfWeek === 6 || isHoliday
    
    if (isWeekendOrHoliday && weekendFullDay) {
      // 周末/节假日特殊处理
      if (dayOfWeek === 5) { // 周五
        // 周五按正常工作日时间
        slots.push(generateWorkdaySlot(currentDate, startTime, endTime))
      } else if (dayOfWeek === 6) { // 周六
        // 周六：从前一天结束时间延续到23:59:59
        const prevSlot = slots[slots.length - 1]
        if (prevSlot && isSameDay(prevSlot.endTime, currentDate)) {
          slots.push({
            startTime: prevSlot.endTime,
            endTime: endOfDay(currentDate)
          })
        } else {
          slots.push({
            startTime: startOfDay(currentDate),
            endTime: endOfDay(currentDate)
          })
        }
      } else if (dayOfWeek === 0) { // 周日
        // 周日：00:00:00到周一结束时间
        const mondayEnd = setTimeToDate(addDays(currentDate, 1), endTime)
        slots.push({
          startTime: startOfDay(currentDate),
          endTime: mondayEnd
        })
      } else {
        // 其他节假日全天
        slots.push({
          startTime: startOfDay(currentDate),
          endTime: endOfDay(currentDate)
        })
      }
    } else {
      // 工作日：使用指定时间段，支持跨天
      slots.push(generateWorkdaySlot(currentDate, startTime, endTime))
    }
  }
  
  return slots
}

function generateWorkdaySlot(date: Date, startTime: Date, endTime: Date): TimeSlot {
  const dayStart = setTimeToDate(date, startTime)
  const dayEnd = isTimeAfter(endTime, startTime)
    ? setTimeToDate(date, endTime)
    : setTimeToDate(addDays(date, 1), endTime) // 跨天处理
  
  return { startTime: dayStart, endTime: dayEnd }
}
```

### 随机时间生成算法
```typescript
function generateRandomTimeSlots(config: RandomConfig): TimeSlot[] {
  const slots: TimeSlot[] = []
  const { startTime, slotCount, slotDuration } = config
  let currentEnd = startTime
  
  for (let i = 0; i < slotCount; i++) {
    // 随机生成下一个开始时间（在前一个结束时间之后）
    const minGap = 30 // 最小间隔30分钟
    const maxGap = 240 // 最大间隔4小时
    const randomGap = Math.floor(Math.random() * (maxGap - minGap + 1)) + minGap
    
    const slotStart = addMinutes(currentEnd, randomGap)
    const slotEnd = addHours(slotStart, slotDuration)
    
    slots.push({
      startTime: slotStart,
      endTime: slotEnd
    })
    
    currentEnd = slotEnd
  }
  
  return slots
}
```

## Excel处理逻辑

### 数据转换和导出（优化版）
```typescript
class ExcelService {
  static generateWorkbook(formData: FormData, timeSlots: TimeSlot[]): XLSX.WorkBook {
    // 使用完整的列标题（与原模板一致）
    const headers = [
      '活动类型',
      '商品ID', 
      '开始时间\n示例：\n2020-03-27 00:00:00',
      '结束时间\n示例：\n2020-03-27 23:59:59',
      '活动区域\n支持填写"全国"及5大区\n5大区：华中,西南/西北,华南,华东,华北',
      '满几件\n说明：表示满N件，件数只能在下拉框中选择',
      '折扣-打几折\n说明：表示打几折，折扣只能下拉选择',
      '立减金额-多件总额下的扣减金额\n表示扣减的金额，单位元，支持最多2位小数，与折扣只能择一生效'
    ]
    
    // 每个时间段生成一条记录
    const rows = timeSlots.map(slot => [
      '"多件多折"', // 用双引号包围
      `"${formData.productId}"`,
      `"${format(slot.startTime, 'yyyy-MM-dd HH:mm:ss')}"`,
      `"${format(slot.endTime, 'yyyy-MM-dd HH:mm:ss')}"`,
      `"${this.formatRegion(formData.region)}"`,
      `"${formData.minQuantity}"`,
      `"${formData.discount}"`,
      '""' // 空值
    ])
    
    // 创建工作表，包含多行标题
    const worksheet = XLSX.utils.aoa_to_sheet([headers, ...rows])
    
    // 设置列宽
    worksheet['!cols'] = [
      { wch: 12 }, // 活动类型
      { wch: 15 }, // 商品ID
      { wch: 25 }, // 开始时间
      { wch: 25 }, // 结束时间
      { wch: 30 }, // 活动区域
      { wch: 20 }, // 满几件
      { wch: 20 }, // 折扣
      { wch: 35 }  // 立减金额
    ]
    
    const workbook = XLSX.utils.book_new()
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1')
    
    return workbook
  }
  
  static exportToFile(workbook: XLSX.WorkBook, filename: string): void {
    // 强制XLSX格式
    const xlsxFilename = filename.endsWith('.xlsx') ? filename : `${filename}.xlsx`
    XLSX.writeFile(workbook, xlsxFilename, { 
      bookType: 'xlsx',
      compression: true 
    })
  }
  
  private static formatRegion(region: RegionType): string {
    if (region.type === 'national') {
      return '全国'
    }
    // 多个大区用逗号分隔
    return region.regions.join(',')
  }
}
```

## 错误处理

### 表单验证
```typescript
const formSchema = z.object({
  productId: z.string().min(1, '商品ID不能为空'),
  region: z.union([
    z.object({ type: z.literal('national') }),
    z.object({ 
      type: z.literal('regional'),
      regions: z.array(z.enum(['华中', '西南/西北', '华南', '华东', '华北'])).min(1)
    })
  ]),
  minQuantity: z.number().min(1).max(999),
  discount: z.number().min(5.0).max(9.9).multipleOf(0.1),
  timeMode: z.enum(['continuous', 'random']),
  // ... 其他字段验证
})
```

### 错误边界和用户反馈
```typescript
interface ErrorBoundaryState {
  hasError: boolean
  error?: Error
}

class TemplateGeneratorErrorBoundary extends Component<Props, ErrorBoundaryState> {
  // 错误捕获和用户友好的错误显示
}
```

## 测试策略

### 单元测试
- 时间生成算法测试
- Excel数据转换测试
- 表单验证测试
- 工具函数测试

### 集成测试
- 完整的表单提交流程
- Excel文件生成和下载
- 不同时间模式的端到端测试

### 用户体验测试
- 响应式设计测试
- 可访问性测试
- 性能测试

## 性能优化

### 客户端优化
- React.memo用于组件优化
- useMemo和useCallback用于计算缓存
- 虚拟滚动用于大数据预览
- 代码分割和懒加载

### 服务端优化
- 静态生成用于首页
- 服务端组件减少客户端JavaScript
- 图片优化和CDN

## 可扩展性设计

### 插件化架构
- 时间生成器可扩展新的模式
- 区域选择器支持自定义区域配置
- Excel模板支持多种格式

### 配置化
- 折扣范围和步长可配置
- 时间格式可配置
- 导出文件名模板可配置

### 国际化准备
- 多语言支持架构
- 时区处理
- 本地化的日期时间格式