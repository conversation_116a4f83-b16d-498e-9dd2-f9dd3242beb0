# 多件多折批量导入模板生成器 - 需求文档

## 介绍

本功能是一个在线网页工具，用于生成多件多折活动的批量导入Excel模板。用户可以通过网页界面填写商品信息和活动参数，系统会根据用户设置的时间规则自动生成对应的活动时间段，并导出符合原始Excel模板格式的文件。

## 需求

### 需求 1 - 基础表单输入功能

**用户故事:** 作为活动运营人员，我希望能在网页上填写商品ID和活动参数，以便快速生成活动配置数据。

#### 验收标准

1. WHEN 用户访问网页 THEN 系统 SHALL 显示包含以下字段的表单：
   - 商品ID（文本输入框）
   - 活动区域选择（单选全国或多选5大区）
   - 满几件（默认值1）
   - 折扣选择（下拉框，5折到9.9折，步长0.1）

2. WHEN 用户选择"全国"活动区域 THEN 系统 SHALL 禁用5大区选择选项

3. WHEN 用户选择5大区选项 THEN 系统 SHALL 禁用"全国"选项并支持多选华中、西南/西北、华南、华东、华北

4. WHEN 用户打开折扣下拉框 THEN 系统 SHALL 显示从5.0到9.9的所有选项，步长为0.1（5.0, 5.1, 5.2...9.9）

### 需求 2 - 连续天数时间生成功能

**用户故事:** 作为活动运营人员，我希望能设置一个时间段并指定连续天数，系统自动生成每天相同时间段的活动安排，以便批量创建长期活动。

#### 验收标准

1. WHEN 用户选择开始时间和结束时间 THEN 系统 SHALL 提供日期时间选择器支持精确到分钟

2. WHEN 用户输入活动天数（如30天） THEN 系统 SHALL 根据选择的时间段生成连续30天的活动时间

3. WHEN 生成的活动日期是工作日 THEN 系统 SHALL 使用用户指定的时间段

4. WHEN 生成的活动日期是周六或周日 THEN 系统 SHALL 自动设置为全天24小时（00:00:00-23:59:59）

5. WHEN 用户选择跨天时间段（如19:30:00到次日08:00:00） THEN 系统 SHALL 正确处理跨天逻辑

### 需求 3 - 随机时间段生成功能

**用户故事:** 作为活动运营人员，我希望能生成多个随机的不重叠时间段，以便创建灵活的活动时间安排。

#### 验收标准

1. WHEN 用户指定起始时间和时间段数量 THEN 系统 SHALL 提供时间段数量输入框和时长选择（1小时或2小时）

2. WHEN 用户点击生成随机时间段 THEN 系统 SHALL 生成指定数量的不重叠时间段

3. WHEN 生成随机时间段 THEN 系统 SHALL 确保每个时间段的开始时间都在前一个时间段结束时间之后

4. WHEN 生成随机时间段 THEN 系统 SHALL 确保所有时间段不重叠且按时间顺序排列

5. WHEN 用户设置时间段长度为1小时 THEN 系统 SHALL 生成每个时间段持续1小时的活动安排

### 需求 4 - Excel文件导出功能

**用户故事:** 作为活动运营人员，我希望能导出与原始模板完全一致的Excel文件，以便直接用于系统批量导入。

#### 验收标准

1. WHEN 用户点击导出按钮 THEN 系统 SHALL 生成包含以下列的Excel文件：
   - 活动类型（固定值"多件打折"）
   - 商品ID（用户输入值）
   - 开始时间（格式：YYYY-MM-DD HH:mm:ss）
   - 结束时间（格式：YYYY-MM-DD HH:mm:ss）
   - 活动区域（用户选择值）
   - 满几件（默认1）
   - 折扣-打几折（用户选择值）
   - 立减金额-多件总额下的扣减金额（空值）

2. WHEN 导出Excel文件 THEN 系统 SHALL 确保列名与原始模板完全一致

3. WHEN 导出Excel文件 THEN 系统 SHALL 为每个生成的时间段创建一行数据

4. WHEN 导出Excel文件 THEN 系统 SHALL 使用浏览器下载功能保存文件

### 需求 5 - 用户界面和体验

**用户故事:** 作为活动运营人员，我希望界面简洁易用且响应迅速，以便高效完成工作。

#### 验收标准

1. WHEN 用户访问网页 THEN 系统 SHALL 显示使用Tailwind CSS设计的现代化界面

2. WHEN 用户在移动设备上访问 THEN 系统 SHALL 提供响应式设计适配不同屏幕尺寸

3. WHEN 用户输入无效数据 THEN 系统 SHALL 显示清晰的错误提示信息

4. WHEN 用户操作表单 THEN 系统 SHALL 提供实时的表单验证反馈

5. WHEN 用户生成时间段 THEN 系统 SHALL 显示预览表格展示将要导出的数据